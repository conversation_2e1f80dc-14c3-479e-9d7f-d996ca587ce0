# ChatPanel Thread Integration Guide

## 📋 Tổng quan

ChatPanel chính của app (`src/shared/components/layout/chat-panel/ChatPanel.tsx`) đã được enhance để support thread integration với ThreadsPage thông qua global state management.

## 🏗️ Kiến trúc Integration

### **1. Enhanced ChatPanel Props**

```typescript
interface ChatPanelProps {
  onClose: () => void;
  onKeywordDetected?: (keyword: string) => void;
  isVisible?: boolean;
  
  // Thread integration props
  enableThreadIntegration?: boolean;
  onThreadCreated?: (threadId: string, threadName: string) => void;
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;
  onThreadNameChanged?: (threadId: string, newName: string) => void;
  onThreadDeleted?: (threadId: string) => void;
}
```

### **2. Enhanced ChatHeader**

- ✅ Thread name display trong header
- ✅ Inline editing với Edit3 icon
- ✅ Loading states cho thread switching
- ✅ Responsive design

### **3. Global State Management**

```typescript
// Hook cho ChatPanel
const threadIntegration = useChatPanelThreadIntegration(callbacks);

// Hook cho ThreadsPage
const threadIntegration = useThreadsPageIntegration();
```

## 🔄 Integration Flow

### **Thread Creation Flow**
1. ChatPanel → `createNewThread()` → trigger `onThreadCreated` event
2. Global state → update `currentThreadId` và `currentThreadName`
3. ThreadsPage → receive callback → update threads list

### **Thread Switching Flow**
1. ThreadsPage → `requestThreadSwitch(threadId)` → set `isThreadSwitching: true`
2. ChatPanel → receive callback → `chatStream.switchToThread(threadId)`
3. useChatStream → disconnect SSE → load new thread → trigger `onThreadLoaded`
4. Global state → update thread info → set `isThreadSwitching: false`

### **Thread Name Sync Flow**
1. ChatHeader → inline edit → `onThreadNameChange(threadId, newName)`
2. ChatStream → `updateThreadName()` → API call → trigger `onThreadNameChanged`
3. Global state → update `currentThreadName`
4. ThreadsPage → receive callback → update thread in list

### **Thread Deletion Flow**
1. ThreadsPage → delete thread → `notifyThreadDeleted(threadId)`
2. ChatPanel → receive callback → trigger `onThreadDeleted`
3. Global state → clear current thread if deleted
4. Auto-switch logic có thể được implement ở ThreadsPage level

## 🎯 Cách sử dụng

### **1. Basic Integration**

```typescript
// Trong component cha (e.g., Layout)
const [isChatVisible, setIsChatVisible] = useState(true);

// Thread event handlers
const handleThreadCreated = (threadId: string, threadName: string) => {
  console.log('Thread created:', { threadId, threadName });
  // Update threads list
};

const handleThreadSwitched = (fromThreadId: string, toThreadId: string) => {
  console.log('Thread switched:', { fromThreadId, toThreadId });
  // Update UI state
};

const handleThreadNameChanged = (threadId: string, newName: string) => {
  console.log('Thread name changed:', { threadId, newName });
  // Update thread in list
};

const handleThreadDeleted = (threadId: string) => {
  console.log('Thread deleted:', { threadId });
  // Remove thread from list
};

// Render ChatPanel với integration
<ChatPanel
  onClose={() => setIsChatVisible(false)}
  isVisible={isChatVisible}
  enableThreadIntegration={true}
  onThreadCreated={handleThreadCreated}
  onThreadSwitched={handleThreadSwitched}
  onThreadNameChanged={handleThreadNameChanged}
  onThreadDeleted={handleThreadDeleted}
/>
```

### **2. ThreadsPage Integration**

```typescript
// Trong ThreadsPage component
const threadIntegration = useThreadsPageIntegration();

// Handle thread operations
const handleStartThread = (threadId: string) => {
  threadIntegration.requestThreadSwitch(threadId);
};

const handleEditThreadName = (threadId: string, newName: string) => {
  threadIntegration.requestThreadNameChange(threadId, newName);
};

const handleDeleteThread = (threadId: string) => {
  // Delete from API first
  await deleteThreadAPI(threadId);
  
  // Then notify integration
  threadIntegration.notifyThreadDeleted(threadId);
};

// Access current state
const { currentThreadId, currentThreadName, isThreadSwitching } = threadIntegration;
```

### **3. Complete Example**

Xem `ChatPanelWithThreadsIntegration.tsx` để có example đầy đủ về cách integrate ChatPanel với mock ThreadsPage.

## 🎨 UI Features

### **Thread Name Display**
- Hiển thị trong ChatHeader bên cạnh agent name
- Separated bằng "|" character
- Truncate text nếu quá dài

### **Inline Editing**
- Click vào thread name để edit
- Enter để save, Escape để cancel
- Check/X buttons cho save/cancel
- Auto-focus input khi start editing

### **Loading States**
- Spinning indicator khi `isThreadSwitching: true`
- "Đang chuyển thread..." text
- Animate pulse effect

### **Responsive Design**
- Flex layout với proper min-width
- Truncate long thread names
- Mobile-friendly touch targets

## 🔧 Technical Details

### **Global State Management**
```typescript
// Global state structure
interface ThreadIntegrationState {
  currentThreadId: string | null;
  currentThreadName: string | null;
  isThreadSwitching: boolean;
}

// Callbacks interface
interface ThreadIntegrationCallbacks {
  onThreadCreated?: (threadId: string, threadName: string) => void;
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;
  onThreadNameChanged?: (threadId: string, newName: string) => void;
  onThreadDeleted?: (threadId: string) => void;
}
```

### **Event Flow**
1. **ChatPanel** → triggers events → **Global State**
2. **Global State** → notifies listeners → **ThreadsPage**
3. **ThreadsPage** → requests operations → **Global State**
4. **Global State** → triggers callbacks → **ChatPanel**

### **Error Handling**
- TypeScript strict mode compliance
- Optional props với proper type checking
- Graceful degradation khi integration disabled

## 🚀 Benefits

### **Real-time Synchronization**
- Thread state sync giữa ChatPanel và ThreadsPage
- Immediate UI updates
- No polling required

### **Decoupled Architecture**
- ChatPanel không cần biết về ThreadsPage
- ThreadsPage không cần direct reference đến ChatPanel
- Global state làm bridge

### **Backward Compatibility**
- ChatPanel hoạt động bình thường khi `enableThreadIntegration: false`
- Existing code không bị break
- Progressive enhancement

### **Performance**
- Minimal re-renders với proper state management
- Event-driven updates
- Efficient memory usage

## 📝 Next Steps

1. **Integration với real ThreadsPage**: Replace mock data với real API calls
2. **Error Handling**: Add comprehensive error handling cho network failures
3. **Persistence**: Save thread state to localStorage/sessionStorage
4. **Advanced Features**: Thread search, filtering, sorting
5. **Testing**: Unit tests cho integration hooks và components

## 🎯 Usage trong Production

```typescript
// App.tsx hoặc Layout component
import ChatPanel from '@/shared/components/layout/chat-panel/ChatPanel';
import { useThreadsPageIntegration } from '@/shared/hooks/useChatPanelThreadIntegration';

const App = () => {
  const [showChat, setShowChat] = useState(false);
  
  return (
    <div className="app-layout">
      {/* Other components */}
      
      {showChat && (
        <ChatPanel
          onClose={() => setShowChat(false)}
          enableThreadIntegration={true}
          onThreadCreated={(threadId, threadName) => {
            // Handle thread creation
            console.log('New thread:', { threadId, threadName });
          }}
          onThreadSwitched={(fromId, toId) => {
            // Handle thread switching
            console.log('Thread switched:', { fromId, toId });
          }}
          onThreadNameChanged={(threadId, newName) => {
            // Handle name changes
            console.log('Name changed:', { threadId, newName });
          }}
          onThreadDeleted={(threadId) => {
            // Handle deletion
            console.log('Thread deleted:', threadId);
          }}
        />
      )}
    </div>
  );
};
```

Thread integration đã sẵn sàng để sử dụng trong production! 🎉
