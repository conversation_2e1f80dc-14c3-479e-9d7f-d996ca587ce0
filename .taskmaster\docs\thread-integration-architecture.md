# Thread Integration Architecture Design

## 📋 Phân tích hiện tại

### 1. **useChatStream Hook Analysis**
- **State Management**: <PERSON><PERSON><PERSON><PERSON> lý `threadId`, `threadName`, `messages`, `isLoading`, `isStreaming`
- **Key Methods**:
  - `createNewThread(name?: string)`: Tạo thread mới
  - `switchToThread(threadId: string)`: Chuyển thread (disconnect SSE → load new thread)
  - `getCurrentThreadId()`: Lấy current thread ID
  - `loadSpecificThread(threadId: string)`: Load thread cụ thể
- **SSE Management**: Auto disconnect/reconnect khi switch thread
- **Message History**: Auto load history khi switch thread

### 2. **ChatIntegrationService Analysis**
- **Event System**: `onThreadLoaded`, `onThreadSwitched`, `onThreadDeleted`
- **Chat Panel Integration**: Tích hợp với useChatStream hook
- **Thread Management**: Handle thread deletion với auto-switch logic
- **Current Implementation**: Đ<PERSON> có sẵn integration infrastructure

### 3. **ThreadsPage & ThreadsGrid Analysis**
- **useThreadManagement Hook**: Quản lý thread operations với React Query
- **Event Callbacks**: `onThreadStarted`, `onThreadDeleted` 
- **Chat Integration**: Sử dụng `useChatIntegration` hook
- **Current Flow**: ThreadsPage → useThreadManagement → chatIntegrationService → useChatStream

### 4. **ChatPanel Analysis**
- **Current Props**: `title`, `messages`, `onAddMessage`, `onDeleteMessage`, `onEditMessage`
- **Header Structure**: Fixed title trong header
- **Missing Integration**: Không có threadId/threadName props, không có thread name display

## 🎯 Thiết kế kiến trúc tích hợp

### 1. **Event-Driven Architecture**

```typescript
// Enhanced ChatIntegrationEvents
interface ChatIntegrationEvents {
  onThreadCreated: (threadId: string, threadName: string) => void;
  onThreadLoaded: (threadId: string, threadName: string) => void;
  onThreadSwitched: (fromThreadId: string, toThreadId: string) => void;
  onThreadNameChanged: (threadId: string, newName: string) => void;
  onThreadDeleted: (threadId: string, nextThreadId?: string) => void;
}
```

### 2. **Enhanced useChatStream Hook**

```typescript
interface UseChatStreamReturn {
  // Existing state...
  threadId: string | null;
  threadName: string | null;
  
  // Enhanced methods
  createNewThread: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  switchToThread: (threadId: string) => Promise<void>;
  updateThreadName: (threadId: string, newName: string) => Promise<void>;
  
  // New event emitters
  onThreadCreated?: (threadId: string, threadName: string) => void;
  onThreadNameChanged?: (threadId: string, newName: string) => void;
}
```

### 3. **Enhanced ChatPanel Interface**

```typescript
interface ChatPanelProps {
  // Existing props...
  title: string;
  messages: DatasetMessage[];
  
  // New thread integration props
  threadId?: string | null;
  threadName?: string | null;
  showThreadName?: boolean;
  onThreadNameChange?: (threadId: string, newName: string) => void;
  isThreadSwitching?: boolean;
  
  // Enhanced callbacks
  onAddMessage: (message: DatasetMessage) => void;
  onDeleteMessage: (index: number) => void;
  onEditMessage?: (index: number, message: DatasetMessage) => void;
}
```

### 4. **Integration Flow Design**

#### **Thread Creation Flow:**
1. ChatPanel → useChatStream.createNewThread()
2. useChatStream → API call → set threadId/threadName
3. useChatStream → emit onThreadCreated event
4. chatIntegrationService → listen event → trigger React Query invalidation
5. ThreadsPage → re-fetch threads → update UI

#### **Thread Switching Flow:**
1. ThreadsPage → click start thread → handleThreadStarted()
2. useChatIntegration.loadThreadToChat() → chatIntegrationService.loadThreadToChat()
3. chatIntegrationService → useChatStream.switchToThread()
4. useChatStream → disconnect SSE → load thread → update state
5. ChatPanel → receive new threadId/threadName → update header

#### **Thread Name Sync Flow:**
1. ThreadCard → edit name → API update
2. ThreadCard → emit thread name change event
3. chatIntegrationService → listen event → notify useChatStream
4. useChatStream → update threadName state
5. ChatPanel → re-render with new name

#### **Thread Deletion Flow:**
1. ThreadsPage → delete thread → useThreadManagement.deleteThread()
2. useThreadManagement → API delete → chatIntegrationService.handleThreadDeleted()
3. chatIntegrationService → find next thread → auto-switch
4. useChatStream → switchToThread(nextThreadId)
5. ChatPanel → update with new thread

## 🔧 Implementation Strategy

### Phase 1: Foundation (Tasks 1-3)
- Enhance ChatPanel props và UI
- Extend useChatStream với event system
- Update chatIntegrationService

### Phase 2: Integration (Tasks 4-7)
- Implement thread creation integration
- Implement thread switching
- Implement name synchronization
- Implement deletion handling

### Phase 3: Optimization (Tasks 8-12)
- Performance optimization
- Error handling
- UI/UX enhancements
- Testing

## 📊 Data Flow Diagram

```
ThreadsPage ←→ useChatIntegration ←→ chatIntegrationService ←→ useChatStream ←→ ChatPanel
     ↓                                        ↓                        ↓
useThreadManagement                    Event System              threadId/threadName
     ↓                                        ↓                        ↓
React Query                           Event Listeners           Header Display
```

## ✅ Success Criteria

1. **Real-time Sync**: Thread operations sync instantly between components
2. **Performance**: < 500ms for thread operations
3. **Data Consistency**: Zero inconsistency between ThreadsPage và ChatPanel
4. **User Experience**: Smooth transitions, proper loading states
5. **Error Handling**: Graceful error handling với rollback capability
