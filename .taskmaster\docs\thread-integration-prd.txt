# PRD: <PERSON><PERSON><PERSON> hợ<PERSON> Thread Management giữa ThreadsPage và ChatPanel

## Tổng quan dự án
Tích hợp quản lý thread giữa màn hình ThreadsPage (danh sách threads) và ChatPanel (giao diện chat) để tạo ra một hệ thống chat liền mạch với khả năng chuyển đổi thread, đồng bộ dữ liệu real-time và hiển thị tên thread.

## Mục tiêu chính
1. **Thread Creation Integration**: Khi tạo thread mới ở ChatPanel, cập nhật ngay lập tức danh sách threads ở ThreadsPage
2. **Thread Switching**: Chuyển đổi thread từ ThreadsPage và load history messages vào ChatPanel
3. **Thread Name Sync**: Đồng bộ tên thread giữa hai màn hình khi có thay đổi
4. **Thread Deletion Handling**: Xử lý xóa thread và chuyển sang thread tiếp theo
5. **Thread Name Display**: Hiển thị tên thread hiện tại trong ChatPanel header

## Yêu cầu chức năng chi tiết

### 1. Thread Creation Integration
- Khi ChatPanel tạo thread mới (qua useChatStream.createNewThread)
- ThreadsPage phải cập nhật danh sách threads ngay lập tức
- Sử dụng React Query invalidation để refresh data
- Đảm bảo thread mới xuất hiện ở đầu danh sách

### 2. Thread Switching từ ThreadsPage
- Khi user click "start" thread ở ThreadsPage
- Disconnect SSE connection hiện tại nếu có
- Load thread mới vào ChatPanel qua useChatStream.switchToThread
- Load history messages của thread đó
- Cập nhật threadId và threadName trong ChatPanel

### 3. Thread Name Synchronization
- Khi user edit tên thread ở ThreadsPage (qua ThreadCard)
- ChatPanel phải cập nhật tên thread ngay lập tức
- Sử dụng event system hoặc shared state management
- Đảm bảo tên thread hiển thị đúng trong header ChatPanel

### 4. Thread Deletion Handling
- Khi user xóa thread ở ThreadsPage
- Nếu thread đang được sử dụng trong ChatPanel:
  - Chuyển sang thread tiếp theo trong danh sách
  - Load history messages của thread mới
  - Cập nhật threadId và threadName
- Nếu không có thread nào khác, set threadId = null

### 5. Thread Name Display trong ChatPanel
- Hiển thị tên thread hiện tại ở vị trí header (thay thế title cố định)
- Vị trí: giữa agent selector và add button
- Style: text có thể edit inline hoặc readonly tùy context
- Responsive design cho mobile

## Yêu cầu kỹ thuật

### 1. State Management
- Sử dụng useChatStream hook làm single source of truth cho thread state
- ThreadsPage subscribe vào thread changes qua useChatIntegration
- Implement event-driven architecture cho real-time sync

### 2. API Integration
- Sử dụng existing APIs: threads CRUD, chat streaming
- Implement optimistic updates cho UX tốt hơn
- Handle error cases và rollback khi cần

### 3. Component Architecture
- Modify ChatPanel props để nhận threadId và threadName
- Enhance ThreadsPage với chat integration callbacks
- Create shared hooks cho thread state management

### 4. Performance Considerations
- Debounce thread name updates
- Optimize React Query cache invalidation
- Minimize unnecessary re-renders

## Acceptance Criteria

### Thread Creation
- [ ] Tạo thread mới ở ChatPanel → ThreadsPage cập nhật danh sách ngay lập tức
- [ ] Thread mới xuất hiện ở đầu danh sách với tên đúng
- [ ] Không có duplicate threads trong danh sách

### Thread Switching
- [ ] Click start thread ở ThreadsPage → ChatPanel load thread đó
- [ ] SSE connection cũ bị disconnect trước khi connect mới
- [ ] History messages load đúng và hiển thị trong ChatPanel
- [ ] ThreadId và threadName update đúng

### Thread Name Sync
- [ ] Edit tên thread ở ThreadsPage → ChatPanel header cập nhật ngay
- [ ] Tên thread hiển thị đúng ở cả hai màn hình
- [ ] Validation tên thread hoạt động đúng

### Thread Deletion
- [ ] Xóa thread hiện tại → ChatPanel chuyển sang thread tiếp theo
- [ ] Xóa thread khác → ChatPanel không bị ảnh hưởng
- [ ] Xóa thread cuối cùng → ChatPanel reset về trạng thái no thread

### UI/UX
- [ ] Tên thread hiển thị đúng vị trí trong ChatPanel header
- [ ] Loading states hiển thị khi switching threads
- [ ] Error handling cho các trường hợp lỗi
- [ ] Responsive design hoạt động tốt

## Constraints và Limitations
- Phải tương thích với existing chat streaming system
- Không được break existing functionality của ThreadsPage và ChatPanel
- Phải maintain performance tốt với large thread lists
- Support cả desktop và mobile layouts

## Success Metrics
- Thread operations hoàn thành trong < 500ms
- Zero data inconsistency giữa ThreadsPage và ChatPanel
- 100% test coverage cho thread integration logic
- User experience mượt mà không có glitches
