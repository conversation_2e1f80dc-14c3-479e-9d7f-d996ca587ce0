# 🔥 Hot Module Replacement (HMR) Troubleshooting Guide

## Vấn đề: Code thay đổi nhưng giao diện không cập nhật ngay

### ✅ Checklist khắc phục HMR

#### 1. **Kiểm tra Dev Server**
```bash
# Khởi động dev server
npm run dev

# Kiểm tra server có chạy trên port 5173
Get-NetTCPConnection -LocalPort 5173 -ErrorAction SilentlyContinue
```

#### 2. **Kiểm tra Browser Console**
- Mở DevTools (F12)
- Kiểm tra tab Console có lỗi không
- Kiểm tra tab Network có kết nối WebSocket không

#### 3. **Kiểm tra TypeScript Errors**
```bash
# Kiểm tra lỗi TypeScript
npm run type-check

# Kiểm tra lỗi ESLint
npm run lint
```

#### 4. **Kiểm tra File Extension**
- File phải có extension `.tsx` hoặc `.ts`
- File phải nằm trong thư mục `src/`
- Không có circular dependencies

#### 5. **Kiểm tra Component Export**
```tsx
// ✅ Đúng - Default export cho component
const MyComponent: React.FC = () => {
  return <div>Hello</div>;
};

export default MyComponent;

// ❌ Sai - Named export có thể gây vấn đề HMR
export const MyComponent: React.FC = () => {
  return <div>Hello</div>;
};
```

#### 6. **Kiểm tra Import Statement**
```tsx
// ✅ Đúng
import MyComponent from './MyComponent';

// ❌ Có thể gây vấn đề
import { MyComponent } from './MyComponent';
```

### 🛠️ Các giải pháp khắc phục

#### Giải pháp 1: Restart Dev Server
```bash
# Dừng server (Ctrl+C)
# Khởi động lại
npm run dev
```

#### Giải pháp 2: Clear Cache
```bash
# Xóa node_modules và reinstall
rm -rf node_modules
npm install

# Hoặc clear npm cache
npm cache clean --force
```

#### Giải pháp 3: Kiểm tra Port Conflict
```bash
# Kiểm tra port 5173 và 24679 có bị chiếm không
netstat -an | findstr :5173
netstat -an | findstr :24679
```

#### Giải pháp 4: Browser Hard Refresh
- Ctrl+F5 (Windows)
- Cmd+Shift+R (Mac)
- Hoặc disable cache trong DevTools

#### Giải pháp 5: Kiểm tra File Watcher
```bash
# Trên Windows, có thể cần enable polling
# Thêm vào vite.config.ts:
server: {
  watch: {
    usePolling: true,
    interval: 1000
  }
}
```

### 🧪 Test HMR

#### Sử dụng Test Component
```bash
# Tạo test component
npm run test-hmr

# Xóa test component
npm run test-hmr:clean

# Kiểm tra status
npm run test-hmr:status
```

#### Manual Test
1. Mở một component đang được sử dụng
2. Thay đổi text hoặc style
3. Save file (Ctrl+S)
4. Kiểm tra browser có cập nhật ngay không

### 🚨 Các trường hợp HMR không hoạt động

#### 1. **Lỗi Syntax**
- TypeScript errors
- ESLint errors
- Import/Export errors

#### 2. **Component Structure Issues**
- Circular dependencies
- Dynamic imports
- HOC (Higher Order Components) phức tạp

#### 3. **State Management Issues**
- Redux store changes
- Context provider changes
- Global state mutations

#### 4. **File System Issues**
- File nằm ngoài src/
- Symlinks
- File permissions

### 📋 Cấu hình tối ưu cho HMR

#### vite.config.ts
```typescript
export default defineConfig({
  server: {
    hmr: {
      port: 24679,
      host: 'localhost',
      overlay: true,
      clientPort: 24679,
    },
    watch: {
      usePolling: false,
      interval: 100,
      ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**'],
    },
  },
  plugins: [
    react({
      jsxRuntime: 'automatic',
    }),
    eslint({
      failOnError: false,
      failOnWarning: false,
    }),
  ],
});
```

### 🔍 Debug Commands

```bash
# Kiểm tra HMR status
npm run test-hmr:status

# Kiểm tra TypeScript
npm run type-check

# Kiểm tra ESLint
npm run lint

# Build để kiểm tra lỗi
npm run build:fast
```

### 📞 Khi nào cần hỗ trợ

Nếu sau khi thử tất cả các giải pháp trên mà HMR vẫn không hoạt động:

1. Kiểm tra lại cấu hình Vite
2. Kiểm tra version của các dependencies
3. Tạo issue với thông tin chi tiết về lỗi
4. Cung cấp minimal reproduction case
