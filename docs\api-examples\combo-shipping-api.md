# API Endpoint cho Combo Shipping

## GET /user/products/{productId}/combo-details

Endpoint để lấy thông tin chi tiết các sản phẩm trong combo và kiểm tra có sản phẩm vật lý không.

### Request

```
GET /user/products/123/combo-details
Authorization: Bearer {token}
```

### Response Success

```json
{
  "code": 0,
  "message": "Lấy thông tin combo thành công",
  "result": {
    "comboProductId": 123,
    "items": [
      {
        "productId": 456,
        "productType": "PHYSICAL",
        "quantity": 2,
        "shipmentConfig": {
          "lengthCm": 30,
          "widthCm": 25,
          "heightCm": 5,
          "weightGram": 200
        }
      },
      {
        "productId": 789,
        "productType": "DIGITAL",
        "quantity": 1,
        "shipmentConfig": null
      }
    ],
    "hasPhysicalProducts": true,
    "totalPhysicalItems": 1
  }
}
```

### Response Error

```json
{
  "code": 404,
  "message": "Không tìm thấy sản phẩm combo",
  "result": null
}
```

## Ví dụ sử dụng

### Combo chỉ có sản phẩm số

```json
{
  "code": 0,
  "message": "Lấy thông tin combo thành công",
  "result": {
    "comboProductId": 124,
    "items": [
      {
        "productId": 101,
        "productType": "DIGITAL",
        "quantity": 1,
        "shipmentConfig": null
      },
      {
        "productId": 102,
        "productType": "SERVICE",
        "quantity": 1,
        "shipmentConfig": null
      }
    ],
    "hasPhysicalProducts": false,
    "totalPhysicalItems": 0
  }
}
```

### Combo có cả sản phẩm vật lý và số

```json
{
  "code": 0,
  "message": "Lấy thông tin combo thành công",
  "result": {
    "comboProductId": 125,
    "items": [
      {
        "productId": 201,
        "productType": "PHYSICAL",
        "quantity": 1,
        "shipmentConfig": {
          "lengthCm": 20,
          "widthCm": 15,
          "heightCm": 3,
          "weightGram": 150
        }
      },
      {
        "productId": 202,
        "productType": "PHYSICAL",
        "quantity": 2,
        "shipmentConfig": {
          "lengthCm": 10,
          "widthCm": 10,
          "heightCm": 2,
          "weightGram": 50
        }
      },
      {
        "productId": 203,
        "productType": "DIGITAL",
        "quantity": 1,
        "shipmentConfig": null
      }
    ],
    "hasPhysicalProducts": true,
    "totalPhysicalItems": 2
  }
}
```

## Logic xử lý

1. **Frontend gọi API** khi cần kiểm tra combo có sản phẩm vật lý không
2. **Backend trả về** danh sách chi tiết các sản phẩm trong combo
3. **Frontend sử dụng** thông tin này để:
   - Quyết định có hiển thị form vận chuyển không
   - Tính phí vận chuyển cho các sản phẩm vật lý trong combo
   - Hiển thị thông tin phù hợp cho user

## Fallback Strategy

Nếu API lỗi hoặc không có response:
- **Giả sử combo có sản phẩm vật lý** (để an toàn)
- **Hiển thị form vận chuyển** 
- **Log error** để debug

Điều này đảm bảo user không bị mất chức năng vận chuyển khi có lỗi API.
