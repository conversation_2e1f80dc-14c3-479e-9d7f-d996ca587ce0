# Dataset Thread Integration Guide

Hướng dẫn tích hợp Dataset ChatPanel với ThreadsPage để đồng bộ hóa dữ liệu khi tạo cuộc trò chuyện mới.

## Tổng quan

Khi người dùng tạo cuộc trò chuyện mới trong Dataset ChatPanel, ThreadsPage sẽ tự động refresh để hiển thị thread mới. Điều này được thực hiện thông qua Redux store và React Query cache invalidation.

## Cách hoạt động

1. **Dataset ChatPanel** - <PERSON>hi gửi message đầu tiên, dispatch `datasetConversationCreated` event
2. **Redux Store** - Lưu trữ event trong `threadIntegrationSlice`
3. **ThreadsPage** - Listen to Redux events và invalidate React Query cache
4. **React Query** - Refetch threads list để hiển thị thread mới

## Cách sử dụng

### 1. Enable Thread Integration trong ChatLayout

```typescript
import { ChatLayout } from '@/modules/user-dataset/components';

// Trong component của bạn
<ChatLayout
  onConversationsChange={handleConversationsChange}
  enableThreadIntegration={true}
  datasetId="your-dataset-id" // Optional: để tạo unique thread ID
/>
```

### 2. Hoặc trực tiếp trong ChatPanel

```typescript
import { ChatPanel } from '@/modules/user-dataset/components';

// Trong component của bạn
<ChatPanel
  title="Dataset Chat"
  messages={messages}
  onAddMessage={handleAddMessage}
  onDeleteMessage={handleDeleteMessage}
  onEditMessage={handleEditMessage}
  enableThreadIntegration={true}
  datasetId="your-dataset-id" // Optional
/>
```

## Thread ID Format

- Với datasetId: `dataset-{datasetId}-{timestamp}`
- Không có datasetId: `dataset-conversation-{timestamp}`

## Thread Name Format

- `Dataset: {title}` - Sử dụng title của ChatPanel

## Redux Events

### datasetConversationCreated

```typescript
{
  threadId: string;
  threadName: string;
}
```

Event này được dispatch khi:
- `enableThreadIntegration = true`
- Gửi message đầu tiên (messages.length === 0)

## ThreadsPage Integration

ThreadsPage tự động listen to `datasetConversationCreated` events và:

1. **Force invalidate React Query cache** với `refetchType: 'active'`
2. **Invalidate infinite queries** đặc biệt cho ThreadsGrid
3. **Manual refetch** trong ThreadsGrid để đảm bảo data được load lại
4. **Clear Redux event** sau khi xử lý

### Cơ chế Refetch

1. **ThreadsPage**: Invalidate cache với force refetch
2. **ThreadsGrid**: Listen to Redux events và trigger manual refetch
3. **Double guarantee**: Cả invalidation và manual refetch để đảm bảo data mới

## Ví dụ hoàn chỉnh

```typescript
import React, { useState } from 'react';
import { ChatLayout } from '@/modules/user-dataset/components';
import { ImportedConversation } from '@/modules/user-dataset/user-data-fine-tune/types/user-data-fine-tune.types';

const DatasetPage: React.FC = () => {
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);

  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
    console.log('Conversations updated:', updatedConversations.length);
  };

  return (
    <div className="h-full">
      <ChatLayout
        onConversationsChange={handleConversationsChange}
        enableThreadIntegration={true}
        datasetId="dataset-123" // Your dataset ID
      />
    </div>
  );
};

export default DatasetPage;
```

## Lưu ý

1. **Thread Integration chỉ hoạt động khi gửi message đầu tiên** - Để tránh tạo quá nhiều thread không cần thiết
2. **datasetId là optional** - Nếu không có, sẽ sử dụng timestamp để tạo unique ID
3. **ThreadsPage phải được mount** - Để listen to Redux events
4. **React Query cache** - Sẽ được invalidate và refetch tự động

## Troubleshooting

### Thread không xuất hiện trong ThreadsPage

1. Kiểm tra `enableThreadIntegration={true}`
2. Kiểm tra Redux DevTools để xem event có được dispatch không
3. Kiểm tra ThreadsPage có mount và listen to events không
4. Kiểm tra React Query cache invalidation

### Console logs để debug

```javascript
// Trong Dataset ChatPanel
console.log('[DatasetChatPanel] Creating new dataset conversation thread:', { threadId, threadName });

// Trong ThreadsPage
console.log('[ThreadsPage] New dataset conversation created:', events.datasetConversationCreated);
console.log('[ThreadsPage] Force invalidated and refetched threads queries after dataset conversation creation');
console.log('[ThreadsPage] Also invalidated infinite queries specifically for ThreadsGrid');

// Trong ThreadsGrid
console.log('[ThreadsGrid] Thread creation event detected, triggering manual refetch:', {
  threadCreated: events.threadCreated,
  datasetConversationCreated: events.datasetConversationCreated
});
```

## Cách kiểm tra hoạt động

1. **Mở Developer Tools** và xem Console tab
2. **Mở ThreadsPage** ở một tab/window
3. **Mở Dataset ChatPanel** ở tab/window khác
4. **Enable thread integration** trong ChatPanel
5. **Gửi message đầu tiên** trong dataset conversation
6. **Kiểm tra Console logs** để xem events được dispatch
7. **Kiểm tra ThreadsPage** - sẽ thấy thread mới xuất hiện ngay lập tức
