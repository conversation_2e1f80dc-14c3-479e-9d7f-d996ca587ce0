# Sửa lỗi import useUserAddress hooks

## Vấn đề

<PERSON>hi chạy dev server, gặp lỗi:
```
[plugin:vite:import-analysis] Failed to resolve import "./useUserAddress" from "src/modules/business/hooks/index.ts". Does the file exist?
```

## Nguyên nhân

File `useUserAddress.ts` không tồn tại trong thư mục `src/modules/business/hooks/` nhưng đã được export trong `index.ts`:

```typescript
// src/modules/business/hooks/index.ts
export * from './useUserAddress'; // ❌ File không tồn tại
```

## Giải pháp

### 1. Tạo lại file `useUserAddress.ts`

Tạo file `src/modules/business/hooks/useUserAddress.ts` với nội dung đầy đủ:

```typescript
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getUserAddresses,
  getDefault<PERSON>ser<PERSON>ddress,
  getUserAddressById,
  create<PERSON>ser<PERSON><PERSON><PERSON>,
  update<PERSON>ser<PERSON>ddress,
  deleteUserAddress,
  setDefaultUserAddress,
  CreateUserAddressDto,
  UpdateUserAddressDto,
} from '../services/user-address.api';

// Query keys và các hooks...
```

### 2. Cấu trúc file hoàn chỉnh

File bao gồm:

#### **Query Keys**
```typescript
export const USER_ADDRESS_QUERY_KEYS = {
  all: ['user', 'addresses'] as const,
  lists: () => [...USER_ADDRESS_QUERY_KEYS.all, 'list'] as const,
  list: () => [...USER_ADDRESS_QUERY_KEYS.lists()] as const,
  details: () => [...USER_ADDRESS_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...USER_ADDRESS_QUERY_KEYS.details(), id] as const,
  default: () => [...USER_ADDRESS_QUERY_KEYS.all, 'default'] as const,
};
```

#### **Query Hooks**
- `useUserAddresses()` - Lấy danh sách địa chỉ
- `useDefaultUserAddress()` - Lấy địa chỉ mặc định
- `useUserAddress(id)` - Lấy địa chỉ theo ID

#### **Mutation Hooks**
- `useCreateUserAddress()` - Tạo địa chỉ mới
- `useUpdateUserAddress()` - Cập nhật địa chỉ
- `useDeleteUserAddress()` - Xóa địa chỉ
- `useSetDefaultUserAddress()` - Đặt làm mặc định

### 3. Tính năng của các hooks

#### **useUserAddresses()**
```typescript
export const useUserAddresses = () => {
  return useQuery({
    queryKey: USER_ADDRESS_QUERY_KEYS.list(),
    queryFn: async () => {
      const response = await getUserAddresses();
      return response.result || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
```

#### **useCreateUserAddress()**
```typescript
export const useCreateUserAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserAddressDto) => createUserAddress(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách địa chỉ
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.default() });
    },
  });
};
```

## Kết quả

### ✅ **Đã sửa thành công:**
- File `useUserAddress.ts` đã được tạo lại
- Export trong `index.ts` hoạt động bình thường
- Dev server chạy thành công trên port 5174
- Không còn lỗi import resolution

### 🔧 **Files đã tạo:**
- `src/modules/business/hooks/useUserAddress.ts`

### 📋 **Hooks có sẵn:**

#### **Query Hooks (Lấy dữ liệu)**
- `useUserAddresses()` - Lấy danh sách địa chỉ của user
- `useDefaultUserAddress()` - Lấy địa chỉ mặc định
- `useUserAddress(id)` - Lấy địa chỉ theo ID

#### **Mutation Hooks (Thay đổi dữ liệu)**
- `useCreateUserAddress()` - Tạo địa chỉ mới
- `useUpdateUserAddress()` - Cập nhật địa chỉ
- `useDeleteUserAddress()` - Xóa địa chỉ
- `useSetDefaultUserAddress()` - Đặt làm mặc định

### 🎯 **Tính năng tự động:**
- **Cache management**: Tự động cache dữ liệu 5 phút
- **Auto invalidation**: Tự động refresh cache khi có thay đổi
- **Optimistic updates**: Cập nhật UI ngay lập tức
- **Error handling**: Xử lý lỗi tự động

### 🔄 **Cách sử dụng trong AddressSelector:**

```typescript
import { useUserAddresses, useCreateUserAddress } from '../../hooks/useUserAddress';

const AddressSelector = () => {
  // Lấy danh sách địa chỉ
  const { data: userAddresses = [], isLoading } = useUserAddresses();
  
  // Tạo địa chỉ mới
  const createAddressMutation = useCreateUserAddress();
  
  // Sử dụng trong component...
};
```

## Lưu ý

- Hooks sử dụng TanStack Query để quản lý server state
- Tự động invalidate cache khi có mutations
- Stale time được set 5 phút để tối ưu performance
- Tất cả hooks đều có TypeScript types đầy đủ
- Error handling được xử lý bởi TanStack Query
