# Hot Reload Debug Guide

## Vấn đề: Giao diện không thay đổi ngay khi sửa code

### 1. Kiểm tra cấu hình Vite

Đảm bảo `vite.config.ts` có cấu hình đúng:

```typescript
export default defineConfig({
  server: {
    hmr: {
      overlay: true, // Hiển thị lỗi trên overlay
      clientPort: 24679, // Port HMR
    },
    watch: {
      usePolling: false, // Tắt polling để cải thiện hiệu suất
      interval: 100, // Giảm interval xuống 100ms
    },
  },
  plugins: [
    react({
      jsxRuntime: 'automatic',
      // Fast Refresh enabled by default
    }),
  ],
});
```

### 2. Các bước debug

#### Bước 1: Kiểm tra console browser
- Mở DevTools (F12)
- Kiểm tra tab Console có lỗi không
- Kiểm tra tab Network có request HMR không

#### Bước 2: <PERSON><PERSON><PERSON> tra terminal
- Xem có lỗi TypeScript/ESLint không
- <PERSON><PERSON><PERSON> tra HMR update messages

#### Bước 3: Restart development server
```bash
# Dừng server (Ctrl+C)
# Xóa cache
rm -rf node_modules/.vite
rm -rf dist

# Restart
npm run dev
```

#### Bước 4: Kiểm tra file đang edit
- Đảm bảo file nằm trong `src/` directory
- Đảm bảo file có extension `.tsx` hoặc `.ts`
- Đảm bảo không có syntax error

### 3. Các nguyên nhân thường gặp

#### A. TypeScript errors
- Lỗi TypeScript có thể block HMR
- Chạy `npm run type-check` để kiểm tra

#### B. ESLint errors
- Lỗi ESLint nghiêm trọng có thể block HMR
- Chạy `npm run lint` để kiểm tra

#### C. Import/Export issues
- Import sai path
- Circular dependencies
- Missing exports

#### D. Browser cache
- Hard refresh (Ctrl+Shift+R)
- Disable cache trong DevTools

### 4. Solutions

#### Solution 1: Tắt tạm thời type checking
```typescript
// vite.config.ts
// Comment out checker plugin
// checker({ typescript: ... })
```

#### Solution 2: Sử dụng polling (nếu cần)
```typescript
// vite.config.ts
server: {
  watch: {
    usePolling: true,
    interval: 1000,
  },
}
```

#### Solution 3: Restart với clean cache
```bash
npm run dev -- --force
```

### 5. Test Hot Reload

Tạo file test đơn giản:

```tsx
// src/test-hot-reload.tsx
import React, { useState } from 'react';

const TestHotReload: React.FC = () => {
  const [count, setCount] = useState(0);

  return (
    <div>
      <h1>Test Hot Reload - Version 1</h1>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        Increment
      </button>
    </div>
  );
};

export default TestHotReload;
```

Thay đổi "Version 1" thành "Version 2" và kiểm tra xem có update ngay không.

### 6. Monitoring HMR

Thêm vào component để monitor HMR:

```tsx
// Thêm vào component
React.useEffect(() => {
  console.log('Component mounted/updated:', new Date().toISOString());
}, []);
```

### 7. Cấu hình tối ưu cho development

```typescript
// vite.config.ts - Cấu hình tối ưu
export default defineConfig({
  server: {
    hmr: true,
    watch: {
      usePolling: false,
      interval: 100,
    },
  },
  esbuild: {
    logOverride: { 'this-is-undefined-in-esm': 'silent' }
  },
  optimizeDeps: {
    include: ['react', 'react-dom'],
  },
});
```
