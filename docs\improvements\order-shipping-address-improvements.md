# Cải thiện API tính phí vận chuyển và UX địa chỉ giao hàng

## Vấn đề đã khắc phục

### 1. Chưa lấy được địa chỉ giao hàng
**Trước:** AddressSelector chưa được triển khai đầy đủ, không thể lấy được địa chỉ giao hàng để tính phí.

**Sau:** 
- AddressSelector đã được cải thiện với logic xử lý địa chỉ mới hoàn chỉnh
- Tự động tạo DeliveryAddressDto khi người dùng nhập đủ thông tin
- Tích hợp với MultiCarrierShippingCalculator để tính phí tự động

### 2. Hiển thị translation keys thay vì text
**Trước:** Các button hiển thị `order.useExistingAddress` thay vì "Sử dụng địa chỉ có sẵn"

**Sau:**
- Thêm đầy đủ translation keys vào `src/modules/business/locales/vi.json`:
  - `useExistingAddress`: "Sử dụng địa chỉ có sẵn"
  - `addNewAddress`: "Thêm địa chỉ mới"
  - `recipientName`: "Tên người nhận"
  - `recipientPhone`: "Số điện thoại người nhận"
  - `address`: "Địa chỉ"
  - `province`: "Tỉnh/Thành phố"
  - `district`: "Quận/Huyện"
  - `ward`: "Phường/Xã"
  - `shippingCalculation`: "Tính phí vận chuyển"
  - `manualShippingNote`: "Bạn có thể nhập thông tin vận chuyển thủ công hoặc sử dụng tính năng tự động ở trên"

### 3. UX kém - Layout khó nhìn
**Trước:** Địa chỉ giao hàng nằm trên phần thông tin vận chuyển, gây khó nhìn và khó sử dụng.

**Sau:** Cải thiện layout với 3 phần riêng biệt:

#### Phần 1: Địa chỉ giao hàng (Card riêng)
- Icon map-pin màu xanh dương
- Tiêu đề rõ ràng "Địa chỉ giao hàng"
- Form nhập địa chỉ với validation

#### Phần 2: Tính phí vận chuyển tự động (Card riêng)
- Icon calculator màu xanh lá
- Tiêu đề "Tính phí vận chuyển"
- Bảng so sánh phí từ nhiều nhà vận chuyển
- Tự động chọn carrier rẻ nhất

#### Phần 3: Thông tin vận chuyển thủ công (Card riêng)
- Icon truck màu cam
- Tiêu đề "Thông tin vận chuyển"
- Form nhập thủ công (fallback)
- Ghi chú hướng dẫn sử dụng

## Files đã thay đổi

### 1. `src/modules/business/locales/vi.json`
- Thêm các translation keys cho địa chỉ giao hàng
- Thêm placeholders cho form inputs
- Thêm messages hướng dẫn

### 2. `src/modules/business/components/order/PhysicalShippingForm.tsx`
- Cải thiện layout với 3 cards riêng biệt
- Thêm icons và màu sắc phân biệt
- Tách biệt logic địa chỉ và tính phí
- Loại bỏ import Divider không sử dụng

### 3. `src/modules/business/components/shipping/AddressSelector.tsx`
- Đã có sẵn logic xử lý địa chỉ tốt
- Sử dụng translation keys đúng cách
- Tự động tạo DeliveryAddressDto

## Kết quả

### Trước khi cải thiện:
- Không lấy được địa chỉ giao hàng
- Hiển thị translation keys thay vì text
- Layout khó nhìn, khó sử dụng

### Sau khi cải thiện:
- ✅ Lấy được địa chỉ giao hàng đầy đủ
- ✅ Hiển thị text tiếng Việt chính xác
- ✅ Layout rõ ràng, dễ sử dụng với 3 phần riêng biệt
- ✅ UX tốt hơn với icons và màu sắc phân biệt
- ✅ Tính phí vận chuyển tự động hoạt động

## Luồng hoạt động mới

1. **Người dùng nhập địa chỉ giao hàng** (Card 1)
   - Chọn "Thêm địa chỉ mới"
   - Nhập thông tin người nhận, địa chỉ, tỉnh/huyện/xã
   - Hệ thống tự động tạo DeliveryAddressDto

2. **Hệ thống tính phí tự động** (Card 2)
   - Gọi API calculate-shipping-fee cho GHN và GHTK
   - Hiển thị bảng so sánh phí
   - Tự động chọn carrier rẻ nhất
   - Cập nhật shipping data

3. **Fallback thủ công** (Card 3)
   - Cho phép nhập thông tin vận chuyển thủ công
   - Ghi chú hướng dẫn sử dụng tính năng tự động

## Testing

Để test các cải thiện:

1. Vào trang tạo đơn hàng
2. Chọn sản phẩm và khách hàng
3. Ở bước "Cấu hình giao hàng":
   - Kiểm tra hiển thị 3 cards riêng biệt
   - Nhập địa chỉ giao hàng mới
   - Xem tính phí tự động hoạt động
   - Kiểm tra text hiển thị đúng tiếng Việt

## Lưu ý

- Các lỗi ESLint còn lại không liên quan đến thay đổi này
- Cần test trên môi trường thực với API backend
- Có thể cần điều chỉnh thêm styling cho responsive
