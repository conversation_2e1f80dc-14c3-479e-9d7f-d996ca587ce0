# Kế hoạch sửa lỗi tính phí vận chuyển tự động

## Vấn đề hiện tại

Flow tạo đơn hàng Step 3 "Cấu hình giao hàng" đã được thiết kế đúng nhưng phí vận chuyển không được tính tự động sau khi chọn địa chỉ giao hàng và nhà vận chuyển.

## Phân tích nguyên nhân

1. **Code flow đã đúng**: 
   - `PhysicalShippingForm` → `AddressSelector` + `PreferredCarrierSelector` → `SingleCarrierCalculator`
   - `SingleCarrierCalculator` có `useEffect` tự động gọi API khi có thay đổi

2. **Vấn đề có thể là**:
   - API endpoint `/user/orders/debug-provider-config/${carrier}` chưa hoạt động
   - Lỗi trong việc xử lý response
   - Thiếu fallback khi API debug fail

## Giải pháp

### 1. Thêm fallback API
- Khi API debug-provider-config fail, fallback về API calculate-shipping-fee cũ
- Thêm error handling tốt hơn

### 2. Cải thiện logging và debugging
- Thêm console.log chi tiết hơn
- Hiển thị error message rõ ràng cho user

### 3. Thêm validation
- Validate input trước khi gọi API
- Kiểm tra shopId, deliveryAddress, preferredCarrier

### 4. Cải thiện UX
- Hiển thị loading state rõ ràng
- Thêm button "Tính lại" khi có lỗi
- Hiển thị thông báo lỗi user-friendly

## Các file cần sửa

1. **src/modules/business/services/shipping-calculator.service.ts**
   - Thêm fallback logic
   - Cải thiện error handling

2. **src/modules/business/components/shipping/SingleCarrierCalculator.tsx**
   - Thêm fallback khi API debug fail
   - Cải thiện error display

3. **src/modules/business/components/order/PhysicalShippingForm.tsx**
   - Thêm validation trước khi render SingleCarrierCalculator
   - Cải thiện error handling

## Triển khai

### Bước 1: Sửa ShippingCalculatorService
- Thêm method fallback
- Cải thiện error handling

### Bước 2: Sửa SingleCarrierCalculator
- Implement fallback logic
- Cải thiện UI/UX

### Bước 3: Test và verify
- Test với các trường hợp khác nhau
- Verify API calls trong Network tab

## Kết quả mong đợi

Sau khi sửa:
1. Phí vận chuyển được tính tự động khi user chọn địa chỉ và nhà vận chuyển
2. Có fallback khi API debug fail
3. Error handling tốt hơn
4. UX mượt mà hơn
