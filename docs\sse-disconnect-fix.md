# SSE Disconnect Fix - Cancel Request Implementation

## Vấn đề

Khi người dùng nhấn nút Cancel trong chat interface, hệ thống gặp các vấn đề:

1. **SSE connection vẫn mở** và tiếp tục nhận events
2. **Events vẫn được process** sau khi cancel, tạo ra multiple messages
3. **Role switching logic** tạo message mới khi role thay đổi (worker → supervisor)
4. **Timeouts vẫn chạy** và có thể trigger actions sau khi cancel
5. **Memory leaks** từ SSE connections và timeouts
6. **UI state không được reset** đúng cách

## Nguyên nhân

Trong function `stopStreaming` của `useChatStream.ts`, có điều kiện check:

```typescript
if (!currentRunId || !apiServiceRef.current) {
  return; // ❌ Return sớm, không disconnect SSE
}
```

Điều này khiến SSE không được disconnect khi:
- <PERSON>hông có `currentRunId` 
- Hoặc `apiServiceRef.current` chưa được khởi tạo

## Giải pháp

### 1. **Thêm flag `isDisconnecting` trong SSE Service**

```typescript
private isDisconnecting: boolean = false; // Flag để prevent event processing khi đang disconnect
```

### 2. **Sửa logic disconnect trong SSE Service**

```typescript
disconnect(): void {
  // Set disconnecting flag FIRST để prevent further event processing
  this.isDisconnecting = true;

  // Clear timeout timer
  this.clearTimeoutTimer();

  if (this.eventSource) {
    // Store reference before clearing
    const eventSourceToClose = this.eventSource;

    // Clear references FIRST để prevent further event processing
    this.eventSource = null;
    this.isConnected = false;
    this.isDisconnecting = false; // Reset flag sau khi disconnect xong
    this.currentThreadId = null;
    this.currentRunId = null;

    // Close EventSource after clearing references
    eventSourceToClose.close();

    // Notify callbacks
    this.callbacks.onClose?.();
  }
}
```

### 3. **Thêm checks trong event handlers**

```typescript
// Generic message handler
this.eventSource.onmessage = (event) => {
  // Check if we're still connected and not disconnecting before processing
  if (!this.isConnected || !this.eventSource || this.isDisconnecting) {
    console.warn('[SSE] ⚠️ Received event after disconnect/during disconnection, ignoring:', {
      data: event.data,
      isConnected: this.isConnected,
      hasEventSource: !!this.eventSource,
      isDisconnecting: this.isDisconnecting
    });
    return;
  }
  // ... process event
};

// Text token handler
private handleTextTokenEvent(event: MessageEvent): void {
  if (!this.isConnected || !this.eventSource || this.isDisconnecting) {
    console.warn('[SSE] ⚠️ Received text token after disconnect/during disconnection, ignoring');
    return;
  }
  // ... process token
}
```

### 4. **Thêm checks trong event callbacks của useChatStream**

```typescript
onTextToken: (text: string, role: string) => {
  // ✅ CRITICAL CHECK: Ignore tokens if not in streaming/loading state
  if (!isStreaming && !isLoading) {
    console.warn('[useChatStream] ⚠️ Received text token but not in streaming/loading state, ignoring:', {
      text: text.substring(0, 50) + (text.length > 50 ? '...' : ''),
      role,
      isStreaming,
      isLoading,
      isConnected
    });
    return;
  }
  // ... process token
},

onLLMStreamEnd: (role: string) => {
  // ✅ Check if we're still in valid streaming state
  if (!isStreaming && !isLoading) {
    console.warn('[useChatStream] ⚠️ Received LLM stream end but not in streaming/loading state, ignoring');
    return;
  }
  // ... process stream end
},

onMessageCreated: (messageId: string, threadId: string, role: string, contentPreview: string) => {
  // ✅ Check if we're still in valid streaming state
  if (!isStreaming && !isLoading) {
    console.warn('[useChatStream] ⚠️ Received message created but not in streaming/loading state, ignoring');
    return;
  }
  // ... process message created
}
```

### 5. **Sửa logic trong `stopStreaming`**

```typescript
const stopStreaming = useCallback(async () => {
  try {
    console.log('[useChatStream] 🛑 STOPPING STREAMING/LOADING...', {
      currentRunId,
      isStreaming,
      isLoading,
      isConnected
    });

    // 1. ✅ Reset state IMMEDIATELY để prevent further event processing
    console.log('[useChatStream] 🛑 Resetting state immediately...');
    setIsStreaming(false);
    setIsLoading(false);
    setIsThinking(false);
    setCurrentRunId(null);
    currentStreamingMessageRef.current = null;

    // 1.5. ✅ Clear any pending timeouts
    if (historyTimeoutRef.current) {
      clearTimeout(historyTimeoutRef.current);
      historyTimeoutRef.current = null;
      console.log('[useChatStream] 🛑 Cleared history timeout');
    }

    // 2. ✅ Disconnect SSE (luôn thực hiện)
    if (sseServiceRef.current && isConnected) {
      console.log('[useChatStream] 🛑 Disconnecting SSE connection...');
      sseServiceRef.current.disconnect();
      setIsConnected(false);
    }

    // 3. ✅ Call API để dừng run (chỉ khi có currentRunId và apiService)
    if (currentRunId && apiServiceRef.current) {
      console.log('[useChatStream] 🛑 Stopping current run via API:', currentRunId);
      await apiServiceRef.current.stopRun(currentRunId);
    } else {
      console.log('[useChatStream] 🛑 No currentRunId or apiService, skipping API call');
    }

    console.log('[useChatStream] ✅ Stop streaming completed successfully');

  } catch (error: unknown) {
    console.error('[useChatStream] ❌ Failed to stop streaming:', error);

    // ✅ Vẫn reset state ngay cả khi có lỗi
    setIsStreaming(false);
    setIsLoading(false);
    setIsThinking(false);
    setCurrentRunId(null);
    setIsConnected(false);
    currentStreamingMessageRef.current = null;
  }
}, [currentRunId, isStreaming, isLoading, isConnected]);
```

### 2. **Sửa logic cancel trong `sendMessage`**

```typescript
// Hủy streaming/loading hiện tại nếu có
if (isStreaming || isLoading) {
  console.log('[useChatStream] Cancelling current operation before sending new message');
  try {
    // ✅ Sử dụng stopStreaming function trực tiếp để đảm bảo SSE được disconnect
    await stopStreaming();
  } catch (error) {
    console.warn('[useChatStream] Failed to stop current operation:', error);
  }
}
```

### 3. **Thêm assignment cho `stopStreamingRef`**

```typescript
// Assign stopStreaming to ref để có thể gọi từ sendMessage
useEffect(() => {
  stopStreamingRef.current = stopStreaming;
}, [stopStreaming]);
```

## Thứ tự thực hiện khi Cancel

### ✅ **Thứ tự mới (đã sửa):**

1. **Disconnect SSE** - Luôn thực hiện đầu tiên
2. **Call API stop run** - Chỉ khi có currentRunId và apiService
3. **Reset UI state** - Luôn thực hiện cuối cùng
4. **Error handling** - Vẫn reset state ngay cả khi có lỗi

### ❌ **Thứ tự cũ (có vấn đề):**

1. **Check conditions** - Return sớm nếu không có currentRunId
2. **Call API stop run** - Chỉ khi pass được check
3. **Disconnect SSE** - Chỉ khi không có lỗi
4. **Reset state** - Chỉ khi không có lỗi

## Kết quả

### ✅ **Sau khi sửa:**

- SSE connection **luôn được disconnect** khi cancel
- UI state **luôn được reset** đúng cách
- Không có memory leaks từ SSE connections
- Streaming dừng ngay lập tức khi cancel
- Error handling tốt hơn với fallback state reset

### 🔍 **Cách kiểm tra:**

1. **Mở Developer Tools** → Network tab
2. **Gửi message** để bắt đầu streaming
3. **Nhấn Cancel** trong khi đang stream
4. **Kiểm tra Network tab** - SSE connection phải được closed
5. **Kiểm tra Console logs** - phải thấy "Disconnecting SSE connection"

## Console Logs để Debug

```javascript
// Khi cancel được trigger
console.log('[useChatStream] Stopping streaming/loading...', {
  currentRunId,
  isStreaming,
  isLoading,
  isConnected
});

// Khi disconnect SSE
console.log('[useChatStream] Disconnecting SSE connection...');

// Khi skip API call
console.log('[useChatStream] No currentRunId or apiService, skipping API call');

// Khi reset state
console.log('[useChatStream] Resetting streaming state...');
```

## Files đã sửa

- `src/shared/hooks/common/useChatStream.ts`
  - Function `stopStreaming` - Sửa logic disconnect SSE
  - Function `sendMessage` - Sửa logic cancel operation
  - Thêm `useEffect` để assign `stopStreamingRef.current`

Giải pháp này đảm bảo SSE connection **luôn được disconnect** khi cancel, bất kể trạng thái của currentRunId hay apiService.
