import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Input, IconCard, Card } from '@/shared/components/common';
import { formatDate } from '@/shared/utils/format';

// Hàm formatBytes với xử lý null/NaN
const formatBytes = (bytes: unknown, decimals = 2): string => {
  // Xử lý trường hợp null, undefined, NaN
  if (bytes === null || bytes === undefined || Number.isNaN(Number(bytes))) {
    return 'N/A';
  }

  const numBytes = Number(bytes);

  // X<PERSON> lý trường hợp số âm hoặc không hợp lệ
  if (numBytes < 0 || !Number.isFinite(numBytes)) {
    return 'N/A';
  }

  if (numBytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(numBytes) / Math.log(k));

  return parseFloat((numBytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

interface VectorStoreFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isLoading?: boolean;
  readOnly?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  initialValues?: any; // Sử dụng any để tránh lỗi type
}

/**
 * Form tạo vector store
 */
const VectorStoreForm: React.FC<VectorStoreFormProps> = ({
  onSubmit,
  onCancel,
  isLoading,
  readOnly,
  initialValues,
}) => {
  const { t } = useTranslation();
  const [name, setName] = useState((initialValues?.name as string) || '');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      setError(t('admin.data.vectorStore.nameRequired', 'Vector store name is required'));
      return;
    }

    onSubmit({ name });
  };

  return (
    <Card>
      <form onSubmit={handleSubmit} className="space-y-4">
        <Typography variant="h5" className="mb-4 text-foreground">
          {readOnly
            ? t('data:vectorStore.viewVectorStore', 'View Vector Store')
            : t('data:vectorStore.createVectorStore', 'Create New Vector Store')}
        </Typography>

        {!readOnly ? (
          <div className="space-y-4">
            <div className={`${error ? 'text-red-500' : ''}`}>
              <Input
                value={name}
                onChange={e => {
                  setName(e.target.value);
                  setError('');
                }}
                placeholder={t('data:vectorStore.namePlaceholder', 'Enter vector store name')}
                disabled={readOnly || isLoading}
                className="w-full"
              />
              {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
            </div>
          </div>
        ) : (
          <div className="space-y-4 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300"
                >
                  {t('data:vectorStore.name', 'Vector Store Name')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {initialValues?.storeName as string}
                </Typography>
              </div>
              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300"
                >
                  {t('data:vectorStore.size', 'Size')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {formatBytes(initialValues?.size)}
                </Typography>
              </div>
              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300"
                >
                  {t('data:vectorStore.files', 'Number of files')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {(initialValues?.files as number) || 0}
                </Typography>
              </div>
              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300"
                >
                  {t('data:vectorStore.agents', 'Number of agents using')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {(initialValues?.agents as number) || 0}
                </Typography>
              </div>
              <div>
                <Typography
                  variant="body2"
                  className="font-semibold text-gray-600 dark:text-gray-300"
                >
                  {t('data:vectorStore.createdAt', 'Created at')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {formatDate(initialValues?.createdAt as number)}
                </Typography>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <IconCard
            icon="x"
            title={t('common.cancel', 'Cancel')}
            onClick={onCancel}
            disabled={!!isLoading}
            variant="default"
          />
          {!readOnly && (
            <IconCard
              icon="plus"
              title={
                isLoading
                  ? t('data:common.creating', 'Creating...')
                  : t('data:common.create', 'Create')
              }
              onClick={() => {
                const form = document.querySelector('form');
                if (form) {
                  form.requestSubmit();
                }
              }}
              disabled={!!isLoading}
              variant="primary"
              isLoading={!!isLoading}
            />
          )}
        </div>
      </form>
    </Card>
  );
};

export default VectorStoreForm;
