import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
  DateTimePicker,
} from '@/shared/components/common';
import { Controller } from 'react-hook-form';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import { NotificationUtil } from '@/shared/utils/notification';
import { UpdateProductDto, PriceTypeEnum, HasPriceDto, StringPriceDto } from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useProduct, useUpdateProduct } from '../../hooks/useProductQuery';

interface ServiceProductEditFormProps {
  productId: number;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Extended FileWithMetadata interface
interface ExtendedFileWithMetadata extends FileWithMetadata {
  url?: string;
  name?: string;
  size?: number;
  type?: string;
  metadata?: {
    key?: string;
    url?: string;
    isExisting?: boolean;
  };
}

interface ServiceProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: ExtendedFileWithMetadata[];
  // Service product specific fields
  serviceTime?: Date;
  serviceDuration?: string | number;
  serviceProvider?: string;
  serviceType?: 'CONSULTATION' | 'BEAUTY' | 'MAINTENANCE' | 'INSTALLATION';
  serviceLocation?: 'AT_HOME' | 'AT_CENTER' | 'ONLINE';
}

const ServiceProductEditForm: React.FC<ServiceProductEditFormProps> = ({
  productId,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Gọi API lấy chi tiết sản phẩm
  const { data: product, isLoading: isLoadingProduct, error: productError } = useProduct(productId);

  // Hook để cập nhật sản phẩm
  const updateProductMutation = useUpdateProduct();

  // Schema validation cho dịch vụ
  const serviceProductSchema = z
    .object({
      name: z.string().min(1, 'Tên dịch vụ không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      // Service product specific validations
      serviceTime: z.date().optional(),
      serviceDuration: z.union([z.string(), z.number()]).optional().transform((val) => {
        if (val === undefined || val === null || val === '') return undefined;
        const num = Number(val);
        if (isNaN(num) || num < 1) {
          throw new Error('Thời lượng dịch vụ phải lớn hơn 0');
        }
        return num;
      }),
      serviceProvider: z.string().optional(),
      serviceType: z.enum(['CONSULTATION', 'BEAUTY', 'MAINTENANCE', 'INSTALLATION']).optional(),
      serviceLocation: z.enum(['AT_HOME', 'AT_CENTER', 'ONLINE']).optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra giá phù hợp với loại giá
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        if (!data.listPrice || data.listPrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá niêm yết',
            path: ['listPrice'],
          });
        }
        if (!data.salePrice || data.salePrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá bán',
            path: ['salePrice'],
          });
        }
        if (!data.currency || data.currency.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng chọn đơn vị tiền tệ',
            path: ['currency'],
          });
        }
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!data.priceDescription || !data.priceDescription.trim()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập mô tả giá',
            path: ['priceDescription'],
          });
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<ExtendedFileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State để track ảnh bị xóa
  const [deletedImages, setDeletedImages] = useState<Array<{ key: string; url: string }>>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Khởi tạo giá trị mặc định từ product data
  const defaultValues = useMemo(() => {
    if (!product) return {
      name: '',
      typePrice: PriceTypeEnum.HAS_PRICE,
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      priceDescription: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      // Service product defaults
      serviceTime: undefined,
      serviceDuration: '60', // 60 phút mặc định
      serviceProvider: '',
      serviceType: 'CONSULTATION' as const,
      serviceLocation: 'AT_CENTER' as const,
    };

    const price = product.price as HasPriceDto | StringPriceDto;
    const hasPrice = product.typePrice === PriceTypeEnum.HAS_PRICE;
    const stringPrice = product.typePrice === PriceTypeEnum.STRING_PRICE;

    // Extract service-specific data from multiple sources
    // Cast product để truy cập advancedInfo và các trường service
    const productData = product as any;
    const serviceConfig = productData?.advancedInfo?.servicePackages?.[0];
    const metadata = productData?.metadata;

    const values = {
      name: product.name || '',
      typePrice: product.typePrice || PriceTypeEnum.HAS_PRICE,
      listPrice: hasPrice ? (price as HasPriceDto).listPrice?.toString() || '' : '',
      salePrice: hasPrice ? (price as HasPriceDto).salePrice?.toString() || '' : '',
      currency: hasPrice ? (price as HasPriceDto).currency || 'VND' : 'VND',
      priceDescription: stringPrice ? (price as StringPriceDto).priceDescription || '' : '',
      description: product.description || '',
      tags: product.tags || [],
      customFields: [],
      media: [],
      // Service product defaults từ API response - ưu tiên root level, fallback metadata, cuối cùng serviceConfig
      serviceTime: productData?.serviceTime
        ? new Date(Number(productData.serviceTime))
        : metadata?.serviceTime
          ? new Date(Number(metadata.serviceTime))
          : serviceConfig?.startTime
            ? new Date(serviceConfig.startTime)
            : undefined,
      serviceDuration: productData?.serviceDuration
        ? String(productData.serviceDuration)
        : metadata?.serviceDuration
          ? String(metadata.serviceDuration)
          : serviceConfig?.duration
            ? String(serviceConfig.duration)
            : '60',
      serviceProvider: serviceConfig?.provider || '',
      serviceType: (productData?.serviceType
        || metadata?.serviceType
        || serviceConfig?.serviceType
        || 'CONSULTATION') as 'CONSULTATION' | 'BEAUTY' | 'MAINTENANCE' | 'INSTALLATION',
      serviceLocation: (productData?.serviceLocation
        || metadata?.serviceLocation
        || serviceConfig?.serviceLocation
        || 'AT_CENTER') as 'AT_HOME' | 'AT_CENTER' | 'ONLINE',
    };
    return values;
  }, [product]);

  // Sync tags với state khi product thay đổi
  useEffect(() => {
    if (product?.tags) {
      setTempTags(product.tags);
    }
  }, [product?.tags]);

  // Sync existing images với state khi product thay đổi
  useEffect(() => {
    if (product?.images && product.images.length > 0) {
      // Convert existing images to ExtendedFileWithMetadata format for display
      const existingImages: ExtendedFileWithMetadata[] = product.images.map((img, index) => {
        // Extract file name from URL or use a default name
        const urlParts = img.url.split('/');
        const fileName = urlParts[urlParts.length - 1]?.split('?')[0] || `image-${index + 1}`;

        return {
          id: `existing-${img.key}`,
          file: new File([], fileName, { type: 'image/jpeg' }), // Placeholder file with proper name
          preview: img.url, // This is the key - set preview to the image URL for display
          // Thêm metadata để track key và url cho việc xóa
          metadata: {
            key: img.key,
            url: img.url,
            isExisting: true,
          },
        };
      });

      // Chỉ set lại mediaFiles nếu chưa có ảnh nào hoặc số lượng ảnh khác nhau
      // Điều này tránh ghi đè ảnh mới đã được thêm vào
      setMediaFiles(prev => {

        // Nếu chưa có ảnh existing nào, set toàn bộ
        const currentExistingCount = prev.filter(file => file.id.startsWith('existing-')).length;

        if (currentExistingCount === 0) {
          return existingImages;
        }

        // Nếu số lượng ảnh từ API khác với số ảnh existing hiện tại, cập nhật
        if (existingImages.length !== currentExistingCount) {
          // Giữ lại ảnh mới (không phải existing) và thêm ảnh existing mới
          const newFiles = prev.filter(file => !file.id.startsWith('existing-'));
          const result = [...existingImages, ...newFiles];
          return result;
        }

        // Nếu số lượng giống nhau, chỉ cập nhật URL cho ảnh existing (có thể URL đã thay đổi)
        const result = prev.map(file => {
          if (file.id.startsWith('existing-')) {
            const matchingImage = existingImages.find(img => img.id === file.id);
            if (matchingImage) {
              return {
                ...file,
                preview: matchingImage.preview, // Cập nhật URL mới
                metadata: matchingImage.metadata || file.metadata || {}, // Cập nhật metadata mới
              } as ExtendedFileWithMetadata;
            }
          }
          return file;
        });
        return result;
      });
    } else {
      // Nếu không có ảnh từ API, chỉ giữ lại ảnh mới
      setMediaFiles(prev => prev.filter(file => !file.id.startsWith('existing-')));
    }
  }, [product?.images]);

  // Sync existing custom fields với state khi product thay đổi
  useEffect(() => {
    if (product?.metadata?.customFields && product.metadata.customFields.length > 0) {
      const existingCustomFields: SelectedCustomField[] = product.metadata.customFields.map((field) => ({
        id: field.id,
        fieldId: field.id, // Sử dụng id làm fieldId
        label: field.label,
        component: field.type,
        type: field.type,
        required: field.required,
        configJson: field.configJson,
        value: field.value,
      }));
      setProductCustomFields(existingCustomFields);
    }
  }, [product?.metadata?.customFields]);

  // Handler để xử lý khi ảnh bị xóa
  const handleMediaFilesChange = useCallback((files: ExtendedFileWithMetadata[]) => {
    // Tìm những ảnh existing đã bị xóa
    const currentExistingFiles = mediaFiles.filter(file => file.id.startsWith('existing-'));
    const newExistingFiles = files.filter(file => file.id.startsWith('existing-'));

    const removedFiles = currentExistingFiles.filter(
      currentFile => !newExistingFiles.find(newFile => newFile.id === currentFile.id)
    );

    // Thêm vào danh sách ảnh bị xóa
    if (removedFiles.length > 0) {
      const removedImageInfo = removedFiles
        .filter(file => file.metadata?.key && file.metadata?.url)
        .map(file => ({
          key: file.metadata!.key as string,
          url: file.metadata!.url as string,
        }));

      setDeletedImages(prev => [...prev, ...removedImageInfo]);
    }

    setMediaFiles(files);
  }, [mediaFiles]);

  // Xử lý submit form
  const handleSubmit = async (values: FieldValues) => {
    // Test đơn giản trước
    if (!values['name']) {
      NotificationUtil.error({
        message: 'Vui lòng nhập tên dịch vụ',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as ServiceProductFormValues;
      setIsUploading(true);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Chỉ gửi những trường cần thiết cho sản phẩm dịch vụ
      const productData: UpdateProductDto = {
        name: formValues.name,
        typePrice: formValues.typePrice,
        price: priceData,
      };

      // Chỉ thêm các thuộc tính optional khi có giá trị
      if (formValues.description && formValues.description.trim()) {
        productData.description = formValues.description.trim();
      }

      if (formValues.tags && formValues.tags.length > 0) {
        productData.tags = formValues.tags;
      }

      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
          const fieldValue = field.value?.['value'];

          // Nếu là undefined hoặc null thì loại bỏ
          if (fieldValue === undefined || fieldValue === null) {
            return false;
          }

          // Nếu là string rỗng thì loại bỏ
          if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
            return false;
          }

          // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
          return true;
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'],
          },
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      // Tạo service advanced info với dữ liệu từ form
      const currentTime = Date.now();
      const serviceDurationMinutes = Number(formValues.serviceDuration) || 60;

      const advancedInfo = {
        purchaseCount: (product as any)?.advancedInfo?.purchaseCount || 0,
        servicePackages: [
          {
            name: formValues.name || 'Gói dịch vụ mặc định',
            price: priceData && 'salePrice' in priceData ? priceData.salePrice : 1800000,
            startTime: formValues.serviceTime ? formValues.serviceTime.getTime() : currentTime,
            endTime: formValues.serviceTime
              ? formValues.serviceTime.getTime() + (serviceDurationMinutes * 60 * 1000)
              : currentTime + (serviceDurationMinutes * 60 * 1000),
            timezone: 'Asia/Ho_Chi_Minh',
            description: formValues.description || 'Mô tả gói dịch vụ mặc định',
            quantity: 50,
            minQuantityPerPurchase: 1,
            maxQuantityPerPurchase: 3,
            status: 'PENDING' as const,
            duration: serviceDurationMinutes, // Thời lượng tính bằng phút
            provider: formValues.serviceProvider || '',
            serviceType: formValues.serviceType || 'CONSULTATION',
            serviceLocation: formValues.serviceLocation || 'AT_CENTER',
            imagesMediaTypes: mediaFiles.length > 0 ? mediaFiles.map(file => file.file.type) : ['image/jpeg'],
          },
        ],
      };

      productData.advancedInfo = advancedInfo;

      // Xử lý ảnh theo format yêu cầu: DELETE existing images + ADD new images
      const imageOperations: Array<{
        operation: 'DELETE' | 'ADD';
        key?: string;
        mimeType?: string;
      }> = [];

      // Thêm operations DELETE cho ảnh bị xóa
      if (deletedImages.length > 0) {
        console.log('🗑️ [ServiceProductEditForm] Processing DELETE operations for images:', deletedImages);
        deletedImages.forEach(deletedImage => {
          // Validate key exists
          if (deletedImage.key && deletedImage.key.trim() !== '') {
            imageOperations.push({
              operation: 'DELETE',
              key: deletedImage.key,
            });
          } else {
            console.warn('⚠️ [ServiceProductEditForm] Skipping DELETE operation - invalid key:', deletedImage);
          }
        });
      }

      // Thêm operations ADD cho ảnh mới
      const newMediaFiles = mediaFiles.filter(file => !file.id.startsWith('existing-'));
      if (newMediaFiles.length > 0) {
        console.log('📸 [ServiceProductEditForm] Processing ADD operations for new images:', newMediaFiles.map(f => ({
          fileName: f.file.name,
          type: f.file.type,
          size: f.file.size
        })));

        newMediaFiles.forEach((file) => {
          // Validate mimeType exists and is valid
          if (file.file.type && file.file.type.trim() !== '') {
            imageOperations.push({
              operation: 'ADD',
              mimeType: file.file.type,
            });
          } else {
            console.warn('⚠️ [ServiceProductEditForm] Skipping ADD operation - invalid mimeType:', {
              fileName: file.file.name,
              type: file.file.type,
            });
          }
        });
      }

      console.log('🔧 [ServiceProductEditForm] Final image operations:', imageOperations);

      // Chỉ thêm images vào request nếu có operations
      if (imageOperations.length > 0) {
        productData.images = imageOperations;
        console.log('✅ [ServiceProductEditForm] Added images operations to productData');
      } else {
        console.log('ℹ️ [ServiceProductEditForm] No image operations needed');
      }

      // Thêm các trường service vào root level của productData để match với API response format
      const serviceUpdateData = {
        ...productData,
        serviceTime: formValues.serviceTime ? formValues.serviceTime.getTime() : undefined,
        serviceDuration: String(serviceDurationMinutes),
        serviceType: formValues.serviceType || 'CONSULTATION',
        serviceLocation: formValues.serviceLocation || 'AT_CENTER',
      } as any;

      console.log('📤 [ServiceProductEditForm] Final service update data:', serviceUpdateData);
      console.log('🔧 [ServiceProductEditForm] Service fields in update:', {
        serviceTime: serviceUpdateData.serviceTime,
        serviceDuration: serviceUpdateData.serviceDuration,
        serviceType: serviceUpdateData.serviceType,
        serviceLocation: serviceUpdateData.serviceLocation,
        advancedInfo: serviceUpdateData.advancedInfo,
        hasImages: !!serviceUpdateData.images,
        imagesOperationsCount: serviceUpdateData.images ? serviceUpdateData.images.length : 0,
      });

      // Debug: Kiểm tra format request body trước khi gửi API
      console.log('🔍 [ServiceProductEditForm] Request body structure check:');
      console.log('📊 [ServiceProductEditForm] Product ID:', productId);
      console.log('📊 [ServiceProductEditForm] Service update data keys:', Object.keys(serviceUpdateData));
      console.log('📊 [ServiceProductEditForm] Images operations in request:', serviceUpdateData.images);

      if (serviceUpdateData.images) {
        console.log('🖼️ [ServiceProductEditForm] Images operations details:');
        serviceUpdateData.images.forEach((op: any, index: number) => {
          console.log(`  ${index + 1}. Operation: ${op.operation}`, {
            key: op.key || 'N/A',
            mimeType: op.mimeType || 'N/A',
          });
        });
      }

      // Gọi API cập nhật sản phẩm với service data
      const response = await updateProductMutation.mutateAsync({
        id: productId,
        data: serviceUpdateData,
      });

      console.log('✅ [ServiceProductEditForm] Update API response:', response);

      // Upload media nếu có và API trả về images với upload URLs
      // Only upload new images (not existing ones that start with "existing-")
      const newMediaFilesForUpload = mediaFiles.filter(file => !file.id.startsWith('existing-'));

      console.log('📸 [ServiceProductEditForm] Media files for upload:', {
        totalMediaFiles: mediaFiles.length,
        newMediaFilesForUpload: newMediaFilesForUpload.length,
        newMediaFilesList: newMediaFilesForUpload.map(f => ({ id: f.id, fileName: f.file.name, type: f.file.type })),
      });

      if (newMediaFilesForUpload.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          // Kiểm tra alternative upload URLs structure (có thể API trả về format khác)
          let alternativeUploadUrls = null;
          if (!hasUploadUrls && response && response.uploadUrls) {
            // Kiểm tra advancedImagesUploadUrls
            if ('advancedImagesUploadUrls' in response.uploadUrls && Array.isArray(response.uploadUrls.advancedImagesUploadUrls)) {
              alternativeUploadUrls = response.uploadUrls.advancedImagesUploadUrls;
            }
          }

          // Sử dụng upload URLs từ imagesUploadUrls hoặc advancedImagesUploadUrls
          const finalUploadUrls = hasUploadUrls && response.uploadUrls
            ? response.uploadUrls.imagesUploadUrls
            : alternativeUploadUrls;

          if (finalUploadUrls && finalUploadUrls.length > 0) {
            const uploadUrls = finalUploadUrls;
            if (uploadUrls.length > 0) {
              // Tạo mapping giữa new media files và upload URLs từ backend
              const uploadTasks = newMediaFilesForUpload.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                if (!uploadInfo) {
                  throw new Error(`Upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              });

              // Tạo array các file và URLs để upload cùng lúc
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh mới cùng lúc, skip cache invalidation trong hook
              // Cache invalidation đã được xử lý trong useUpdateProduct hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            } else {
              NotificationUtil.warning({
                message: t(
                  'business:product.mediaUploadWarning',
                  'Sản phẩm đã được cập nhật nhưng không có URL để tải ảnh lên'
                ),
                duration: 5000,
              });
            }
          } else {
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh (không có upload URLs)'
              ),
              duration: 5000,
            });
          }
        } catch {
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      } else {
        console.log('ℹ️ [ServiceProductEditForm] No new media files to upload');
      }

      setIsUploading(false);

      // Reset deletedImages sau khi update thành công
      setDeletedImages([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.updateSuccess', 'Cập nhật sản phẩm thành công'),
        duration: 3000,
      });

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      setIsUploading(false);

      // Kiểm tra nếu là lỗi validation
      if (error && typeof error === 'object' && 'issues' in error) {
        NotificationUtil.error({
          message: t('business:product.validationError', 'Lỗi validation dữ liệu'),
          duration: 3000,
        });
      } else {
        NotificationUtil.error({
          message: t('business:product.updateError', 'Có lỗi xảy ra khi cập nhật sản phẩm'),
          duration: 3000,
        });
      }
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = useCallback((values: ServiceProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      if (!values.listPrice || values.listPrice === '') {
        throw new Error(t('business:product.form.validation.listPriceRequired'));
      }
      if (!values.salePrice || values.salePrice === '') {
        throw new Error(t('business:product.form.validation.salePriceRequired'));
      }
      if (!values.currency || values.currency.trim() === '') {
        throw new Error(t('business:product.form.validation.currencyRequired'));
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error(t('business:product.form.validation.listPriceInvalid'));
      }
      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error(t('business:product.form.validation.salePriceInvalid'));
      }
      if (listPrice <= salePrice) {
        throw new Error(t('business:product.form.validation.listPriceGreaterThanSale'));
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error(t('business:product.form.validation.priceDescriptionRequired'));
      }
      return {
        priceDescription: values.priceDescription.trim(),
      };
    }

    throw new Error(t('business:product.form.validation.invalidPriceType'));
  }, [t]);

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Xác định giá trị mặc định dựa trên kiểu dữ liệu
        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        let defaultValue: string | number | boolean = '';

        // Xác định giá trị mặc định dựa trên type hoặc component
        if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0;
        } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
          defaultValue = false;
        } else {
          defaultValue = '';
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Hiển thị loading khi đang fetch chi tiết sản phẩm
  if (isLoadingProduct) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="flex justify-center items-center py-8">
          <Typography variant="body1">
            {t('business:product.form.loadingProduct', 'Đang tải thông tin sản phẩm...')}
          </Typography>
        </div>
      </FormMultiWrapper>
    );
  }

  // Hiển thị lỗi nếu không tải được sản phẩm
  if (productError || !product) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-red-500">
            {t('business:product.form.loadError', 'Không thể tải thông tin sản phẩm')}
          </Typography>
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            {t('common:back', 'Quay lại')}
          </button>
        </div>
      </FormMultiWrapper>
    );
  }

  return (
    <FormMultiWrapper title={t('business:product.form.editServiceTitle', 'Chỉnh sửa dịch vụ')}>
      <Form
        ref={formRef}
        schema={serviceProductSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
        onError={errors => {
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || t('business:product.form.validation.formValidationError');
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        submitOnEnter={false}
        className="space-y-4"
      >

        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.serviceNamePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.serviceDescriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Giá sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '2. Giá dịch vụ')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
              <Controller
                name="typePrice"
                render={({ field }) => (
                  <Select
                    fullWidth
                    value={field.value || PriceTypeEnum.HAS_PRICE}
                    onChange={(value) => field.onChange(value)}
                    options={[
                      {
                        value: PriceTypeEnum.HAS_PRICE,
                        label: t('business:product.priceType.hasPrice'),
                      },
                      {
                        value: PriceTypeEnum.STRING_PRICE,
                        label: t('business:product.priceType.stringPrice'),
                      },
                    ]}
                  />
                )}
              />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.HAS_PRICE,
              }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem name="listPrice" label={t('business:product.listPrice')} required>
                  <Input fullWidth type="number" min="0" placeholder={t('business:product.form.listPricePlaceholder')} />
                </FormItem>
                <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                  <Input fullWidth type="number" min="0" placeholder={t('business:product.form.salePricePlaceholder')} />
                </FormItem>
                <FormItem name="currency" label={t('business:product.currency')} required>
                  <Controller
                    name="currency"
                    render={({ field }) => (
                      <Select
                        fullWidth
                        value={field.value || 'VND'}
                        onChange={value => field.onChange(value)}
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    )}
                  />
                </FormItem>
              </div>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.STRING_PRICE,
              }}
            >
              <FormItem
                name="priceDescription"
                label={t('business:product.priceDescription')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.priceDescriptionPlaceholder')}
                />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 3. Thông tin dịch vụ */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.serviceInfo', '3. Thông tin dịch vụ')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem name="serviceTime" label={t('business:product.form.serviceProduct.serviceTime')}>
                <Controller
                  name="serviceTime"
                  render={({ field }) => (
                    <DateTimePicker
                      value={field.value}
                      onChange={field.onChange}
                      placeholder={t('business:product.form.serviceProduct.serviceTimePlaceholder')}
                      format="dd/MM/yyyy HH:mm"
                      timeFormat="24h"
                      fullWidth
                      minDate={new Date()}
                    />
                  )}
                />
              </FormItem>

              <FormItem name="serviceDuration" label={t('business:product.form.serviceProduct.serviceDuration')}>
                <Input
                  fullWidth
                  type="number"
                  min="1"
                  placeholder={t('business:product.form.serviceProduct.serviceDurationPlaceholder')}
                />
              </FormItem>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem name="serviceType" label={t('business:product.form.serviceProduct.serviceType.title')}>
                <Controller
                  name="serviceType"
                  render={({ field }) => {
                    console.log('🔧 [ServiceProductEditForm] serviceType field:', {
                      value: field.value,
                      name: field.name,
                      onChange: typeof field.onChange,
                    });

                    return (
                      <Select
                        fullWidth
                        value={field.value || 'CONSULTATION'}
                        onChange={(value) => {
                          console.log('🔄 [ServiceProductEditForm] serviceType onChange:', {
                            oldValue: field.value,
                            newValue: value,
                            valueType: typeof value,
                          });
                          field.onChange(value);
                        }}
                        options={[
                          { value: 'CONSULTATION', label: t('business:product.form.serviceProduct.serviceType.consultation') },
                          { value: 'BEAUTY', label: t('business:product.form.serviceProduct.serviceType.beauty') },
                          { value: 'MAINTENANCE', label: t('business:product.form.serviceProduct.serviceType.maintenance') },
                          { value: 'INSTALLATION', label: t('business:product.form.serviceProduct.serviceType.installation') },
                        ]}
                      />
                    );
                  }}
                />
              </FormItem>

              <FormItem name="serviceLocation" label={t('business:product.form.serviceProduct.serviceLocation.title')}>
                <Controller
                  name="serviceLocation"
                  render={({ field }) => {
                    console.log('🔧 [ServiceProductEditForm] serviceLocation field:', {
                      value: field.value,
                      name: field.name,
                      onChange: typeof field.onChange,
                    });

                    return (
                      <Select
                        fullWidth
                        value={field.value || 'AT_CENTER'}
                        onChange={(value) => {
                          console.log('🔄 [ServiceProductEditForm] serviceLocation onChange:', {
                            oldValue: field.value,
                            newValue: value,
                            valueType: typeof value,
                          });
                          field.onChange(value);
                        }}
                        options={[
                          { value: 'AT_HOME', label: t('business:product.form.serviceProduct.serviceLocation.atHome') },
                          { value: 'AT_CENTER', label: t('business:product.form.serviceProduct.serviceLocation.atCenter') },
                          { value: 'ONLINE', label: t('business:product.form.serviceProduct.serviceLocation.online') },
                        ]}
                      />
                    );
                  }}
                />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 4. Hình ảnh dịch vụ */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '4. Hình ảnh dịch vụ')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: ExtendedFileWithMetadata[]) => {
                      handleMediaFilesChange(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 5. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '5. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(field => field.fieldId)}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-4 mt-4">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.selectedCustomFields', 'Trường tùy chỉnh đã chọn:')}
                </Typography>
                {productCustomFields.map(field => {
                  const fieldValue = field.value?.['value'] as string | number | boolean || '';

                  // Debug logging
                  console.log(`🔍 ServiceProductEdit CustomField ${field.label}:`, {
                    fieldId: field.fieldId,
                    type: field.type,
                    component: field.component,
                    rawValue: field.value,
                    extractedValue: fieldValue,
                    valueType: typeof fieldValue
                  });

                  return (
                    <CustomFieldRenderer
                      key={field.id}
                      field={field}
                      value={fieldValue}
                      onChange={(value: string | number | boolean) => {
                        console.log(`🔄 ServiceProductEdit CustomField ${field.label} onChange:`, {
                          oldValue: fieldValue,
                          newValue: value,
                          newValueType: typeof value
                        });
                        handleUpdateCustomFieldInProduct(field.id, value);
                      }}
                      onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              updateProductMutation.isPending || isUploading
                ? t('business:product.form.updating', 'Đang cập nhật...')
                : t('business:product.form.update', 'Cập nhật dịch vụ')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={updateProductMutation.isPending || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default ServiceProductEditForm;
