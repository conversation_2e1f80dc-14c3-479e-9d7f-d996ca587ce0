import React, { useState, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
  DateTimePicker,
} from '@/shared/components/common';
import { Controller } from 'react-hook-form';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto,
  ServiceProductConfig,
  ProductTypeEnum,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { useCreateServiceProduct } from '../../hooks/useProductQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';

interface ServiceProductFormProps {
  onCancel: () => void;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho form values
interface ServiceProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Service product specific fields
  serviceTime?: Date;
  serviceDuration?: string | number;
  serviceProvider?: string;
  serviceType: ServiceProductConfig['servicePackages'];
  serviceLocation: ServiceProductConfig['servicePackages'];
}

/**
 * Form tạo dịch vụ
 */
const ServiceProductForm: React.FC<ServiceProductFormProps> = ({ onCancel }) => {
  const { t } = useTranslation(['business', 'common']);

  // Hook để tạo sản phẩm SERVICE
  const createServiceProductMutation = useCreateServiceProduct();

  // Schema validation cho dịch vụ
  const serviceProductSchema = z
    .object({
      name: z.string().min(1, 'Tên dịch vụ không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      // Service product specific validations
      serviceTime: z.date().optional(),
      serviceDuration: z.union([z.string(), z.number()]).optional().transform((val) => {
        if (val === undefined || val === null || val === '') return undefined;
        const num = Number(val);
        if (isNaN(num) || num < 1) {
          throw new Error('Thời lượng dịch vụ phải lớn hơn 0');
        }
        return num;
      }),
      serviceProvider: z.string().optional(),
      serviceType: z.enum(['CONSULTATION', 'BEAUTY', 'MAINTENANCE', 'INSTALLATION']),
      serviceLocation: z.enum(['AT_HOME', 'AT_CENTER', 'ONLINE']),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra giá phù hợp với loại giá
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        if (!data.listPrice || data.listPrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá niêm yết',
            path: ['listPrice'],
          });
        }
        if (!data.salePrice || data.salePrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá bán',
            path: ['salePrice'],
          });
        }
        if (!data.currency || data.currency.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng chọn đơn vị tiền tệ',
            path: ['currency'],
          });
        }
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!data.priceDescription || !data.priceDescription.trim()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập mô tả giá',
            path: ['priceDescription'],
          });
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 ServiceProductForm handleSubmit called with values:', values);

    if (!values['name'] || !values['typePrice']) {
      console.error('❌ Missing required fields:', {
        name: values['name'],
        typePrice: values['typePrice'],
      });
      NotificationUtil.error({
        message: t('business:product.form.validation.serviceRequiredFieldsMissing'),
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as ServiceProductFormValues;
      setIsUploading(true);

      console.log('✅ Form values before processing:', formValues);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        console.error('❌ Price validation error:', priceError);
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : t('business:product.form.validation.priceValidationError'),
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Tạo advancedInfo theo API structure cho SERVICE product
      const currentTime = Date.now();
      const advancedInfo = {
        purchaseCount: 0,
        servicePackages: [
          {
            name: formValues.name || t('business:product.form.serviceProduct.defaultPackageName'),
            price: priceData && 'salePrice' in priceData ? priceData.salePrice : 1800000,
            startTime: formValues.serviceTime ? formValues.serviceTime.getTime() : currentTime,
            endTime: formValues.serviceTime
              ? formValues.serviceTime.getTime() + ((Number(formValues.serviceDuration) || 60) * 60 * 1000)
              : currentTime + (60 * 60 * 1000), // +1 hour default
            timezone: 'Asia/Ho_Chi_Minh',
            description: formValues.description || t('business:product.form.serviceProduct.defaultPackageDescription'),
            quantity: 50,
            minQuantityPerPurchase: 1,
            maxQuantityPerPurchase: 3,
            status: 'PENDING' as const,
            imagesMediaTypes: mediaFiles.length > 0 ? mediaFiles.map(file => file.file.type) : ['image/jpeg'],
            // Thêm các trường từ request body
            provider: formValues.serviceProvider || '',
            serviceType: formValues.serviceType || 'CONSULTATION',
            serviceLocation: formValues.serviceLocation || 'AT_CENTER',
          },
        ],
      };

      const productData: CreateProductDto = {
        name: formValues.name,
        productType: ProductTypeEnum.SERVICE,
        typePrice: formValues.typePrice,
        price: priceData,
        ...(formValues.description && { description: formValues.description }),
        ...(formValues.tags && formValues.tags.length > 0 && { tags: formValues.tags }),
        ...(mediaFiles.length > 0 && { imagesMediaTypes: mediaFiles.map(file => file.file.type) }),
        ...(productCustomFields.length > 0 && {
          customFields: productCustomFields
            .filter(field => {
              // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
              const fieldValue = field.value?.['value'];

              // Nếu là undefined hoặc null thì loại bỏ
              if (fieldValue === undefined || fieldValue === null) {
                return false;
              }

              // Nếu là string rỗng thì loại bỏ
              if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
                return false;
              }

              // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
              return true;
            })
            .map(field => ({
              customFieldId: field.fieldId,
              value: field.value,
            }))
        }),
        // Thêm các trường service theo request body format
        ...(formValues.serviceTime && { serviceTime: formValues.serviceTime.getTime() }),
        ...(formValues.serviceDuration && { serviceDuration: String(formValues.serviceDuration) }),
        ...(formValues.serviceProvider && { serviceProvider: formValues.serviceProvider }),
        serviceType: formValues.serviceType || 'CONSULTATION',
        serviceLocation: formValues.serviceLocation || 'AT_CENTER',
        advancedInfo,
      } as Record<string, unknown>; // Cast to Record để tránh lỗi TypeScript với các trường service

      console.log('📤 Final service product data to be sent to API:', JSON.stringify(productData, null, 2));

      // Gọi API tạo sản phẩm SERVICE bằng hook
      const response = await createServiceProductMutation.mutateAsync(productData);

      console.log('✅ Service product created successfully:', response);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray((response.uploadUrls as Record<string, unknown>).imagesUploadUrls);

          if (hasUploadUrls) {
            const uploadUrls = (response.uploadUrls as Record<string, unknown>).imagesUploadUrls;

            if (uploadUrls.length > 0) {
              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                if (!uploadInfo) return null;
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              }).filter(Boolean) as Array<{
                file: File;
                uploadUrl: string;
                key: string;
                index: number;
              }>;

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              console.log('✅ All service product images uploaded successfully');

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            }
          } else {
            console.warn('⚠️ Media files exist but no upload URLs provided from backend');
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ Error uploading service product images:', uploadError);
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.createSuccess', 'Tạo dịch vụ thành công'),
        duration: 3000,
      });

      // Quay lại trang trước
      onCancel();
    } catch (error) {
      console.error('Error in ServiceProductForm handleSubmit:', error);
      setIsUploading(false);

      NotificationUtil.error({
        message: t('business:product.createError', 'Có lỗi xảy ra khi tạo dịch vụ'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = useCallback((values: ServiceProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      if (!values.listPrice || values.listPrice === '') {
        throw new Error(t('business:product.form.validation.listPriceRequired'));
      }
      if (!values.salePrice || values.salePrice === '') {
        throw new Error(t('business:product.form.validation.salePriceRequired'));
      }
      if (!values.currency || values.currency.trim() === '') {
        throw new Error(t('business:product.form.validation.currencyRequired'));
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error(t('business:product.form.validation.listPriceInvalid'));
      }
      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error(t('business:product.form.validation.salePriceInvalid'));
      }
      if (listPrice <= salePrice) {
        throw new Error(t('business:product.form.validation.listPriceGreaterThanSale'));
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error(t('business:product.form.validation.priceDescriptionRequired'));
      }
      return {
        priceDescription: values.priceDescription.trim(),
      };
    }

    throw new Error(t('business:product.form.validation.invalidPriceType'));
  }, [t]);

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Xác định giá trị mặc định dựa trên kiểu dữ liệu
        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        let defaultValue: string | number | boolean = '';

        // Xác định giá trị mặc định dựa trên type hoặc component
        if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0;
        } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
          defaultValue = false;
        } else {
          defaultValue = '';
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Giá trị mặc định cho form
  const defaultValues = useMemo(
    () => ({
      name: '',
      typePrice: PriceTypeEnum.HAS_PRICE,
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      priceDescription: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      // Service product defaults
      serviceTime: undefined,
      serviceDuration: '60', // 60 phút mặc định
      serviceProvider: '',
      serviceType: 'CONSULTATION' as const,
      serviceLocation: 'AT_CENTER' as const,
    }),
    []
  );

  return (
    <FormMultiWrapper title={t('business:product.form.createServiceTitle', 'Tạo dịch vụ')}>
      <Form
        ref={formRef}
        schema={serviceProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || t('business:product.form.validation.formValidationError');
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.serviceNamePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.serviceDescriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Giá sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '2. Giá dịch vụ')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
              <Select
                fullWidth
                options={[
                  {
                    value: PriceTypeEnum.HAS_PRICE,
                    label: t('business:product.priceType.hasPrice'),
                  },
                  {
                    value: PriceTypeEnum.STRING_PRICE,
                    label: t('business:product.priceType.stringPrice'),
                  },
                ]}
              />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.HAS_PRICE,
              }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem name="listPrice" label={t('business:product.listPrice')} required>
                  <Input fullWidth type="number" min="0" placeholder={t('business:product.form.listPricePlaceholder')} />
                </FormItem>
                <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                  <Input fullWidth type="number" min="0" placeholder={t('business:product.form.salePricePlaceholder')} />
                </FormItem>
                <FormItem name="currency" label={t('business:product.currency')} required>
                  <Controller
                    name="currency"
                    render={({ field }) => (
                      <Select
                        fullWidth
                        value={field.value || 'VND'}
                        onChange={value => field.onChange(value)}
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    )}
                  />
                </FormItem>
              </div>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.STRING_PRICE,
              }}
            >
              <FormItem
                name="priceDescription"
                label={t('business:product.priceDescription')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.priceDescriptionPlaceholder')}
                />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 3. Thông tin dịch vụ */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.serviceInfo', '3. Thông tin dịch vụ')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem name="serviceTime" label={t('business:product.form.serviceProduct.serviceTime')}>
                <Controller
                  name="serviceTime"
                  render={({ field }) => (
                    <DateTimePicker
                      value={field.value}
                      onChange={field.onChange}
                      placeholder={t('business:product.form.serviceProduct.serviceTimePlaceholder')}
                      format="dd/MM/yyyy HH:mm"
                      timeFormat="24h"
                      fullWidth
                      minDate={new Date()}
                    />
                  )}
                />
              </FormItem>

              <FormItem name="serviceDuration" label={t('business:product.form.serviceProduct.serviceDuration')}>
                <Input
                  fullWidth
                  type="number"
                  min="1"
                  placeholder={t('business:product.form.serviceProduct.serviceDurationPlaceholder')}
                />
              </FormItem>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem name="serviceType" label={t('business:product.form.serviceProduct.serviceType.title')} required>
                <Select
                  fullWidth
                  options={[
                    { value: 'CONSULTATION', label: t('business:product.form.serviceProduct.serviceType.consultation') },
                    { value: 'BEAUTY', label: t('business:product.form.serviceProduct.serviceType.beauty') },
                    { value: 'MAINTENANCE', label: t('business:product.form.serviceProduct.serviceType.maintenance') },
                    { value: 'INSTALLATION', label: t('business:product.form.serviceProduct.serviceType.installation') },
                  ]}
                />
              </FormItem>

              <FormItem name="serviceLocation" label={t('business:product.form.serviceProduct.serviceLocation.title')} required>
                <Select
                  fullWidth
                  options={[
                    { value: 'AT_HOME', label: t('business:product.form.serviceProduct.serviceLocation.atHome') },
                    { value: 'AT_CENTER', label: t('business:product.form.serviceProduct.serviceLocation.atCenter') },
                    { value: 'ONLINE', label: t('business:product.form.serviceProduct.serviceLocation.online') },
                  ]}
                />
              </FormItem>
            </div>
            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.serviceDescriptionPlaceholder')}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 4. Hình ảnh dịch vụ */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '4. Hình ảnh dịch vụ')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: FileWithMetadata[]) => {
                      setMediaFiles(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 5. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '5. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(field => field.fieldId)}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-4 mt-4">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.selectedCustomFields', 'Trường tùy chỉnh đã chọn:')}
                </Typography>
                {productCustomFields.map(field => {
                  const fieldValue = field.value?.['value'] as string | number | boolean || '';

                  // Debug logging
                  console.log(`🔍 ServiceProduct CustomField ${field.label}:`, {
                    fieldId: field.fieldId,
                    type: field.type,
                    component: field.component,
                    rawValue: field.value,
                    extractedValue: fieldValue,
                    valueType: typeof fieldValue
                  });

                  return (
                    <CustomFieldRenderer
                      key={field.id}
                      field={field}
                      value={fieldValue}
                      onChange={(value: string | number | boolean) => {
                        console.log(`🔄 ServiceProduct CustomField ${field.label} onChange:`, {
                          oldValue: fieldValue,
                          newValue: value,
                          newValueType: typeof value
                        });
                        handleUpdateCustomFieldInProduct(field.id, value);
                      }}
                      onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              createServiceProductMutation.isPending || isUploading
                ? t('business:product.form.creating', 'Đang tạo...')
                : t('business:product.form.create', 'Tạo dịch vụ')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={createServiceProductMutation.isPending || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default ServiceProductForm;
