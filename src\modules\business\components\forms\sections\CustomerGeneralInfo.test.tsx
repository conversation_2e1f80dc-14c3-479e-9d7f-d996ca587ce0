import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/shared/i18n';
import CustomerGeneralInfo from './CustomerGeneralInfo';
import { CustomerDetailData } from './types';

// Mock the hooks
jest.mock('../../../hooks/useCustomerQuery', () => ({
  useUpdateCustomerBasicInfo: () => ({
    mutateAsync: jest.fn().mockResolvedValue({
      result: {
        id: '1',
        name: 'Test Customer',
        avatar: 'test-avatar.jpg'
      }
    }),
    isPending: false
  }),
  useUpdateCustomerInformation: () => ({
    mutateAsync: jest.fn().mockResolvedValue({
      result: {
        id: '1',
        name: 'Test Customer',
        avatar: 'test-avatar.jpg'
      }
    }),
    isPending: false
  })
}));

// Mock notification util
jest.mock('@/shared/utils/notification', () => ({
  NotificationUtil: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

const mockCustomer: CustomerDetailData = {
  id: '1',
  name: 'Test Customer',
  email: '<EMAIL>',
  phone: '**********',
  address: '123 Test Street',
  avatar: 'test-avatar.jpg',
  status: 'active',
  customerSince: '2023-01-01',
  tags: ['VIP', 'Premium']
};

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </QueryClientProvider>
  );
};

describe('CustomerGeneralInfo', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders customer information correctly', () => {
    renderWithProviders(<CustomerGeneralInfo customer={mockCustomer} />);
    
    expect(screen.getByDisplayValue('Test Customer')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('**********')).toBeInTheDocument();
    expect(screen.getByDisplayValue('123 Test Street')).toBeInTheDocument();
  });

  it('displays existing tags', () => {
    renderWithProviders(<CustomerGeneralInfo customer={mockCustomer} />);
    
    expect(screen.getByText('VIP')).toBeInTheDocument();
    expect(screen.getByText('Premium')).toBeInTheDocument();
  });

  it('allows adding new tags', async () => {
    renderWithProviders(<CustomerGeneralInfo customer={mockCustomer} />);
    
    const tagInput = screen.getByPlaceholderText('Thêm tag mới...');
    
    fireEvent.change(tagInput, { target: { value: 'New Tag' } });
    fireEvent.keyDown(tagInput, { key: 'Enter' });
    
    await waitFor(() => {
      expect(screen.getByText('New Tag')).toBeInTheDocument();
    });
  });

  it('allows removing tags', async () => {
    renderWithProviders(<CustomerGeneralInfo customer={mockCustomer} />);
    
    const vipTag = screen.getByText('VIP');
    const closeButton = vipTag.parentElement?.querySelector('[data-testid="chip-close"]');
    
    if (closeButton) {
      fireEvent.click(closeButton);
      
      await waitFor(() => {
        expect(screen.queryByText('VIP')).not.toBeInTheDocument();
      });
    }
  });

  it('shows correct status chip', () => {
    renderWithProviders(<CustomerGeneralInfo customer={mockCustomer} />);
    
    expect(screen.getByText('Hoạt động')).toBeInTheDocument();
  });
});
