# Fix: Cậ<PERSON> nhật Tags cho Customer

## Vấn đề
Trong trang `/business/customer`, khi cập nhật thông tin khách hàng, trường `tags` không được gửi lên API vì component `CustomerGeneralInfo` đang sử dụng endpoint `/basic-info` thay vì endpoint `/information`.

## Phân tích
- **API endpoint hiện tại**: `PUT /user/convert-customers/{id}/basic-info` - không hỗ trợ tags
- **API endpoint có hỗ trợ tags**: `PUT /user/convert-customers/{id}/information` - có hỗ trợ tags

## Giải pháp

### 1. Thêm Interface mới
Tạo `UpdateCustomerInformationDto` trong `customer.service.ts` để hỗ trợ đầy đủ các trường bao gồm tags.

### 2. Thêm Service method mới
Thêm `updateCustomerInformation` method trong `CustomerService` để gọi endpoint `/information`.

### 3. Thêm Hook mới
Tạo `useUpdateCustomerInformation` hook để xử lý việc cập nhật thông tin đầy đủ khách hàng.

### 4. Cập nhật Component
Cập nhật `CustomerGeneralInfo` component để:
- Theo dõi thay đổi tags
- Sử dụng API phù hợp dựa trên việc có thay đổi tags hay không
- Nếu có thay đổi tags → sử dụng `/information` endpoint
- Nếu không có thay đổi tags → sử dụng `/basic-info` endpoint (tối ưu hơn)

## Các file đã thay đổi

### 1. `src/modules/business/services/customer.service.ts`
- Thêm `UpdateCustomerInformationDto` interface
- Thêm `updateCustomerInformation` method

### 2. `src/modules/business/hooks/useCustomerQuery.ts`
- Import `UpdateCustomerInformationDto`
- Thêm `useUpdateCustomerInformation` hook

### 3. `src/modules/business/components/forms/sections/CustomerGeneralInfo.tsx`
- Import `useUpdateCustomerInformation` hook
- Thêm state để theo dõi tags gốc
- Thêm logic để phát hiện thay đổi tags
- Cập nhật `handleSave` để sử dụng API phù hợp

## Cách hoạt động

1. **Khi component load**: Lưu tags gốc vào state
2. **Khi user thay đổi tags**: Component phát hiện sự khác biệt
3. **Khi save**:
   - Nếu có thay đổi tags → gọi `PUT /user/convert-customers/{id}/information`
   - Nếu không có thay đổi tags → gọi `PUT /user/convert-customers/{id}/basic-info`

## Lợi ích

1. **Tương thích ngược**: Vẫn sử dụng `/basic-info` khi không cần thiết
2. **Hiệu quả**: Chỉ sử dụng `/information` khi thực sự cần
3. **Đầy đủ tính năng**: Hỗ trợ cập nhật tags như yêu cầu
4. **Dễ bảo trì**: Logic rõ ràng và dễ hiểu

## Test

Đã tạo test file `CustomerGeneralInfo.test.tsx` để kiểm tra:
- Hiển thị thông tin khách hàng
- Hiển thị tags hiện có
- Thêm tags mới
- Xóa tags
- Hiển thị status chip

## API Endpoints

### Basic Info (không có tags)
```
PUT /user/convert-customers/{id}/basic-info
{
  "name": "string",
  "phone": "string", 
  "email": { "primary": "string", "secondary": "string" },
  "address": "string",
  "avatarFile": { "fileName": "string", "mimeType": "string" }
}
```

### Information (có tags)
```
PUT /user/convert-customers/{id}/information
{
  "name": "string",
  "phone": "string",
  "email": { "primary": "string", "secondary": "string" },
  "address": "string",
  "tags": ["string"],
  "avatarFile": { "fileName": "string", "mimeType": "string" },
  "platform": "string",
  "timezone": "string",
  "agentId": "string",
  "metadata": [{ "configId": "string", "value": "any" }],
  "facebookLink": "string",
  "twitterLink": "string", 
  "linkedinLink": "string",
  "zaloLink": "string",
  "websiteLink": "string"
}
```
