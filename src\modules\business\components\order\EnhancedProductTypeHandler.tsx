import React, { useMemo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Input,
  Chip,
  Icon,
  FormItem,
  Button,
} from '@/shared/components/common';
import {
  ProductDto,
  ProductTypeEnum,
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto
} from '../../types/product.types';
import { formatCurrency } from '@/shared/utils/format';

interface EnhancedProductTypeHandlerProps {
  product: ProductDto;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  onValidationChange: (isValid: boolean, errors: string[]) => void;
  onPriceCalculated: (price: number) => void;
  onManualPriceChange?: (price: number) => void;
  className?: string;
}

interface ProductValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface EventDetails {
  startTime?: number;
  endTime?: number;
  capacity?: number;
  currentRegistrations?: number;
  registrationDeadline?: number;
  location?: string;
}

interface ServicePackage {
  minQuantityPerPurchase?: number;
  maxQuantityPerPurchase?: number;
  status?: string;
  startTime?: number;
  endTime?: number;
}

/**
 * Enhanced component xử lý logic và hiển thị theo loại sản phẩm
 * Hỗ trợ đầy đủ 5 loại sản phẩm và 2 loại giá
 */
const EnhancedProductTypeHandler: React.FC<EnhancedProductTypeHandlerProps> = ({
  product,
  quantity,
  onQuantityChange,
  onValidationChange,
  onPriceCalculated,
  onManualPriceChange,
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Xác định loại sản phẩm và loại giá với type assertion để tránh unreachable case
  const productType: ProductTypeEnum = product.productType || ProductTypeEnum.PHYSICAL;
  const priceType: PriceTypeEnum = product.typePrice || PriceTypeEnum.NO_PRICE;

  // State cho giá thủ công (dành cho STRING_PRICE)
  const [manualPrice, setManualPrice] = React.useState<number>(0);

  // Validation theo loại sản phẩm
  const validation = useMemo((): ProductValidation => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validation chung
    if (quantity <= 0) {
      errors.push(t('business:order.validation.quantityRequired'));
    }

    // Validation theo loại sản phẩm
    switch (productType as ProductTypeEnum) {
      case ProductTypeEnum.PHYSICAL: {
        // Kiểm tra tồn kho
        if (product.inventory && product.inventory.availableQuantity !== undefined && quantity > product.inventory.availableQuantity) {
          errors.push(t('business:order.validation.insufficientStock', {
            available: product.inventory.availableQuantity
          }));
        }

        // Kiểm tra cấu hình vận chuyển
        if (!product.shipmentConfig) {
          warnings.push(t('business:order.validation.missingShipmentConfig'));
        }
        break;
      }

      case ProductTypeEnum.DIGITAL: {
        // Sản phẩm số thường không cần số lượng lớn
        if (quantity > 10) {
          warnings.push(t('business:order.validation.digitalQuantityWarning'));
        }
        break;
      }

      case ProductTypeEnum.EVENT: {
        // Kiểm tra thời gian sự kiện
        if (product.advancedInfo?.['eventDetails']) {
          const eventDetails = product.advancedInfo['eventDetails'] as EventDetails;
          const now = Date.now();

          if (eventDetails.startTime && eventDetails.startTime < now) {
            errors.push(t('business:order.validation.eventExpired'));
          }

          // Kiểm tra số chỗ còn lại
          if (eventDetails.capacity && eventDetails.currentRegistrations) {
            const remaining = eventDetails.capacity - eventDetails.currentRegistrations;
            if (quantity > remaining) {
              errors.push(t('business:order.validation.eventCapacityExceeded', {
                remaining
              }));
            }
          }
        }
        break;
      }

      case ProductTypeEnum.SERVICE: {
        // Kiểm tra giới hạn số lượng cho dịch vụ
        if (product.advancedInfo?.['servicePackages']) {
          const servicePackages = product.advancedInfo['servicePackages'] as ServicePackage[];
          const activePackage = servicePackages.find(pkg => pkg.status === 'ACTIVE') || servicePackages[0];

          if (activePackage) {
            if (activePackage.minQuantityPerPurchase && quantity < activePackage.minQuantityPerPurchase) {
              errors.push(t('business:order.validation.serviceMinQuantity', {
                min: activePackage.minQuantityPerPurchase
              }));
            }

            if (activePackage.maxQuantityPerPurchase && quantity > activePackage.maxQuantityPerPurchase) {
              errors.push(t('business:order.validation.serviceMaxQuantity', {
                max: activePackage.maxQuantityPerPurchase
              }));
            }
          }
        }
        break;
      }

      case ProductTypeEnum.COMBO: {
        // Kiểm tra tồn kho của các sản phẩm con
        if (product.advancedInfo?.['info']) {
          warnings.push(t('business:order.validation.comboStockCheck'));
        }
        break;
      }

      default:
        // Không có validation đặc biệt cho loại sản phẩm này
        break;
    }

    // Validation cho giá STRING_PRICE
    if (priceType === PriceTypeEnum.STRING_PRICE && manualPrice <= 0) {
      errors.push(t('business:order.validation.manualPriceRequired'));
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }, [productType, priceType, quantity, product, manualPrice, t]);

  // Tính toán giá
  const calculatedPrice = useMemo(() => {
    switch (priceType as PriceTypeEnum) {
      case PriceTypeEnum.HAS_PRICE: {
        const hasPrice = product.price as HasPriceDto;
        return hasPrice ? hasPrice.salePrice * quantity : 0;
      }

      case PriceTypeEnum.STRING_PRICE: {
        return manualPrice * quantity;
      }

      case PriceTypeEnum.NO_PRICE:
      default:
        return 0;
    }
  }, [priceType, product.price, quantity, manualPrice]);

  // Effect để thông báo validation và giá
  useEffect(() => {
    onValidationChange(validation.isValid, validation.errors);
    onPriceCalculated(calculatedPrice);
  }, [validation, calculatedPrice, onValidationChange, onPriceCalculated]);

  // Xử lý thay đổi giá thủ công
  const handleManualPriceChange = useCallback((price: number) => {
    setManualPrice(price);
    onManualPriceChange?.(price);
  }, [onManualPriceChange]);

  // Render thông tin giá
  const renderPriceInfo = () => {
    switch (priceType as PriceTypeEnum) {
      case PriceTypeEnum.HAS_PRICE: {
        const hasPrice = product.price as HasPriceDto;
        if (!hasPrice) return null;

        return (
          <div className="space-y-2">
            {hasPrice.listPrice !== hasPrice.salePrice && (
              <div className="flex items-center space-x-2">
                <Typography variant="body2" className="line-through text-gray-500">
                  {formatCurrency(hasPrice.listPrice, hasPrice.currency)}
                </Typography>
                <Chip variant="danger" size="sm">
                  {Math.round((1 - hasPrice.salePrice / hasPrice.listPrice) * 100)}% OFF
                </Chip>
              </div>
            )}
            <Typography variant="h6" className="font-bold text-primary-600">
              {formatCurrency(hasPrice.salePrice, hasPrice.currency)}
            </Typography>
            <Typography variant="body2" className="text-gray-600">
              {t('business:order.total')}: {formatCurrency(calculatedPrice, hasPrice.currency)}
            </Typography>
          </div>
        );
      }

      case PriceTypeEnum.STRING_PRICE: {
        const stringPrice = product.price as StringPriceDto;
        return (
          <div className="space-y-3">
            <div className="p-3 bg-yellow-50 rounded-lg">
              <Typography variant="body2" className="text-yellow-800">
                {stringPrice?.priceDescription || t('business:order.contactForPrice')}
              </Typography>
            </div>
            <FormItem label={t('business:order.manualPrice')} required>
              <Input
                type="number"
                value={manualPrice}
                onChange={(e) => handleManualPriceChange(parseFloat(e.target.value) || 0)}
                placeholder={t('business:order.enterPrice')}
                min={0}
                step={1000}
              />
            </FormItem>
            {manualPrice > 0 && (
              <Typography variant="body2" className="text-gray-600">
                {t('business:order.total')}: {formatCurrency(calculatedPrice)}
              </Typography>
            )}
          </div>
        );
      }

      case PriceTypeEnum.NO_PRICE:
      default:
        return (
          <Typography variant="body2" className="text-gray-500">
            {t('business:order.noPriceAvailable')}
          </Typography>
        );
    }
  };

  // Render thông tin đặc biệt theo loại sản phẩm
  const renderProductSpecificInfo = () => {
    switch (productType as ProductTypeEnum) {
      case ProductTypeEnum.PHYSICAL:
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Icon name="package" size="sm" className="text-blue-600" />
              <Typography variant="caption" className="text-blue-800">
                {t('business:product.physicalProduct')}
              </Typography>
            </div>
            {product.inventory && product.inventory.availableQuantity !== undefined && (
              <div className="flex justify-between">
                <Typography variant="caption">{t('business:product.stock')}</Typography>
                <Typography variant="caption" className="font-medium">
                  {product.inventory.availableQuantity} {t('business:product.units')}
                </Typography>
              </div>
            )}
            {product.shipmentConfig && (
              <div className="text-xs text-gray-500">
                <Icon name="package" size="sm" className="inline mr-1" />
                {product.shipmentConfig.weightGram}g, {product.shipmentConfig.lengthCm}×{product.shipmentConfig.widthCm}×{product.shipmentConfig.heightCm}cm
              </div>
            )}
          </div>
        );

      case ProductTypeEnum.DIGITAL:
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Icon name="cloud" size="sm" className="text-green-600" />
              <Typography variant="caption" className="text-green-800">
                {t('business:product.digitalProduct')}
              </Typography>
            </div>
            <Typography variant="caption" className="text-gray-600">
              {t('business:order.noShippingRequired')}
            </Typography>
          </div>
        );

      case ProductTypeEnum.EVENT:
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Icon name="calendar" size="sm" className="text-purple-600" />
              <Typography variant="caption" className="text-purple-800">
                {t('business:product.eventProduct')}
              </Typography>
            </div>
            {(() => {
              const eventDetails = product.advancedInfo?.['eventDetails'] as EventDetails;
              return eventDetails ? (
                <div className="text-xs text-gray-600">
                  <div>
                    {String(t('business:event.location'))}: {String(eventDetails.location || 'N/A')}
                  </div>
                  <div>
                    {String(t('business:event.capacity'))}: {String(eventDetails.capacity || 'N/A')}
                  </div>
                </div>
              ) : null;
            })()}
          </div>
        );

      case ProductTypeEnum.SERVICE:
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Icon name="settings" size="sm" className="text-orange-600" />
              <Typography variant="caption" className="text-orange-800">
                {t('business:product.serviceProduct')}
              </Typography>
            </div>
            {(() => {
              const servicePackages = product.advancedInfo?.['servicePackages'] as ServicePackage[];
              return servicePackages && Array.isArray(servicePackages) ? (
                <div className="text-xs text-gray-600">
                  {String(t('business:service.packagesAvailable')) || 'Packages Available'}: {String(servicePackages.length)}
                </div>
              ) : null;
            })()}
          </div>
        );

      case ProductTypeEnum.COMBO:
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Icon name="layers" size="sm" className="text-indigo-600" />
              <Typography variant="caption" className="text-indigo-800">
                {t('business:product.comboProduct')}
              </Typography>
            </div>
            {(() => {
              const comboInfo = product.advancedInfo?.['info'] as unknown[];
              return comboInfo && Array.isArray(comboInfo) ? (
                <div className="text-xs text-gray-600">
                  {String(t('business:combo.itemsIncluded'))}: {String(comboInfo.length)}
                </div>
              ) : null;
            })()}
          </div>
        );

      default:
        return null;
    }
  };

  // Render quantity input với validation
  const renderQuantityInput = () => {
    const maxQuantity = (() => {
      switch (productType as ProductTypeEnum) {
        case ProductTypeEnum.PHYSICAL:
          return product.inventory?.availableQuantity || 999;
        case ProductTypeEnum.EVENT:
          if (product.advancedInfo?.['eventDetails']) {
            const eventDetails = product.advancedInfo['eventDetails'] as EventDetails;
            return (eventDetails.capacity || 999) - (eventDetails.currentRegistrations || 0);
          }
          return 999;
        case ProductTypeEnum.SERVICE:
          if (product.advancedInfo?.['servicePackages']) {
            const activePackage = (product.advancedInfo['servicePackages'] as ServicePackage[])[0];
            return activePackage?.maxQuantityPerPurchase || 999;
          }
          return 999;
        case ProductTypeEnum.DIGITAL:
        case ProductTypeEnum.COMBO:
        default:
          return 999;
      }
    })();

    const minQuantity = (() => {
      if ((productType as ProductTypeEnum) === ProductTypeEnum.SERVICE && product.advancedInfo?.['servicePackages']) {
        const activePackage = (product.advancedInfo['servicePackages'] as ServicePackage[])[0];
        return activePackage?.minQuantityPerPurchase || 1;
      }
      return 1;
    })();

    return (
      <div>
        <FormItem label={t('business:order.quantity')} required>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onQuantityChange(Math.max(minQuantity, quantity - 1))}
              disabled={quantity <= minQuantity}
            >
              <Icon name="minus" size="sm" />
            </Button>
            <Input
              type="number"
              value={quantity}
              onChange={(e) => onQuantityChange(Math.max(minQuantity, Math.min(maxQuantity, parseInt(e.target.value) || minQuantity)))}
              min={minQuantity}
              max={maxQuantity}
              className="w-20 text-center"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => onQuantityChange(Math.min(maxQuantity, quantity + 1))}
              disabled={quantity >= maxQuantity}
            >
              <Icon name="plus" size="sm" />
            </Button>
          </div>
        </FormItem>
        <Typography variant="caption" className="text-gray-500 mt-1">
          {t('business:order.quantityRange', { min: minQuantity, max: maxQuantity })}
        </Typography>
      </div>
    );
  };

  return (
    <Card className={className}>
      <div className="p-4 space-y-4">
        {/* Thông tin sản phẩm */}
        <div>
          <Typography variant="h6" className="mb-2">
            {product.name}
          </Typography>
          {renderProductSpecificInfo()}
        </div>

        {/* Thông tin giá */}
        <div>
          <Typography variant="subtitle2" className="mb-2">
            {t('business:order.priceInfo')}
          </Typography>
          {renderPriceInfo()}
        </div>

        {/* Quantity input */}
        {renderQuantityInput()}

        {/* Validation messages */}
        {(validation.errors.length > 0 || validation.warnings.length > 0) && (
          <div className="space-y-2">
            {validation.errors.map((error, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-red-50 rounded">
                <Icon name="alert-circle" size="sm" className="text-red-600" />
                <Typography variant="caption" className="text-red-800">
                  {error}
                </Typography>
              </div>
            ))}
            {validation.warnings.map((warning, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-yellow-50 rounded">
                <Icon name="warning" size="sm" className="text-yellow-600" />
                <Typography variant="caption" className="text-yellow-800">
                  {warning}
                </Typography>
              </div>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
};

export default EnhancedProductTypeHandler;
