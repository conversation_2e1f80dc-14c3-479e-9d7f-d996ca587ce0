import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Select,
  Input,
  FormItem,
  Icon,
  Divider,
  Radio,
  Chip,
} from '@/shared/components/common';
import { DeliveryAddressDto } from '../../services/shipping-calculator.service';
import { useUserAddresses, useCreateUserAddress } from '../../hooks/useUserAddress';

interface AddressSelectorProps {
  selectedAddress?: DeliveryAddressDto;
  onAddressChange: (address: DeliveryAddressDto | undefined) => void;
  customerId?: number;
  className?: string;
}

/**
 * Component chọn địa chỉ giao hàng
 */
const AddressSelector: React.FC<AddressSelectorProps> = ({
  onAddressChange,
  customerId,
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // API hooks
  const { data: userAddressesData = [], isLoading: isLoadingAddresses } = useUserAddresses();
  const createAddressMutation = useCreateUserAddress();



  // Đảm bảo userAddresses luôn là array và memoize để tránh re-render
  const userAddresses = useMemo(() =>
    Array.isArray(userAddressesData) ? userAddressesData : [],
    [userAddressesData]
  );

  // State
  const [addressMode, setAddressMode] = useState<'existing' | 'new'>('existing');
  const [selectedExistingAddressId, setSelectedExistingAddressId] = useState<number | null>(null);
  const [newAddress, setNewAddress] = useState({
    recipientName: '',
    recipientPhone: '',
    address: '',
    province: '',
    district: '',
    ward: '',
    postalCode: '',
    isDefault: false,
    addressType: 'home' as const,
    note: '',
  });

  // Xử lý chọn địa chỉ có sẵn
  const handleExistingAddressSelect = useCallback((addressId: number) => {
    const selectedAddress = userAddresses.find(addr => addr.id === addressId);
    if (selectedAddress) {
      setSelectedExistingAddressId(addressId);
      const deliveryAddress: DeliveryAddressDto = {
        addressId: Number(selectedAddress.id), // Đảm bảo addressId là number
      };
      onAddressChange(deliveryAddress);
    }
  }, [userAddresses, onAddressChange]);

  // Auto-select default address when addresses are loaded
  useEffect(() => {
    if (userAddresses.length > 0 && !selectedExistingAddressId) {
      const defaultAddress = userAddresses.find(addr => addr.isDefault) || userAddresses[0];
      if (defaultAddress) {
        setSelectedExistingAddressId(defaultAddress.id);
        handleExistingAddressSelect(defaultAddress.id);
      }
    }
  }, [userAddresses, selectedExistingAddressId, handleExistingAddressSelect]);

  // Xử lý thay đổi mode
  const handleModeChange = useCallback((mode: 'existing' | 'new') => {
    setAddressMode(mode);
    if (mode === 'existing') {
      // Chọn địa chỉ có sẵn
      if (selectedExistingAddressId) {
        handleExistingAddressSelect(selectedExistingAddressId);
      } else {
        onAddressChange(undefined);
      }
    } else {
      // Reset form địa chỉ mới
      onAddressChange(undefined);
    }
  }, [selectedExistingAddressId, handleExistingAddressSelect, onAddressChange]);

  // Xử lý thay đổi địa chỉ mới
  const handleNewAddressChange = useCallback((field: string, value: string | boolean) => {
    setNewAddress(prev => {
      const updated = { ...prev, [field]: value };

      // Tự động cập nhật delivery address khi có đủ thông tin
      if (updated.recipientName && updated.address && updated.province) {
        const deliveryAddress: DeliveryAddressDto = {
          newAddress: {
            recipientName: updated.recipientName,
            recipientPhone: updated.recipientPhone,
            address: updated.address,
            province: updated.province,
            district: updated.district,
            ward: updated.ward,
            postalCode: updated.postalCode,
            isDefault: updated.isDefault,
            addressType: updated.addressType,
            note: updated.note,
          }
        };
        onAddressChange(deliveryAddress);
      }

      return updated;
    });
  }, [onAddressChange]);

  // Xử lý lưu địa chỉ mới vào database
  const handleSaveNewAddress = useCallback(async () => {
    if (newAddress.recipientName && newAddress.address && newAddress.province) {
      try {
        await createAddressMutation.mutateAsync({
          recipientName: newAddress.recipientName,
          recipientPhone: newAddress.recipientPhone,
          address: newAddress.address,
          province: newAddress.province,
          district: newAddress.district,
          ward: newAddress.ward,
          postalCode: newAddress.postalCode,
          isDefault: newAddress.isDefault,
          addressType: newAddress.addressType,
          note: newAddress.note,
        });

        // Reset form sau khi lưu thành công
        setNewAddress({
          recipientName: '',
          recipientPhone: '',
          address: '',
          province: '',
          district: '',
          ward: '',
          postalCode: '',
          isDefault: false,
          addressType: 'home',
          note: '',
        });

        // Chuyển về mode existing
        setAddressMode('existing');
      } catch (error) {
        console.error('Error saving address:', error);
      }
    }
  }, [newAddress, createAddressMutation]);

  // Render danh sách địa chỉ có sẵn
  const renderExistingAddresses = () => {
    if (isLoadingAddresses) {
      return (
        <div className="text-center py-8">
          <Icon name="loader" size="lg" className="mx-auto mb-4 text-blue-600 animate-spin" />
          <Typography variant="body2" className="text-gray-500">
            {t('business:order.loadingAddresses', 'Đang tải danh sách địa chỉ...')}
          </Typography>
        </div>
      );
    }

    if (userAddresses.length === 0) {
      return (
        <div className="text-center py-8">
          <Icon name="map-pin" size="lg" className="mx-auto mb-4 text-gray-400" />
          <Typography variant="body1" className="text-gray-500 mb-2">
            {t('business:order.noAddressesFound', 'Chưa có địa chỉ nào được lưu')}
          </Typography>
          <Typography variant="body2" className="text-gray-400 mb-4">
            {t('business:order.addFirstAddress', 'Hãy thêm địa chỉ đầu tiên của bạn')}
          </Typography>
          <Button
            variant="primary"
            size="sm"
            onClick={() => setAddressMode('new')}
          >
            <Icon name="plus" size="sm" className="mr-2" />
            {t('business:order.addNewAddress')}
          </Button>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {userAddresses.map((address) => (
          <div
            key={address.id}
            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
              selectedExistingAddressId === address.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleExistingAddressSelect(address.id)}
          >
            <div className="flex items-start gap-3">
              <Radio
                checked={selectedExistingAddressId === address.id}
                onChange={() => handleExistingAddressSelect(address.id)}
              />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Typography variant="subtitle2" className="font-medium">
                    {address.recipientName}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">
                    {address.recipientPhone}
                  </Typography>
                  {address.isDefault && (
                    <Chip variant="success" size="sm">
                      {t('business:order.defaultAddress', 'Mặc định')}
                    </Chip>
                  )}
                </div>
                <Typography variant="body2" className="text-gray-700 mb-1">
                  {address.address}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  {[address.ward, address.district, address.province].filter(Boolean).join(', ')}
                </Typography>
                {address.note && (
                  <Typography variant="caption" className="text-gray-500 block mt-1">
                    {t('business:order.note')}: {address.note}
                  </Typography>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render form địa chỉ mới
  const renderNewAddressForm = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormItem label={t('business:order.recipientName')} required>
          <Input
            value={newAddress.recipientName}
            onChange={(e) => handleNewAddressChange('recipientName', e.target.value)}
            placeholder={t('business:order.recipientNamePlaceholder')}
            fullWidth
          />
        </FormItem>
        
        <FormItem label={t('business:order.recipientPhone')} required>
          <Input
            value={newAddress.recipientPhone}
            onChange={(e) => handleNewAddressChange('recipientPhone', e.target.value)}
            placeholder={t('business:order.recipientPhonePlaceholder')}
            fullWidth
          />
        </FormItem>
      </div>

      <FormItem label={t('business:order.address')} required>
        <Input
          value={newAddress.address}
          onChange={(e) => handleNewAddressChange('address', e.target.value)}
          placeholder={t('business:order.addressPlaceholder')}
          fullWidth
        />
      </FormItem>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormItem label={t('business:order.province')} required>
          <Select
            {...(newAddress.province && { value: newAddress.province })}
            onChange={(value) => handleNewAddressChange('province', value)}
            placeholder={t('business:order.selectProvince')}
            options={[
              { value: 'TP. Hồ Chí Minh', label: 'TP. Hồ Chí Minh' },
              { value: 'Hà Nội', label: 'Hà Nội' },
              { value: 'Đà Nẵng', label: 'Đà Nẵng' },
            ]}
            fullWidth
          />
        </FormItem>
        
        <FormItem label={t('business:order.district')} required>
          <Select
            {...(newAddress.district && { value: newAddress.district })}
            onChange={(value) => handleNewAddressChange('district', value)}
            placeholder={t('business:order.selectDistrict')}
            options={[
              { value: 'Quận 1', label: 'Quận 1' },
              { value: 'Quận 2', label: 'Quận 2' },
              { value: 'Quận 3', label: 'Quận 3' },
            ]}
            fullWidth
          />
        </FormItem>
        
        <FormItem label={t('business:order.ward')} required>
          <Select
            {...(newAddress.ward && { value: newAddress.ward })}
            onChange={(value) => handleNewAddressChange('ward', value)}
            placeholder={t('business:order.selectWard')}
            options={[
              { value: 'Phường Bến Nghé', label: 'Phường Bến Nghé' },
              { value: 'Phường Bến Thành', label: 'Phường Bến Thành' },
              { value: 'Phường Cầu Kho', label: 'Phường Cầu Kho' },
            ]}
            fullWidth
          />
        </FormItem>
      </div>

      <FormItem label={t('business:order.note')}>
        <Input
          value={newAddress.note}
          onChange={(e) => handleNewAddressChange('note', e.target.value)}
          placeholder={t('business:order.notePlaceholder')}
          fullWidth
        />
      </FormItem>
    </div>
  );

  return (
    <Card className={className}>
      <div className="p-4">
        <Typography variant="h6" className="mb-4">
          {t('business:order.deliveryAddress')}
        </Typography>

        {/* Chọn mode */}
        <div className="flex gap-2 mb-4">
          <Button
            variant={addressMode === 'existing' ? 'primary' : 'outline'}
            size="sm"
            onClick={() => handleModeChange('existing')}
            disabled={!customerId}
          >
            <Icon name="map-pin" size="sm" className="mr-2" />
            {t('business:order.useExistingAddress')}
          </Button>
          
          <Button
            variant={addressMode === 'new' ? 'primary' : 'outline'}
            size="sm"
            onClick={() => handleModeChange('new')}
          >
            <Icon name="plus" size="sm" className="mr-2" />
            {t('business:order.addNewAddress')}
          </Button>
        </div>

        <Divider className="my-4" />

        {addressMode === 'existing' ? (
          renderExistingAddresses()
        ) : (
          <div>
            {renderNewAddressForm()}
            <div className="mt-4 flex gap-2">
              <Button
                variant="primary"
                size="sm"
                onClick={handleSaveNewAddress}
                disabled={!newAddress.recipientName || !newAddress.address || !newAddress.province || createAddressMutation.isPending}
              >
                {createAddressMutation.isPending ? (
                  <>
                    <Icon name="loader" size="sm" className="mr-2 animate-spin" />
                    {t('business:order.saving', 'Đang lưu...')}
                  </>
                ) : (
                  <>
                    <Icon name="save" size="sm" className="mr-2" />
                    {t('business:order.saveAddress', 'Lưu địa chỉ')}
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAddressMode('existing')}
              >
                {t('business:order.cancel', 'Hủy')}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default AddressSelector;
