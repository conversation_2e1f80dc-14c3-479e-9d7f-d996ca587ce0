import React from 'react';
import { useAvailableCarriers } from '../../hooks/useAvailableCarriers';
import { Card, Typography } from '@/shared/components/common';

/**
 * Component debug để test carrier loading
 */
const CarrierDebugTest: React.FC = () => {
  const { 
    carriers, 
    isLoading, 
    error, 
    hasActiveCarriers 
  } = useAvailableCarriers();

  console.log('🔍 [CarrierDebugTest] Debug info:', {
    carriers,
    isLoading,
    error,
    hasActiveCarriers,
    carriersLength: carriers.length
  });

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          Carrier Debug Test
        </Typography>
        
        <div className="space-y-2">
          <div>
            <strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Error:</strong> {error || 'None'}
          </div>
          <div>
            <strong>Has Active Carriers:</strong> {hasActiveCarriers ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Carriers Count:</strong> {carriers.length}
          </div>
          
          {carriers.length > 0 && (
            <div>
              <strong>Carriers:</strong>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                {JSON.stringify(carriers, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default CarrierDebugTest;
