import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  Card,
  Typography,
  Button,
  Icon,
  Radio,
  Chip,
  Alert,
  EmptyState,
} from '@/shared/components/common';
import { OrderItemDto } from '../../types/order.types';
import { DeliveryAddressDto } from '../../services/shipping-calculator.service';
import { ShippingCalculatorService } from '../../services/shipping-calculator.service';
import { formatCurrency } from '@/shared/utils/number-format.utils';
import { useAvailableCarriers } from '../../hooks/useAvailableCarriers';
import { useTranslation } from 'react-i18next';
import { useComboShipping, usePhysicalProductsForShipping } from '../../hooks/useComboShipping';

interface CarrierOption {
  id: 'GHN' | 'GHTK';
  name: string;
  description: string;
  icon: string;
  isAvailable: boolean;
  fee?: number;
  serviceType?: string;
  estimatedDeliveryTime?: string;
  error?: string;
  providerId?: string; // ID từ shipping provider configuration
}

interface CarrierSelectorProps {
  shopId: number;
  customerId?: number;
  selectedItems: OrderItemDto[];
  deliveryAddress?: DeliveryAddressDto;
  onCarrierSelected: (carrier: string, fee: number) => void;
  className?: string;
}

/**
 * Component chọn đơn vị vận chuyển với UI đơn giản
 */
const CarrierSelector: React.FC<CarrierSelectorProps> = ({
  shopId,
  customerId,
  selectedItems,
  deliveryAddress,
  onCarrierSelected,
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Lấy danh sách carriers có sẵn từ shipping provider configurations
  const {
    carriers: availableCarriers,
    isLoading: carriersLoading,
    error: carriersError,
    hasActiveCarriers
  } = useAvailableCarriers();

  // State
  const [selectedCarrier, setSelectedCarrier] = useState<string>('');
  const [isCalculating, setIsCalculating] = useState(false);
  const [carriers, setCarriers] = useState<CarrierOption[]>([]);

  // Ref để lưu callback và tránh dependency loop
  const onCarrierSelectedRef = useRef(onCarrierSelected);
  onCarrierSelectedRef.current = onCarrierSelected;

  // Kiểm tra có sản phẩm cần vận chuyển không (bao gồm combo)
  const { needsShipping: hasPhysicalProducts, isLoading: checkingShipping } = useComboShipping(selectedItems);

  // Lấy danh sách sản phẩm vật lý để tính phí ship
  const { physicalProducts, isLoading: loadingPhysicalProducts } = usePhysicalProductsForShipping(selectedItems);

  // Initialize carriers từ available carriers
  useEffect(() => {
    console.log('🔍 [CarrierSelector] Available carriers:', availableCarriers);
    console.log('🔍 [CarrierSelector] Has active carriers:', hasActiveCarriers);
    console.log('🔍 [CarrierSelector] Loading:', carriersLoading);
    console.log('🔍 [CarrierSelector] Error:', carriersError);

    if (availableCarriers.length > 0) {
      const initialCarriers: CarrierOption[] = availableCarriers.map(carrier => ({
        id: carrier.type,
        name: carrier.displayName,
        description: carrier.description,
        icon: carrier.icon,
        isAvailable: false,
        providerId: carrier.id,
      }));
      console.log('✅ [CarrierSelector] Setting initial carriers:', initialCarriers);
      setCarriers(initialCarriers);
    }
  }, [availableCarriers, hasActiveCarriers, carriersLoading, carriersError]);

  // Tính phí vận chuyển cho một carrier
  const calculateCarrierFee = useCallback(async (carrierId: 'GHN' | 'GHTK') => {
    if (!deliveryAddress || !hasPhysicalProducts || selectedItems.length === 0) {
      return null;
    }

    try {
      // Sử dụng danh sách sản phẩm vật lý thực tế (bao gồm từ combo)
      const productsForShipping = physicalProducts.length > 0
        ? physicalProducts
        : selectedItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
          }));

      const request = ShippingCalculatorService.createCalculateShippingRequest(
        shopId,
        productsForShipping,
        customerId,
        deliveryAddress,
        carrierId
      );

      console.log(`🚚 [CarrierSelector] Calculating fee for ${carrierId}:`, request);

      const response = await ShippingCalculatorService.calculateShippingFee(request);

      // Kiểm tra response format - có thể là ApiResponseDto hoặc API trực tiếp
      const isApiResponseDto = 'code' in response && 'result' in response;
      const isDirectApi = 'success' in response && 'data' in response;

      let shippingData: Record<string, unknown> | null = null;
      let isSuccess = false;

      if (isApiResponseDto) {
        isSuccess = response.code === 0 && response.result;
        shippingData = response.result;
      } else if (isDirectApi) {
        isSuccess = (response as Record<string, unknown>).success && (response as Record<string, unknown>).data;
        shippingData = (response as Record<string, unknown>).data;
      }

      if (isSuccess && shippingData) {
        return {
          fee: shippingData.fee || shippingData.shippingFee,
          serviceType: shippingData.serviceType,
          estimatedDeliveryTime: shippingData.estimatedDeliveryTime,
        };
      } else {
        const errorMessage = isApiResponseDto
          ? response.message || 'Calculation failed'
          : (response as Record<string, unknown>).message || 'Calculation failed';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error(`❌ [CarrierSelector] Error calculating ${carrierId}:`, error);
      return {
        error: error instanceof Error ? error.message : 'Calculation failed',
      };
    }
  }, [shopId, customerId, selectedItems, deliveryAddress, hasPhysicalProducts, physicalProducts]);

  // Tính phí cho tất cả carriers
  const calculateAllCarriers = useCallback(async () => {
    if (!deliveryAddress || !hasPhysicalProducts || selectedItems.length === 0 || carriers.length === 0) {
      return;
    }

    setIsCalculating(true);

    try {
      // Chỉ tính phí cho các carriers có sẵn trong configuration
      const carrierTypes = carriers.map(c => c.id);
      const results = await Promise.allSettled(
        carrierTypes.map(carrierId => calculateCarrierFee(carrierId))
      );

      const updatedCarriers = carriers.map((carrier, index) => {
        const result = results[index];

        if (result.status === 'fulfilled' && result.value) {
          if (result.value.error) {
            return {
              ...carrier,
              isAvailable: false,
              error: result.value.error,
            };
          } else {
            return {
              ...carrier,
              isAvailable: true,
              fee: result.value.fee,
              serviceType: result.value.serviceType,
              estimatedDeliveryTime: result.value.estimatedDeliveryTime,
              error: undefined,
            };
          }
        } else {
          return {
            ...carrier,
            isAvailable: false,
            error: 'Calculation failed',
          };
        }
      });

      setCarriers(updatedCarriers);

      // Tự động chọn carrier có phí thấp nhất
      const availableCarriersWithFee = updatedCarriers.filter(c => c.isAvailable && c.fee !== undefined);
      if (availableCarriersWithFee.length > 0) {
        const cheapestCarrier = availableCarriersWithFee.reduce((prev, current) =>
          (prev.fee! < current.fee!) ? prev : current
        );
        setSelectedCarrier(cheapestCarrier.id);
        onCarrierSelectedRef.current(cheapestCarrier.id, cheapestCarrier.fee!);
      }

    } catch (error) {
      console.error('❌ [CarrierSelector] Error in calculation:', error);
    } finally {
      setIsCalculating(false);
    }
  }, [deliveryAddress, hasPhysicalProducts, selectedItems, calculateCarrierFee, carriers]);

  // Auto-calculate khi có thay đổi với debounce
  useEffect(() => {
    if (deliveryAddress && hasPhysicalProducts && selectedItems.length > 0 && carriers.length > 0) {
      // Debounce để tránh gọi API quá nhiều lần
      const timeoutId = setTimeout(() => {
        calculateAllCarriers();
      }, 500); // Đợi 500ms sau thay đổi cuối cùng

      return () => clearTimeout(timeoutId);
    }
  }, [calculateAllCarriers, deliveryAddress, hasPhysicalProducts, selectedItems.length, carriers.length]);

  // Xử lý chọn carrier
  const handleCarrierSelect = (carrierId: string) => {
    const carrier = carriers.find(c => c.id === carrierId);
    if (carrier && carrier.isAvailable && carrier.fee !== undefined) {
      setSelectedCarrier(carrierId);
      onCarrierSelectedRef.current(carrierId, carrier.fee);
    }
  };

  // Không hiển thị nếu không có sản phẩm cần vận chuyển
  if (!hasPhysicalProducts) {
    return null;
  }

  // Loading state khi đang kiểm tra shipping needs
  if (checkingShipping || loadingPhysicalProducts) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              {t('business:order.selectCarrier', 'Chọn đơn vị vận chuyển')}
            </Typography>
          </div>
          <div className="flex items-center gap-2 p-4 bg-blue-50 rounded-lg">
            <Icon name="loader" size="sm" className="animate-spin text-blue-600" />
            <Typography variant="body2" className="text-blue-800">
              {t('business:order.checkingShippingNeeds', 'Đang kiểm tra nhu cầu vận chuyển...')}
            </Typography>
          </div>
        </div>
      </Card>
    );
  }

  // Loading state cho carriers
  if (carriersLoading) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              {t('business:order.selectCarrier', 'Chọn đơn vị vận chuyển')}
            </Typography>
          </div>
          <div className="flex items-center gap-2 p-4 bg-blue-50 rounded-lg">
            <Icon name="loader" size="sm" className="animate-spin text-blue-600" />
            <Typography variant="body2" className="text-blue-800">
              {t('business:order.loadingCarriers', 'Đang tải danh sách nhà vận chuyển...')}
            </Typography>
          </div>
        </div>
      </Card>
    );
  }

  // Error state cho carriers
  if (carriersError) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              {t('business:order.selectCarrier', 'Chọn đơn vị vận chuyển')}
            </Typography>
          </div>
          <Alert
            type="error"
            title={t('business:order.carriersLoadError', 'Lỗi tải danh sách nhà vận chuyển')}
            description={carriersError}
          />
        </div>
      </Card>
    );
  }

  // Empty state - không có carriers được cấu hình
  if (!hasActiveCarriers) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              Chọn đơn vị vận chuyển
            </Typography>
          </div>
          <EmptyState
            icon="truck"
            title={t('business:order.noCarriersConfigured', 'Chưa có nhà vận chuyển nào được cấu hình')}
            description={t('business:order.noCarriersDescription', 'Vui lòng cấu hình ít nhất một nhà vận chuyển để sử dụng tính năng tính phí vận chuyển tự động.')}
            action={
              <Button
                variant="primary"
                size="sm"
                onClick={() => {
                  // TODO: Navigate to shipping configuration page
                  console.log('Navigate to shipping configuration');
                }}
              >
                <Icon name="settings" size="sm" className="mr-2" />
                {t('business:order.configureCarriers', 'Cấu hình nhà vận chuyển')}
              </Button>
            }
          />
        </div>
      </Card>
    );
  }

  // Loading state
  if (isCalculating) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              Chọn đơn vị vận chuyển
            </Typography>
          </div>
          <div className="flex items-center gap-2 p-4 bg-blue-50 rounded-lg">
            <Icon name="loader" size="sm" className="animate-spin text-blue-600" />
            <Typography variant="body2" className="text-blue-800">
              {t('business:order.calculatingShipping', 'Đang tính phí vận chuyển...')}
            </Typography>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              {t('business:order.selectCarrier', 'Chọn đơn vị vận chuyển')}
            </Typography>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={calculateAllCarriers}
            disabled={isCalculating}
          >
            <Icon name="refresh-cw" size="sm" className="mr-2" />
            {t('business:order.recalculate', 'Tính lại')}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {carriers.map((carrier) => (
            <div
              key={carrier.id}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedCarrier === carrier.id
                  ? 'border-blue-500 bg-blue-50'
                  : carrier.isAvailable
                  ? 'border-gray-200 hover:border-gray-300'
                  : 'border-gray-100 bg-gray-50'
              } ${!carrier.isAvailable ? 'opacity-60' : ''}`}
              onClick={() => carrier.isAvailable && handleCarrierSelect(carrier.id)}
            >
              <div className="flex items-start gap-3">
                <Radio
                  checked={selectedCarrier === carrier.id}
                  onChange={() => handleCarrierSelect(carrier.id)}
                  disabled={!carrier.isAvailable}
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon name={carrier.icon} size="sm" className="text-gray-600" />
                    <Typography variant="subtitle2" className="font-medium">
                      {carrier.name}
                    </Typography>
                    {carrier.isAvailable && carrier.fee !== undefined && (
                      <Chip variant="success" size="sm">
                        {formatCurrency(carrier.fee)}
                      </Chip>
                    )}
                  </div>
                  
                  <Typography variant="body2" className="text-gray-600 mb-2">
                    {carrier.description}
                  </Typography>

                  {carrier.isAvailable ? (
                    <div className="space-y-1">
                      {carrier.serviceType && (
                        <Typography variant="caption" className="text-gray-500">
                          {t('business:order.serviceType', 'Dịch vụ')}: {carrier.serviceType}
                        </Typography>
                      )}
                      {carrier.estimatedDeliveryTime && (
                        <Typography variant="caption" className="text-gray-500">
                          {t('business:order.estimatedTime', 'Thời gian')}: {carrier.estimatedDeliveryTime}
                        </Typography>
                      )}
                    </div>
                  ) : (
                    <div>
                      <Chip variant="danger" size="sm">
                        {t('business:order.notAvailable', 'Không khả dụng')}
                      </Chip>
                      {carrier.error && (
                        <Typography variant="caption" className="text-red-600 block mt-1">
                          {carrier.error.includes('Request failed with status code 400')
                            ? t('business:order.apiConnectionError', 'Lỗi kết nối API')
                            : carrier.error}
                        </Typography>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Hiển thị kết quả đã chọn */}
        {selectedCarrier && (
          <div className="mt-4 p-4 bg-green-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Icon name="check-circle" size="sm" className="text-green-600" />
              <Typography variant="subtitle2" className="text-green-800">
                {t('business:order.carrierSelected', 'Đơn vị vận chuyển đã chọn')}
              </Typography>
            </div>
            <Typography variant="body2" className="text-green-700">
              {carriers.find(c => c.id === selectedCarrier)?.name} - {' '}
              {formatCurrency(carriers.find(c => c.id === selectedCarrier)?.fee || 0)}
            </Typography>
          </div>
        )}

        {/* Trường hợp chưa có carrier nào khả dụng */}
        {carriers.every(c => !c.isAvailable) && !isCalculating && (
          <div className="text-center py-8">
            <Icon name="truck" size="lg" className="mx-auto mb-4 text-gray-400" />
            <Typography variant="body1" className="text-gray-500 mb-2">
              {t('business:order.noCarriersAvailable', 'Không có đơn vị vận chuyển khả dụng')}
            </Typography>
            <Typography variant="body2" className="text-gray-400">
              {t('business:order.checkDeliveryAddress', 'Vui lòng kiểm tra lại địa chỉ giao hàng')}
            </Typography>
          </div>
        )}
      </div>
    </Card>
  );
};

export default CarrierSelector;
