import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Icon,
  Table,
  Chip,
  Radio,
} from '@/shared/components/common';
import { OrderItemDto } from '../../types/order.types';
import { DeliveryAddressDto } from '../../services/shipping-calculator.service';
import { ShippingCalculatorService } from '../../services/shipping-calculator.service';
import { formatCurrency } from '@/shared/utils/format';

interface CarrierQuote {
  carrier: 'GHN' | 'GHTK';
  fee: number;
  serviceType: string;
  estimatedDeliveryTime?: string;
  isAvailable: boolean;
  error?: string;
}

interface MultiCarrierShippingCalculatorProps {
  shopId: number;
  customerId?: number;
  selectedItems: OrderItemDto[];
  deliveryAddress?: DeliveryAddressDto;
  onCarrierSelected: (carrier: CarrierQuote) => void;
  onCalculationComplete: (quotes: CarrierQuote[]) => void;
  className?: string;
}

/**
 * Component tính phí vận chuyển từ nhiều nhà vận chuyển và so sánh
 */
const MultiCarrierShippingCalculator: React.FC<MultiCarrierShippingCalculatorProps> = ({
  shopId,
  customerId,
  selectedItems,
  deliveryAddress,
  onCarrierSelected,
  onCalculationComplete,
  className = '',
}) => {

  // State
  const [quotes, setQuotes] = useState<CarrierQuote[]>([]);
  const [selectedCarrier, setSelectedCarrier] = useState<string>('');
  const [isCalculating, setIsCalculating] = useState(false);
  const [calculationError, setCalculationError] = useState<string>('');

  // Kiểm tra có sản phẩm cần vận chuyển không
  const hasPhysicalProducts = selectedItems.some(() =>
    // Giả sử có thông tin productType trong item hoặc cần fetch từ API
    true // Tạm thời return true, cần implement logic kiểm tra productType
  );

  // Tính phí vận chuyển từ tất cả carriers
  const calculateAllCarriers = useCallback(async () => {
    if (!deliveryAddress || !hasPhysicalProducts || selectedItems.length === 0) {
      return;
    }

    setIsCalculating(true);
    setCalculationError('');

    const carriers: ('GHN' | 'GHTK')[] = ['GHN', 'GHTK'];
    const newQuotes: CarrierQuote[] = [];

    try {
      // Tính phí song song cho tất cả carriers
      const results = await Promise.allSettled(
        carriers.map(async (carrier) => {
          try {
            const request = ShippingCalculatorService.createCalculateShippingRequest(
              shopId,
              selectedItems.map(item => ({
                productId: item.productId,
                quantity: item.quantity,
              })),
              customerId,
              deliveryAddress,
              carrier
            );

            const response = await ShippingCalculatorService.calculateShippingFee(request);

            console.log(`✅ [MultiCarrierShippingCalculator] ${carrier} response:`, response);

            if (response.code === 0 && response.result) {
              return {
                carrier,
                fee: response.result.fee || 0,
                serviceType: response.result.serviceType || 'Standard',
                estimatedDeliveryTime: response.result.estimatedDeliveryTime,
                isAvailable: true,
              } as CarrierQuote;
            } else {
              throw new Error(response.message || `${carrier} calculation failed`);
            }
          } catch (error) {
            console.error(`Error calculating ${carrier} shipping:`, error);
            return {
              carrier,
              fee: 0,
              serviceType: '',
              isAvailable: false,
              error: error instanceof Error ? error.message : 'Calculation failed',
            } as CarrierQuote;
          }
        })
      );

      // Xử lý kết quả
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          newQuotes.push(result.value);
        } else {
          newQuotes.push({
            carrier: carriers[index],
            fee: 0,
            serviceType: '',
            isAvailable: false,
            error: result.reason?.message || 'Unknown error',
          });
        }
      });

      setQuotes(newQuotes);
      onCalculationComplete(newQuotes);

      // Tự động chọn carrier rẻ nhất nếu có
      const availableQuotes = newQuotes.filter(q => q.isAvailable);
      if (availableQuotes.length > 0) {
        const cheapest = availableQuotes.reduce((prev, current) => 
          prev.fee < current.fee ? prev : current
        );
        setSelectedCarrier(cheapest.carrier);
        onCarrierSelected(cheapest);
      }

    } catch (error) {
      console.error('Error in multi-carrier calculation:', error);
      setCalculationError(error instanceof Error ? error.message : 'Calculation failed');
    } finally {
      setIsCalculating(false);
    }
  }, [shopId, customerId, selectedItems, deliveryAddress, hasPhysicalProducts, onCalculationComplete, onCarrierSelected]);

  // Auto-calculate khi có thay đổi
  useEffect(() => {
    if (deliveryAddress && hasPhysicalProducts && selectedItems.length > 0) {
      calculateAllCarriers();
    }
  }, [calculateAllCarriers, deliveryAddress, hasPhysicalProducts, selectedItems.length]);

  // Xử lý chọn carrier
  const handleCarrierSelect = useCallback((carrier: string) => {
    console.log('🚚 [MultiCarrierShippingCalculator] Carrier selected:', carrier);

    const selectedQuote = quotes.find(q => q.carrier === carrier);
    if (selectedQuote && selectedQuote.isAvailable) {
      setSelectedCarrier(carrier);
      onCarrierSelected(selectedQuote);
      console.log('🚚 [MultiCarrierShippingCalculator] Quote selected:', selectedQuote);
    } else {
      console.warn('🚚 [MultiCarrierShippingCalculator] No available quote found for carrier:', carrier);
    }
  }, [quotes, onCarrierSelected]);

  // Render loading state
  if (isCalculating) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              Tính phí vận chuyển
            </Typography>
          </div>
          <div className="flex items-center gap-2 p-4 bg-blue-50 rounded-lg">
            <Icon name="loader" size="sm" className="animate-spin text-blue-600" />
            <Typography variant="body2" className="text-blue-800">
              Đang tính phí từ nhiều đơn vị vận chuyển...
            </Typography>
          </div>
        </div>
      </Card>
    );
  }

  // Render error state
  if (calculationError) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-red-600" />
            <Typography variant="h6">
              Tính phí vận chuyển
            </Typography>
          </div>
          <div className="p-4 bg-red-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Icon name="alert-circle" size="sm" className="text-red-600" />
              <Typography variant="subtitle2" className="text-red-800">
                Lỗi tính phí vận chuyển
              </Typography>
            </div>
            <Typography variant="body2" className="text-red-600 mb-3">
              {calculationError}
            </Typography>
            <Button
              variant="outline"
              size="sm"
              onClick={calculateAllCarriers}
            >
              <Icon name="refresh-cw" size="sm" className="mr-2" />
              Thử lại
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  // Không hiển thị nếu không có sản phẩm cần vận chuyển
  if (!hasPhysicalProducts) {
    return null;
  }

  // Render comparison table
  const tableColumns = [
    {
      key: 'carrier',
      title: 'Đơn vị vận chuyển',
      render: (quote: CarrierQuote) => (
        <div className="flex items-center gap-2">
          <Radio
            checked={selectedCarrier === quote.carrier}
            onChange={() => handleCarrierSelect(quote.carrier)}
            disabled={!quote.isAvailable}
          />
          <div>
            <Typography variant="subtitle2" className={quote.isAvailable ? '' : 'text-gray-400'}>
              {quote.carrier === 'GHN' ? 'Giao Hàng Nhanh' : 'Giao Hàng Tiết Kiệm'}
            </Typography>
            <Typography variant="caption" className="text-gray-500">
              {quote.carrier}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'fee',
      title: 'Phí vận chuyển',
      render: (quote: CarrierQuote) => (
        <div>
          {quote.isAvailable ? (
            <div className="flex items-center gap-2">
              <Typography variant="subtitle2" className="font-semibold">
                {formatCurrency(quote.fee)}
              </Typography>
              {quote.fee === Math.min(...quotes.filter(q => q.isAvailable).map(q => q.fee)) && (
                <Chip variant="success" size="sm">
                  Rẻ nhất
                </Chip>
              )}
            </div>
          ) : (
            <Typography variant="body2" className="text-red-600">
              Không khả dụng
            </Typography>
          )}
        </div>
      ),
    },
    {
      key: 'service',
      title: 'Loại dịch vụ',
      render: (quote: CarrierQuote) => (
        <Typography variant="body2" className={quote.isAvailable ? '' : 'text-gray-400'}>
          {quote.isAvailable ? (quote.serviceType || 'Chuẩn') : '-'}
        </Typography>
      ),
    },
    {
      key: 'delivery',
      title: 'Thời gian giao hàng',
      render: (quote: CarrierQuote) => (
        <Typography variant="body2" className={quote.isAvailable ? '' : 'text-gray-400'}>
          {quote.isAvailable && quote.estimatedDeliveryTime ? quote.estimatedDeliveryTime : '-'}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: 'Trạng thái',
      render: (quote: CarrierQuote) => (
        <div>
          {quote.isAvailable ? (
            <Chip variant="success" size="sm">
              Khả dụng
            </Chip>
          ) : (
            <div>
              <Chip variant="danger" size="sm">
                Không khả dụng
              </Chip>
              {quote.error && (
                <Typography variant="caption" className="text-red-600 block mt-1">
                  {quote.error.includes('Request failed with status code 400')
                    ? 'Lỗi kết nối API'
                    : quote.error}
                </Typography>
              )}
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <Card className={className}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              So sánh phí vận chuyển
            </Typography>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={calculateAllCarriers}
            disabled={isCalculating}
          >
            <Icon name="refresh-cw" size="sm" className="mr-2" />
            Tính lại
          </Button>
        </div>

        {quotes.length > 0 ? (
          <div className="space-y-4">
            <Table
              data={quotes}
              columns={tableColumns}
              showPagination={false}
            />

            {selectedCarrier && (
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Icon name="check-circle" size="sm" className="text-green-600" />
                  <Typography variant="subtitle2" className="text-green-800">
                    Đơn vị vận chuyển đã chọn
                  </Typography>
                </div>
                <Typography variant="body2" className="text-green-700">
                  {selectedCarrier === 'GHN' ? 'Giao Hàng Nhanh' : 'Giao Hàng Tiết Kiệm'} - {' '}
                  {formatCurrency(quotes.find(q => q.carrier === selectedCarrier)?.fee || 0)}
                </Typography>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon name="truck" size="lg" className="mx-auto mb-4 text-gray-400" />
            <Typography variant="body1" className="text-gray-500 mb-2">
              Chưa có báo giá vận chuyển
            </Typography>
            <Typography variant="body2" className="text-gray-400">
              Chọn địa chỉ để tính phí vận chuyển
            </Typography>
          </div>
        )}
      </div>
    </Card>
  );
};

export default MultiCarrierShippingCalculator;
