import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Radio,
  EmptyState,
  Alert,
} from '@/shared/components/common';
import { useAvailableCarriers, AvailableCarrier } from '../../hooks/useAvailableCarriers';

interface PreferredCarrierSelectorProps {
  selectedCarrier?: 'GHN' | 'GHTK';
  onCarrierChange: (carrier: 'GHN' | 'GHTK') => void;
  className?: string;
  disabled?: boolean;
}

/**
 * Component để chọn nhà vận chuyển ưu tiên từ danh sách providers đã cấu hình
 */
const PreferredCarrierSelector: React.FC<PreferredCarrierSelectorProps> = ({
  selectedCarrier,
  onCarrierChange,
  className = '',
  disabled = false,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // L<PERSON>y danh sách carriers có sẵn
  const { 
    carriers, 
    isLoading, 
    error, 
    hasActiveCarriers 
  } = useAvailableCarriers();

  // Xử lý chọn carrier
  const handleCarrierSelect = useCallback((carrier: AvailableCarrier) => {
    if (!disabled) {
      console.log('🚚 [PreferredCarrierSelector] Selected carrier:', carrier);
      onCarrierChange(carrier.type);
    }
  }, [onCarrierChange, disabled]);

  // Render loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <div className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('business:order.preferredCarrier', 'Nhà vận chuyển ưu tiên')}
          </Typography>
          <div className="flex items-center justify-center py-8">
            <Icon name="loader" size="lg" className="animate-spin text-gray-400" />
            <Typography variant="body2" className="ml-2 text-gray-500">
              {t('business:order.loadingCarriers', 'Đang tải danh sách nhà vận chuyển...')}
            </Typography>
          </div>
        </div>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card className={className}>
        <div className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('business:order.preferredCarrier', 'Nhà vận chuyển ưu tiên')}
          </Typography>
          <Alert 
            type="error" 
            title={t('business:order.carrierLoadError', 'Lỗi tải danh sách nhà vận chuyển')}
            description={error}
          />
        </div>
      </Card>
    );
  }

  // Render empty state
  if (!hasActiveCarriers) {
    return (
      <Card className={className}>
        <div className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('business:order.preferredCarrier', 'Nhà vận chuyển ưu tiên')}
          </Typography>
          <EmptyState
            icon="truck"
            title={t('business:order.noCarriersConfigured', 'Chưa có nhà vận chuyển nào được cấu hình')}
            description={t('business:order.noCarriersDescription', 'Vui lòng cấu hình ít nhất một nhà vận chuyển để sử dụng tính năng tính phí vận chuyển tự động.')}
            action={
              <Button
                variant="primary"
                size="sm"
                onClick={() => {
                  // TODO: Navigate to shipping configuration page
                  console.log('Navigate to shipping configuration');
                }}
              >
                <Icon name="settings" size="sm" className="mr-2" />
                {t('business:order.configureCarriers', 'Cấu hình nhà vận chuyển')}
              </Button>
            }
          />
        </div>
      </Card>
    );
  }

  // Render carrier selection
  return (
    <Card className={className}>
      <div className="p-4">
        <Typography variant="h6" className="mb-4">
          {t('business:order.preferredCarrier', 'Nhà vận chuyển ưu tiên')}
        </Typography>

        <Typography variant="body2" className="text-gray-600 mb-4">
          {t('business:order.preferredCarrierDescription', 'Chọn nhà vận chuyển ưu tiên để tính phí vận chuyển tự động')}
        </Typography>

        <div className="space-y-3">
          {carriers.map((carrier) => (
            <div
              key={carrier.id}
              className={`
                border rounded-lg p-4 cursor-pointer transition-all duration-200
                ${selectedCarrier === carrier.type 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onClick={() => handleCarrierSelect(carrier)}
            >
              <div className="flex items-center">
                <Radio
                  checked={selectedCarrier === carrier.type}
                  onChange={() => handleCarrierSelect(carrier)}
                  disabled={disabled}
                  className="mr-3"
                />
                
                <div className="flex items-center flex-1">
                  <div className={`
                    w-10 h-10 rounded-lg flex items-center justify-center mr-3
                    ${carrier.type === 'GHN' ? 'bg-blue-100' : 'bg-green-100'}
                  `}>
                    <Icon 
                      name={carrier.icon} 
                      size="md" 
                      className={carrier.type === 'GHN' ? 'text-blue-600' : 'text-green-600'} 
                    />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center">
                      <Typography variant="subtitle1" className="font-medium">
                        {carrier.displayName}
                      </Typography>
                      <Typography variant="body2" className="text-gray-500 ml-2">
                        ({carrier.name})
                      </Typography>
                    </div>
                    <Typography variant="body2" className="text-gray-600 mt-1">
                      {carrier.description}
                    </Typography>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {!selectedCarrier && (
          <Alert 
            type="info" 
            className="mt-4"
            title={t('business:order.selectCarrierRequired', 'Vui lòng chọn nhà vận chuyển')}
            description={t('business:order.selectCarrierRequiredDescription', 'Bạn cần chọn nhà vận chuyển ưu tiên để hệ thống có thể tính phí vận chuyển tự động.')}
          />
        )}
      </div>
    </Card>
  );
};

export default PreferredCarrierSelector;
