import React from 'react';
import {
  Card,
  Typography,
  Radio,
  Chip,
  Icon,
  Button,
} from '@/shared/components/common';
import { formatCurrency } from '@/shared/utils/format';

interface CarrierQuote {
  carrier: 'GHN' | 'GHTK';
  fee: number;
  serviceType: string;
  estimatedDeliveryTime?: string;
  isAvailable: boolean;
  error?: string;
}

interface ShippingMethodSelectorProps {
  quotes: CarrierQuote[];
  selectedCarrier: string;
  onCarrierSelect: (carrier: string) => void;
  onRetryCalculation?: () => void;
  isCalculating?: boolean;
  className?: string;
}

/**
 * Component chọn phương thức vận chuyển dạng card
 */
const ShippingMethodSelector: React.FC<ShippingMethodSelectorProps> = ({
  quotes,
  selectedCarrier,
  onCarrierSelect,
  onRetryCalculation,
  isCalculating = false,
  className = '',
}) => {

  // Tìm carrier rẻ nhất
  const cheapestFee = Math.min(...quotes.filter(q => q.isAvailable).map(q => q.fee));

  if (isCalculating) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              Chọn phương thức vận chuyển
            </Typography>
          </div>
          <div className="flex items-center gap-2 p-4 bg-blue-50 rounded-lg">
            <Icon name="loader" size="sm" className="animate-spin text-blue-600" />
            <Typography variant="body2" className="text-blue-800">
              Đang tính phí vận chuyển...
            </Typography>
          </div>
        </div>
      </Card>
    );
  }

  if (quotes.length === 0) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="truck" size="md" className="text-gray-600" />
            <Typography variant="h6">
              Chọn phương thức vận chuyển
            </Typography>
          </div>
          <div className="text-center py-8">
            <Icon name="truck" size="lg" className="mx-auto mb-4 text-gray-400" />
            <Typography variant="body1" className="text-gray-500 mb-2">
              Chưa có phương thức vận chuyển
            </Typography>
            <Typography variant="body2" className="text-gray-400">
              Vui lòng chọn địa chỉ giao hàng để xem các phương thức có sẵn
            </Typography>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Icon name="truck" size="md" className="text-blue-600" />
            <Typography variant="h6">
              Chọn phương thức vận chuyển
            </Typography>
          </div>
          {quotes.some(q => !q.isAvailable) && onRetryCalculation && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetryCalculation}
              disabled={isCalculating}
            >
              <Icon name="refresh-cw" size="sm" className="mr-2" />
              Thử lại
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quotes.map((quote) => (
            <div
              key={quote.carrier}
              className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                quote.isAvailable
                  ? selectedCarrier === quote.carrier
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                  : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-60'
              }`}
              onClick={() => quote.isAvailable && onCarrierSelect(quote.carrier)}
            >
              {/* Radio button */}
              <div className="absolute top-4 right-4">
                <Radio
                  checked={selectedCarrier === quote.carrier}
                  onChange={() => quote.isAvailable && onCarrierSelect(quote.carrier)}
                  disabled={!quote.isAvailable}
                />
              </div>

              {/* Carrier info */}
              <div className="pr-8">
                <div className="flex items-center gap-3 mb-3">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    quote.carrier === 'GHN' ? 'bg-orange-100' : 'bg-green-100'
                  }`}>
                    <Typography variant="subtitle2" className={
                      quote.carrier === 'GHN' ? 'text-orange-600' : 'text-green-600'
                    }>
                      {quote.carrier}
                    </Typography>
                  </div>
                  <div>
                    <Typography variant="subtitle1" className="font-semibold">
                      {quote.carrier === 'GHN' ? 'Giao Hàng Nhanh' : 'Giao Hàng Tiết Kiệm'}
                    </Typography>
                    <Typography variant="caption" className="text-gray-500">
                      {quote.serviceType || 'Chuẩn'}
                    </Typography>
                  </div>
                </div>

                {/* Fee and status */}
                {quote.isAvailable ? (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Typography variant="h6" className="font-bold text-blue-600">
                        {formatCurrency(quote.fee)}
                      </Typography>
                      {quote.fee === cheapestFee && (
                        <Chip variant="success" size="sm">
                          Rẻ nhất
                        </Chip>
                      )}
                    </div>
                    {quote.estimatedDeliveryTime && (
                      <Typography variant="body2" className="text-gray-600">
                        <Icon name="clock" size="sm" className="inline mr-1" />
                        {quote.estimatedDeliveryTime}
                      </Typography>
                    )}
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Chip variant="danger" size="sm">
                      Không khả dụng
                    </Chip>
                    {quote.error && (
                      <Typography variant="caption" className="text-red-600 block">
                        {quote.error.includes('Request failed with status code 500')
                          ? 'Lỗi máy chủ - Vui lòng thử lại sau'
                          : quote.error.includes('Request failed with status code 400')
                          ? 'Thông tin không hợp lệ'
                          : quote.error.includes('route not found service')
                          ? 'Không hỗ trợ tuyến đường này'
                          : quote.error.includes('Cannot read properties of undefined')
                          ? 'Dữ liệu phản hồi không hợp lệ'
                          : quote.error}
                      </Typography>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Selected carrier summary */}
        {selectedCarrier && (
          <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <Icon name="check-circle" size="sm" className="text-green-600" />
              <Typography variant="subtitle2" className="text-green-800">
                Phương thức vận chuyển đã chọn
              </Typography>
            </div>
            <Typography variant="body2" className="text-green-700">
              {selectedCarrier === 'GHN' ? 'Giao Hàng Nhanh' : 'Giao Hàng Tiết Kiệm'} - {' '}
              {formatCurrency(quotes.find(q => q.carrier === selectedCarrier)?.fee || 0)}
            </Typography>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ShippingMethodSelector;
