import { useMemo } from 'react';
import { useShippingProviderConfigurations } from '@/modules/integration/shipping/hooks';
import { ShippingProviderConfiguration } from '@/modules/integration/shipping/types';

export interface AvailableCarrier {
  id: string;
  name: string;
  type: 'GHN' | 'GHTK';
  displayName: string;
  description: string;
  icon: string;
  isActive: boolean;
  configuration: ShippingProviderConfiguration;
}

export interface UseAvailableCarriersReturn {
  carriers: AvailableCarrier[];
  isLoading: boolean;
  error: string | null;
  hasActiveCarriers: boolean;
  getCarrierByType: (type: 'GHN' | 'GHTK') => AvailableCarrier | undefined;
}

/**
 * Hook để lấy danh sách carriers có sẵn từ shipping provider configurations
 */
export const useAvailableCarriers = (): UseAvailableCarriersReturn => {
  // Fetch shipping provider configurations
  const { 
    data: providersResponse, 
    isLoading, 
    error 
  } = useShippingProviderConfigurations({
    isActive: true, // Chỉ lấy providers đang active
    limit: 100, // Lấy tất cả
  });

  // Transform providers thành available carriers
  const carriers = useMemo(() => {
    console.log('🔍 [useAvailableCarriers] Raw response:', providersResponse);

    // Kiểm tra nhiều cấu trúc response có thể có
    let items = null;

    // Trường hợp 1: Standard ApiResponseDto format
    if (providersResponse?.result?.items) {
      items = providersResponse.result.items;
      console.log('📦 [useAvailableCarriers] Using result.items structure');
    }
    // Trường hợp 2: Direct items trong response
    else if (providersResponse?.items) {
      items = providersResponse.items;
      console.log('📦 [useAvailableCarriers] Using direct items structure');
    }
    // Trường hợp 3: result là array trực tiếp
    else if (Array.isArray(providersResponse?.result)) {
      items = providersResponse.result;
      console.log('📦 [useAvailableCarriers] Using result as array');
    }
    // Trường hợp 4: response là array trực tiếp
    else if (Array.isArray(providersResponse)) {
      items = providersResponse;
      console.log('📦 [useAvailableCarriers] Using response as array');
    }

    if (!items || !Array.isArray(items)) {
      console.log('❌ [useAvailableCarriers] No items found in response, items:', items);
      return [];
    }

    console.log('📦 [useAvailableCarriers] Items found:', items);

    const transformedCarriers = items
      .filter(provider => {
        const isValidType = provider.type === 'GHN' || provider.type === 'GHTK';
        console.log(`🔍 [useAvailableCarriers] Provider ${provider.name} (${provider.type}): isValidType=${isValidType}`);
        return isValidType;
      })
      .map(provider => {
        const carrier = {
          id: provider.id.toString(),
          name: provider.name,
          type: provider.type as 'GHN' | 'GHTK',
          displayName: provider.type === 'GHN' ? 'Giao Hàng Nhanh' : 'Giao Hàng Tiết Kiệm',
          description: provider.type === 'GHN'
            ? 'Mạng lưới giao hàng rộng khắp toàn quốc'
            : 'Dịch vụ giao hàng nhanh chóng với chi phí tiết kiệm',
          icon: 'truck',
          isActive: provider.isActive !== false, // Default to true if not specified
          configuration: provider,
        };
        console.log(`✅ [useAvailableCarriers] Transformed carrier:`, carrier);
        return carrier;
      });

    console.log('✅ [useAvailableCarriers] Final transformed carriers:', transformedCarriers);
    return transformedCarriers;
  }, [providersResponse]);

  // Helper function để tìm carrier theo type
  const getCarrierByType = useMemo(() => {
    return (type: 'GHN' | 'GHTK') => {
      return carriers.find(carrier => carrier.type === type);
    };
  }, [carriers]);

  return {
    carriers,
    isLoading,
    error: error?.message || null,
    hasActiveCarriers: carriers.length > 0,
    getCarrierByType,
  };
};
