import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../services/product.service';
import { CreateProductDto } from '../types/product.dto';
import { PRODUCT_QUERY_KEYS } from './useProductQuery';

export interface CreateDigitalProductResponse {
  id: number;
  name: string;
  productType: string;
  uploadUrls?: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
    // Upload URLs cho variants trong advancedInfo.variantMetadata
    variantUploadUrls?: Array<{
      variantIndex: number;
      variantName: string;
      imagesUploadUrls: Array<{
        url: string;
        key: string;
        index: number;
      }>;
    }>;
  };
  // Fallback cho classifications structure
  classifications?: Array<{
    id: number;
    type: string;
    uploadUrls?: {
      classificationId: string;
      imagesUploadUrls: Array<{
        url: string;
        key: string;
        index: number;
      }>;
    };
  }>;
}

export const useCreateDigitalProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateProductDto): Promise<CreateDigitalProductResponse> => {
      console.log('🚀 [useCreateDigitalProduct] Creating digital product with data:', JSON.stringify(data, null, 2));
      
      const response = await ProductService.createProduct(data);
      
      console.log('📥 [useCreateDigitalProduct] API Response:', JSON.stringify(response, null, 2));
      
      return response as CreateDigitalProductResponse;
    },
    onSuccess: (data) => {
      console.log('✅ [useCreateDigitalProduct] Product created successfully:', data);
      
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
    onError: (error) => {
      console.error('❌ [useCreateDigitalProduct] Error creating product:', error);
    },
  });
};
