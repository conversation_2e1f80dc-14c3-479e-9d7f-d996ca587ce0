import { apiClient } from '@/shared/api/axios';

import { ProductTypeEnum } from '../types/product.types';
import { OrderItemDto } from '../types/order.types';

/**
 * Interface cho thông tin sản phẩm con trong combo
 */
export interface ComboItemInfo {
  productId: number;
  productType: ProductTypeEnum;
  quantity: number;
  shipmentConfig?: {
    lengthCm?: number;
    widthCm?: number;
    heightCm?: number;
    weightGram?: number;
  };
}

/**
 * Interface cho response API lấy thông tin combo
 */
export interface ComboDetailsResponse {
  comboProductId: number;
  items: ComboItemInfo[];
  hasPhysicalProducts: boolean;
  totalPhysicalItems: number;
}

/**
 * Service xử lý logic vận chuyển cho sản phẩm combo
 */
export class ComboShippingService {
  /**
   * Kiểm tra combo có chứa sản phẩm vật lý không
   * @param comboProductId ID của sản phẩm combo
   * @returns Promise<boolean> - true nếu combo chứa sản phẩm vật lý
   */
  static async checkComboContainsPhysicalProducts(comboProductId: number): Promise<boolean> {
    try {
      console.log(`🔍 [ComboShippingService] Checking combo ${comboProductId} for physical products...`);
      
      const response = await apiClient.get<ComboDetailsResponse>(
        `/user/products/${comboProductId}/combo-details`
      );

      if (response.code === 0 && response.result) {
        const hasPhysical = response.result.hasPhysicalProducts;
        console.log(`✅ [ComboShippingService] Combo ${comboProductId} has physical products: ${hasPhysical}`);
        return hasPhysical;
      }

      console.warn(`⚠️ [ComboShippingService] Invalid response for combo ${comboProductId}:`, response);
      return false;
    } catch (error) {
      console.error(`❌ [ComboShippingService] Error checking combo ${comboProductId}:`, error);
      // Fallback: giả sử combo có sản phẩm vật lý để an toàn
      return true;
    }
  }

  /**
   * Lấy thông tin chi tiết các sản phẩm trong combo
   * @param comboProductId ID của sản phẩm combo
   * @returns Promise<ComboItemInfo[]> - danh sách sản phẩm con
   */
  static async getComboItemDetails(comboProductId: number): Promise<ComboItemInfo[]> {
    try {
      console.log(`🔍 [ComboShippingService] Getting combo ${comboProductId} item details...`);
      
      const response = await apiClient.get<ComboDetailsResponse>(
        `/user/products/${comboProductId}/combo-details`
      );

      if (response.code === 0 && response.result) {
        console.log(`✅ [ComboShippingService] Got ${response.result.items.length} items for combo ${comboProductId}`);
        return response.result.items;
      }

      console.warn(`⚠️ [ComboShippingService] Invalid response for combo ${comboProductId}:`, response);
      return [];
    } catch (error) {
      console.error(`❌ [ComboShippingService] Error getting combo ${comboProductId} details:`, error);
      return [];
    }
  }

  /**
   * Kiểm tra danh sách order items có chứa sản phẩm cần vận chuyển không
   * Xử lý cả sản phẩm đơn lẻ và combo
   * @param orderItems Danh sách sản phẩm trong đơn hàng
   * @returns Promise<boolean> - true nếu cần vận chuyển
   */
  static async checkOrderItemsNeedShipping(orderItems: OrderItemDto[]): Promise<boolean> {
    try {
      console.log(`🚚 [ComboShippingService] Checking ${orderItems.length} order items for shipping needs...`);

      // Kiểm tra từng item
      const checkPromises = orderItems.map(async (item) => {
        const productType = item.productType;

        switch (productType) {
          case ProductTypeEnum.PHYSICAL:
            console.log(`📦 [ComboShippingService] Item ${item.productId} is PHYSICAL - needs shipping`);
            return true;

          case ProductTypeEnum.COMBO:
            console.log(`📦 [ComboShippingService] Item ${item.productId} is COMBO - checking contents...`);
            return await this.checkComboContainsPhysicalProducts(item.productId);

          case ProductTypeEnum.DIGITAL:
          case ProductTypeEnum.SERVICE:
          case ProductTypeEnum.EVENT:
            console.log(`💻 [ComboShippingService] Item ${item.productId} is ${productType} - no shipping needed`);
            return false;

          default:
            // Nếu không có productType hoặc không xác định, giả sử là PHYSICAL để an toàn
            console.warn(`⚠️ [ComboShippingService] Item ${item.productId} has unknown productType: ${productType}, assuming PHYSICAL`);
            return true;
        }
      });

      const results = await Promise.all(checkPromises);
      const needsShipping = results.some(result => result === true);

      console.log(`🚚 [ComboShippingService] Order needs shipping: ${needsShipping}`);
      return needsShipping;
    } catch (error) {
      console.error(`❌ [ComboShippingService] Error checking shipping needs:`, error);
      // Fallback: giả sử cần vận chuyển để an toàn
      return true;
    }
  }

  /**
   * Tạo danh sách sản phẩm vật lý từ order items (bao gồm cả combo)
   * Dùng để tính phí vận chuyển
   * @param orderItems Danh sách sản phẩm trong đơn hàng
   * @returns Promise<Array> - danh sách sản phẩm vật lý để tính ship
   */
  static async getPhysicalProductsForShipping(orderItems: OrderItemDto[]): Promise<Array<{
    productId: number;
    quantity: number;
    shipmentConfig?: Record<string, unknown>;
  }>> {
    try {
      console.log(`📦 [ComboShippingService] Getting physical products from ${orderItems.length} order items...`);

      const physicalProducts: Array<{
        productId: number;
        quantity: number;
        shipmentConfig?: Record<string, unknown>;
      }> = [];

      for (const item of orderItems) {
        const productType = item.productType;

        switch (productType) {
          case ProductTypeEnum.PHYSICAL:
            // Sản phẩm vật lý đơn lẻ
            physicalProducts.push({
              productId: item.productId,
              quantity: item.quantity,
              shipmentConfig: item.shipmentConfig,
            });
            console.log(`📦 [ComboShippingService] Added PHYSICAL product ${item.productId} x${item.quantity}`);
            break;

          case ProductTypeEnum.COMBO: {
            // Combo - cần lấy các sản phẩm vật lý bên trong
            const comboItems = await this.getComboItemDetails(item.productId);
            const physicalComboItems = comboItems.filter(comboItem =>
              comboItem.productType === ProductTypeEnum.PHYSICAL
            );

            for (const comboItem of physicalComboItems) {
              physicalProducts.push({
                productId: comboItem.productId,
                quantity: comboItem.quantity * item.quantity, // Nhân với số lượng combo
                shipmentConfig: comboItem.shipmentConfig,
              });
              console.log(`📦 [ComboShippingService] Added PHYSICAL from combo ${item.productId}: product ${comboItem.productId} x${comboItem.quantity * item.quantity}`);
            }
            break;
          }

          default:
            // Sản phẩm số, dịch vụ, sự kiện - không cần vận chuyển
            console.log(`💻 [ComboShippingService] Skipping ${productType} product ${item.productId}`);
            break;
        }
      }

      console.log(`✅ [ComboShippingService] Found ${physicalProducts.length} physical products for shipping`);
      return physicalProducts;
    } catch (error) {
      console.error(`❌ [ComboShippingService] Error getting physical products:`, error);
      // Fallback: trả về tất cả items như sản phẩm vật lý
      return orderItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        shipmentConfig: item.shipmentConfig,
      }));
    }
  }

  /**
   * Kiểm tra một sản phẩm có cần vận chuyển không
   * @param productType Loại sản phẩm
   * @param productId ID sản phẩm (cần cho combo)
   * @returns Promise<boolean>
   */
  static async needsShipping(productType: ProductTypeEnum, productId?: number): Promise<boolean> {
    switch (productType) {
      case ProductTypeEnum.PHYSICAL:
        return true;
      case ProductTypeEnum.COMBO:
        if (productId) {
          return await this.checkComboContainsPhysicalProducts(productId);
        }
        return true; // Fallback
      case ProductTypeEnum.DIGITAL:
      case ProductTypeEnum.EVENT:
      case ProductTypeEnum.SERVICE:
        return false;
      default:
        return false;
    }
  }
}

export default ComboShippingService;
