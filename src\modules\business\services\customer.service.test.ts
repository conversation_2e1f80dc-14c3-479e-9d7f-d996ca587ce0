import { CustomerService, UpdateCustomerInformationDto } from './customer.service';
import { apiClient } from '@/shared/api';

// Mock apiClient
jest.mock('@/shared/api', () => ({
  apiClient: {
    put: jest.fn(),
    get: jest.fn(),
    post: jest.fn(),
    delete: jest.fn()
  }
}));

const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('CustomerService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('updateCustomerInformation', () => {
    it('should call the correct endpoint with tags', async () => {
      const customerId = 18;
      const updateData: UpdateCustomerInformationDto = {
        name: 'Nguyễn <PERSON>ăn <PERSON>',
        phone: '0912345678',
        email: {
          primary: '<EMAIL>',
          secondary: '<EMAIL>'
        },
        tags: ['VIP', 'Premium', 'Hot Lead'],
        address: '123 Test Street'
      };

      const mockResponse = {
        result: {
          id: customerId,
          name: '<PERSON><PERSON><PERSON><PERSON>',
          phone: '0912345678',
          email: { primary: '<EMAIL>' },
          tags: ['VIP', 'Premium', 'Hot Lead'],
          address: '123 Test Street',
          updatedAt: new Date().toISOString()
        }
      };

      mockedApiClient.put.mockResolvedValue(mockResponse);

      const result = await CustomerService.updateCustomerInformation(customerId, updateData);

      expect(mockedApiClient.put).toHaveBeenCalledWith(
        `/user/convert-customers/${customerId}/information`,
        updateData
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty tags array', async () => {
      const customerId = 18;
      const updateData: UpdateCustomerInformationDto = {
        name: 'Nguyễn Văn A',
        phone: '0912345678',
        email: {
          primary: '<EMAIL>'
        },
        tags: [],
        address: '123 Test Street'
      };

      const mockResponse = {
        result: {
          id: customerId,
          name: 'Nguyễn Văn A',
          phone: '0912345678',
          email: { primary: '<EMAIL>' },
          tags: [],
          address: '123 Test Street',
          updatedAt: new Date().toISOString()
        }
      };

      mockedApiClient.put.mockResolvedValue(mockResponse);

      const result = await CustomerService.updateCustomerInformation(customerId, updateData);

      expect(mockedApiClient.put).toHaveBeenCalledWith(
        `/user/convert-customers/${customerId}/information`,
        updateData
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle undefined tags', async () => {
      const customerId = 18;
      const updateData: UpdateCustomerInformationDto = {
        name: 'Nguyễn Văn A',
        phone: '0912345678',
        email: {
          primary: '<EMAIL>'
        },
        address: '123 Test Street'
        // tags is undefined
      };

      const mockResponse = {
        result: {
          id: customerId,
          name: 'Nguyễn Văn A',
          phone: '0912345678',
          email: { primary: '<EMAIL>' },
          address: '123 Test Street',
          updatedAt: new Date().toISOString()
        }
      };

      mockedApiClient.put.mockResolvedValue(mockResponse);

      const result = await CustomerService.updateCustomerInformation(customerId, updateData);

      expect(mockedApiClient.put).toHaveBeenCalledWith(
        `/user/convert-customers/${customerId}/information`,
        updateData
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateCustomerBasicInfo', () => {
    it('should call the basic-info endpoint without tags', async () => {
      const customerId = 18;
      const updateData = {
        name: 'Nguyễn Văn A',
        phone: '0912345678',
        email: {
          primary: '<EMAIL>',
          secondary: ''
        },
        address: '123 Test Street'
      };

      const mockResponse = {
        result: {
          id: customerId.toString(),
          name: 'Nguyễn Văn A',
          phone: '0912345678',
          email: { primary: '<EMAIL>', secondary: '' },
          address: '123 Test Street',
          avatar: '',
          updatedAt: new Date().toISOString()
        }
      };

      mockedApiClient.put.mockResolvedValue(mockResponse);

      const result = await CustomerService.updateCustomerBasicInfo(customerId, updateData);

      expect(mockedApiClient.put).toHaveBeenCalledWith(
        `/user/convert-customers/${customerId}/basic-info`,
        updateData
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
