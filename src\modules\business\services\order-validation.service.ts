import { ProductDto, ProductTypeEnum, PriceTypeEnum, HasPriceDto } from '../types/product.types';
import { OrderItemDto, OrderCustomerDto, CreateEnhancedOrderDto } from '../types/order.types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ProductValidationResult extends ValidationResult {
  productId: number;
  productName: string;
}

export interface OrderValidationResult extends ValidationResult {
  productValidations: ProductValidationResult[];
  customerValidation: ValidationResult;
  shippingValidation: ValidationResult;
  paymentValidation: ValidationResult;
}

/**
 * Service xử lý validation cho đơn hàng
 */
export class OrderValidationService {
  /**
   * Validate sản phẩm theo loại và business rules
   */
  static validateProduct(
    product: ProductDto,
    quantity: number,
    manualPrice?: number
  ): ProductValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const productType = product.productType || ProductTypeEnum.PHYSICAL;
    const priceType = product.typePrice || PriceTypeEnum.NO_PRICE;

    // Validation chung
    if (quantity <= 0) {
      errors.push('Số lượng phải lớn hơn 0');
    }

    // Validation theo loại sản phẩm
    switch (productType) {
      case ProductTypeEnum.PHYSICAL:
        // Kiểm tra tồn kho
        if (product.inventory) {
          if (quantity > product.inventory.availableQuantity) {
            errors.push(`Không đủ hàng trong kho. Còn lại: ${product.inventory.availableQuantity}`);
          }
          if (product.inventory.availableQuantity < 10) {
            warnings.push('Sản phẩm sắp hết hàng');
          }
        } else {
          warnings.push('Không có thông tin tồn kho');
        }

        // Kiểm tra cấu hình vận chuyển
        if (!product.shipmentConfig) {
          warnings.push('Chưa có cấu hình vận chuyển cho sản phẩm này');
        }
        break;

      case ProductTypeEnum.DIGITAL:
        // Sản phẩm số thường không cần số lượng lớn
        if (quantity > 10) {
          warnings.push('Số lượng lớn cho sản phẩm số có thể không cần thiết');
        }

        // Kiểm tra thông tin truy cập
        if (product.advancedInfo?.digitalFulfillmentFlow) {
          const fulfillment = product.advancedInfo.digitalFulfillmentFlow as Record<string, unknown>;
          if (!fulfillment.accessLink && !fulfillment.deliveryMethod) {
            warnings.push('Chưa có thông tin giao hàng số');
          }
        }
        break;

      case ProductTypeEnum.EVENT:
        // Kiểm tra thời gian sự kiện
        if (product.advancedInfo?.eventDetails) {
          const eventDetails = product.advancedInfo.eventDetails as Record<string, unknown>;
          const now = Date.now();
          
          if (eventDetails.startTime && eventDetails.startTime < now) {
            errors.push('Sự kiện đã kết thúc');
          }
          
          // Kiểm tra số chỗ còn lại
          if (eventDetails.capacity && eventDetails.currentRegistrations) {
            const remaining = eventDetails.capacity - eventDetails.currentRegistrations;
            if (quantity > remaining) {
              errors.push(`Không đủ chỗ. Còn lại: ${remaining} chỗ`);
            }
            if (remaining < 5) {
              warnings.push('Sự kiện sắp hết chỗ');
            }
          }

          // Kiểm tra thời gian đăng ký
          if (eventDetails.registrationDeadline && eventDetails.registrationDeadline < now) {
            errors.push('Đã hết hạn đăng ký');
          }
        }
        break;

      case ProductTypeEnum.SERVICE:
        // Kiểm tra giới hạn số lượng cho dịch vụ
        if (product.advancedInfo?.servicePackages) {
          const servicePackages = product.advancedInfo.servicePackages as Record<string, unknown>[];
          const activePackage = servicePackages.find(pkg => pkg.status === 'ACTIVE') || servicePackages[0];
          
          if (activePackage) {
            if (activePackage.minQuantityPerPurchase && quantity < activePackage.minQuantityPerPurchase) {
              errors.push(`Số lượng tối thiểu: ${activePackage.minQuantityPerPurchase}`);
            }
            
            if (activePackage.maxQuantityPerPurchase && quantity > activePackage.maxQuantityPerPurchase) {
              errors.push(`Số lượng tối đa: ${activePackage.maxQuantityPerPurchase}`);
            }

            // Kiểm tra thời gian dịch vụ
            if (activePackage.startTime && activePackage.endTime) {
              const now = Date.now();
              if (now < activePackage.startTime) {
                warnings.push('Dịch vụ chưa bắt đầu');
              }
              if (now > activePackage.endTime) {
                errors.push('Dịch vụ đã kết thúc');
              }
            }
          }
        }
        break;

      case ProductTypeEnum.COMBO: {
        // Kiểm tra tồn kho của các sản phẩm con
        if (product.advancedInfo?.info) {
          warnings.push('Cần kiểm tra tồn kho các sản phẩm trong combo');

          // TODO: Implement logic kiểm tra tồn kho từng sản phẩm con
          // Cần API call để lấy thông tin chi tiết các sản phẩm con
        }
        break;
      }
    }

    // Validation giá
    switch (priceType) {
      case PriceTypeEnum.HAS_PRICE: {
        const hasPrice = product.price as HasPriceDto;
        if (!hasPrice || hasPrice.salePrice <= 0) {
          errors.push('Giá sản phẩm không hợp lệ');
        }
        break;
      }

      case PriceTypeEnum.STRING_PRICE: {
        if (!manualPrice || manualPrice <= 0) {
          errors.push('Cần nhập giá thủ công cho sản phẩm này');
        }
        break;
      }

      case PriceTypeEnum.NO_PRICE:
        warnings.push('Sản phẩm chưa có giá');
        break;
    }

    return {
      productId: product.id,
      productName: product.name,
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate thông tin khách hàng
   */
  static validateCustomer(customer?: OrderCustomerDto): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!customer) {
      errors.push('Chưa chọn khách hàng');
      return { isValid: false, errors, warnings };
    }

    if (!customer.name || customer.name.trim().length === 0) {
      errors.push('Tên khách hàng không được để trống');
    }

    if (!customer.phone && !customer.email) {
      errors.push('Cần có ít nhất số điện thoại hoặc email');
    }

    if (customer.phone && !/^[0-9+\-\s()]+$/.test(customer.phone)) {
      warnings.push('Định dạng số điện thoại có thể không đúng');
    }

    if (customer.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customer.email)) {
      warnings.push('Định dạng email có thể không đúng');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate thông tin vận chuyển
   */
  static validateShipping(
    items: OrderItemDto[],
    hasShippingInfo: boolean,
    deliveryAddress?: Record<string, unknown>
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Kiểm tra có sản phẩm cần vận chuyển không
    const needsShipping = items.some(() => {
      // TODO: Implement logic kiểm tra productType
      // Hiện tại giả sử tất cả đều cần vận chuyển
      return true;
    });

    if (needsShipping) {
      if (!hasShippingInfo) {
        errors.push('Cần thông tin vận chuyển cho đơn hàng này');
      }

      if (!deliveryAddress) {
        errors.push('Chưa có địa chỉ giao hàng');
      } else {
        // Validate địa chỉ
        if (!deliveryAddress.recipientName) {
          errors.push('Chưa có tên người nhận');
        }
        if (!deliveryAddress.recipientPhone) {
          errors.push('Chưa có số điện thoại người nhận');
        }
        if (!deliveryAddress.address) {
          errors.push('Chưa có địa chỉ chi tiết');
        }
        if (!deliveryAddress.province || !deliveryAddress.district || !deliveryAddress.ward) {
          errors.push('Chưa đầy đủ thông tin tỉnh/thành, quận/huyện, phường/xã');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate thông tin thanh toán
   */
  static validatePayment(paymentMethod?: string, paymentStatus?: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!paymentMethod) {
      errors.push('Chưa chọn phương thức thanh toán');
    }

    if (!paymentStatus) {
      errors.push('Chưa có trạng thái thanh toán');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate toàn bộ đơn hàng
   */
  static validateOrder(
    orderData: CreateEnhancedOrderDto,
    products: ProductDto[],
    manualPrices?: Record<number, number>
  ): OrderValidationResult {
    const productValidations: ProductValidationResult[] = [];
    
    // Validate từng sản phẩm
    orderData.items.forEach(item => {
      const product = products.find(p => p.id === item.productId);
      if (product) {
        const manualPrice = manualPrices?.[item.productId];
        const validation = this.validateProduct(product, item.quantity, manualPrice);
        productValidations.push(validation);
      } else {
        productValidations.push({
          productId: item.productId,
          productName: 'Unknown',
          isValid: false,
          errors: ['Không tìm thấy thông tin sản phẩm'],
          warnings: [],
        });
      }
    });

    // Validate khách hàng
    const customerValidation = this.validateCustomer(orderData.customer);

    // Validate vận chuyển
    const shippingValidation = this.validateShipping(
      orderData.items,
      !!orderData.shipping,
      orderData.shipping?.toAddress
    );

    // Validate thanh toán
    const paymentValidation = this.validatePayment(
      orderData.payment?.method,
      orderData.payment?.status
    );

    // Tổng hợp kết quả
    const allErrors = [
      ...productValidations.flatMap(v => v.errors),
      ...customerValidation.errors,
      ...shippingValidation.errors,
      ...paymentValidation.errors,
    ];

    const allWarnings = [
      ...productValidations.flatMap(v => v.warnings),
      ...customerValidation.warnings,
      ...shippingValidation.warnings,
      ...paymentValidation.warnings,
    ];

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
      productValidations,
      customerValidation,
      shippingValidation,
      paymentValidation,
    };
  }
}
