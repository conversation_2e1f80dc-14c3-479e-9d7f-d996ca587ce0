import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * DTO cho sản phẩm trong request tính phí vận chuyển
 */
export interface ShippingProductDto {
  productId: number;
  quantity: number;
}

/**
 * DTO cho địa chỉ mới
 */
export interface NewAddressDto {
  recipientName: string;
  recipientPhone: string;
  address: string;
  province: string;
  district: string;
  ward: string;
  postalCode?: string;
  isDefault?: boolean;
  addressType?: 'home' | 'office' | 'other';
  note?: string;
}

/**
 * DTO cho địa chỉ giao hàng
 */
export interface DeliveryAddressDto {
  addressId?: number;
  newAddress?: NewAddressDto;
}

/**
 * DTO cho request tính phí vận chuyển
 */
export interface CalculateShippingFeeRequestDto {
  shopId: number;
  products: ShippingProductDto[];
  customerId?: number;
  deliveryAddress?: DeliveryAddressDto;
  preferredCarrier: 'GHN' | 'GHTK';
}

/**
 * DTO cho response tính phí vận chuyển
 */
export interface CalculateShippingFeeResponseDto {
  carrier: string;
  fee: number;
  serviceType: string;
  estimatedDeliveryTime?: string;
}

/**
 * Service tính toán phí vận chuyển
 */
export class ShippingCalculatorService {
  /**
   * Tính phí vận chuyển
   * @param request Thông tin request
   * @returns Phí vận chuyển
   */
  static async calculateShippingFee(
    request: CalculateShippingFeeRequestDto
  ): Promise<ApiResponseDto<CalculateShippingFeeResponseDto>> {
    try {
      console.log('🚚 [ShippingCalculatorService] Sending request:', {
        url: '/user/orders/calculate-shipping-fee',
        data: request
      });

      const response = await apiClient.post<CalculateShippingFeeResponseDto>(
        '/user/orders/calculate-shipping-fee',
        request
      );

      console.log('✅ [ShippingCalculatorService] Response received:', response);
      return response;
    } catch (error) {
      console.error('❌ [ShippingCalculatorService] Error:', error);
      throw error;
    }
  }



  /**
   * Validate request tính phí vận chuyển
   * @param request Request cần validate
   * @returns true nếu hợp lệ
   */
  static validateCalculateShippingRequest(request: CalculateShippingFeeRequestDto): boolean {
    // Kiểm tra shopId
    if (!request.shopId || request.shopId <= 0) {
      console.error('❌ [ShippingCalculatorService] Invalid shopId:', request.shopId);
      return false;
    }

    // Kiểm tra products
    if (!request.products || request.products.length === 0) {
      console.error('❌ [ShippingCalculatorService] No products provided');
      return false;
    }

    // Kiểm tra từng product
    for (const product of request.products) {
      if (!product.productId || product.productId <= 0) {
        console.error('❌ [ShippingCalculatorService] Invalid productId:', product.productId);
        return false;
      }
      if (!product.quantity || product.quantity <= 0) {
        console.error('❌ [ShippingCalculatorService] Invalid quantity:', product.quantity);
        return false;
      }
    }

    // Kiểm tra preferredCarrier
    if (!['GHN', 'GHTK'].includes(request.preferredCarrier)) {
      console.error('❌ [ShippingCalculatorService] Invalid preferredCarrier:', request.preferredCarrier);
      return false;
    }

    // Kiểm tra deliveryAddress nếu có
    if (request.deliveryAddress) {
      const { addressId, newAddress } = request.deliveryAddress;
      
      // Phải có ít nhất một trong hai: addressId hoặc newAddress
      if (!addressId && !newAddress) {
        console.error('❌ [ShippingCalculatorService] DeliveryAddress must have either addressId or newAddress');
        return false;
      }

      // Nếu có newAddress, validate các field bắt buộc
      if (newAddress) {
        if (!newAddress.recipientName || !newAddress.recipientPhone || 
            !newAddress.address || !newAddress.province || 
            !newAddress.district || !newAddress.ward) {
          console.error('❌ [ShippingCalculatorService] NewAddress missing required fields:', newAddress);
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Tạo request tính phí vận chuyển từ thông tin đơn hàng
   * @param shopId ID shop
   * @param products Danh sách sản phẩm
   * @param customerId ID khách hàng (optional)
   * @param deliveryAddress Địa chỉ giao hàng (optional)
   * @param preferredCarrier Đơn vị vận chuyển ưu tiên
   * @returns Request object
   */
  static createCalculateShippingRequest(
    shopId: number,
    products: ShippingProductDto[],
    customerId?: number,
    deliveryAddress?: DeliveryAddressDto,
    preferredCarrier: 'GHN' | 'GHTK' = 'GHN'
  ): CalculateShippingFeeRequestDto {
    // Đảm bảo shopId là number
    const normalizedShopId = typeof shopId === 'string' ? parseInt(shopId, 10) : shopId;

    // Đảm bảo products có productId và quantity là number
    const normalizedProducts = products.map(product => ({
      productId: typeof product.productId === 'string' ? parseInt(product.productId, 10) : product.productId,
      quantity: typeof product.quantity === 'string' ? parseInt(product.quantity, 10) : product.quantity,
    }));

    const request: CalculateShippingFeeRequestDto = {
      shopId: normalizedShopId,
      products: normalizedProducts,
      preferredCarrier,
    };

    if (customerId !== undefined && customerId !== null) {
      // Đảm bảo customerId là number
      const normalizedCustomerId = typeof customerId === 'string' ? parseInt(customerId, 10) : customerId;
      if (!isNaN(normalizedCustomerId) && normalizedCustomerId > 0) {
        request.customerId = normalizedCustomerId;
      }
    }

    if (deliveryAddress !== undefined) {
      // Đảm bảo addressId là number nếu có
      const normalizedDeliveryAddress = { ...deliveryAddress };
      if (normalizedDeliveryAddress.addressId !== undefined) {
        normalizedDeliveryAddress.addressId = typeof normalizedDeliveryAddress.addressId === 'string'
          ? parseInt(normalizedDeliveryAddress.addressId, 10)
          : normalizedDeliveryAddress.addressId;
      }
      request.deliveryAddress = normalizedDeliveryAddress;
    }

    console.log('🚚 [ShippingCalculatorService] Created request:', request);
    return request;
  }

  /**
   * Format lỗi từ API response
   * @param error Lỗi từ API
   * @returns Thông báo lỗi dễ hiểu
   */
  static formatError(error: unknown): string {
    if (error instanceof Error) {
      if (error.message.includes('400')) {
        return 'Thông tin tính phí vận chuyển không hợp lệ';
      }
      if (error.message.includes('404')) {
        return 'Không tìm thấy dịch vụ vận chuyển';
      }
      if (error.message.includes('500')) {
        return 'Lỗi hệ thống khi tính phí vận chuyển';
      }
      return error.message;
    }
    return 'Lỗi không xác định khi tính phí vận chuyển';
  }
}

export default ShippingCalculatorService;
