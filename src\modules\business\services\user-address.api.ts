import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Interface cho địa chỉ người dùng
 */
export interface UserAddressDto {
  id: number;
  userId: number;
  recipientName: string;
  recipientPhone: string;
  address: string;
  province?: string;
  district?: string;
  ward?: string;
  hamlet?: string;
  postalCode?: string;
  isDefault: boolean;
  addressType: string;
  note?: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho tạo địa chỉ mới
 */
export interface CreateUserAddressDto {
  recipientName: string;
  recipientPhone: string;
  address: string;
  province?: string;
  district?: string;
  ward?: string;
  hamlet?: string;
  postalCode?: string;
  isDefault?: boolean;
  addressType?: string;
  note?: string;
}

/**
 * Interface cho cập nhật địa chỉ
 */
export interface UpdateUserAddressDto {
  recipientName?: string;
  recipientPhone?: string;
  address?: string;
  province?: string;
  district?: string;
  ward?: string;
  hamlet?: string;
  postalCode?: string;
  isDefault?: boolean;
  addressType?: string;
  note?: string;
}

/**
 * User Address API Services
 */
const BASE_URL = '/user/addresses';

/**
 * Lấy danh sách địa chỉ của user hiện tại
 */
export const getUserAddresses = async (): Promise<ApiResponseDto<PaginatedResult<UserAddressDto>>> => {
  return apiClient.get<PaginatedResult<UserAddressDto>>(BASE_URL);
};

/**
 * Lấy địa chỉ mặc định của user hiện tại
 */
export const getDefaultUserAddress = async (): Promise<ApiResponseDto<UserAddressDto | null>> => {
  return apiClient.get<UserAddressDto | null>(`${BASE_URL}/default`);
};

/**
 * Lấy thông tin địa chỉ theo ID
 */
export const getUserAddressById = async (id: number): Promise<ApiResponseDto<UserAddressDto>> => {
  return apiClient.get<UserAddressDto>(`${BASE_URL}/${id}`);
};

/**
 * Tạo địa chỉ mới
 */
export const createUserAddress = async (data: CreateUserAddressDto): Promise<ApiResponseDto<UserAddressDto>> => {
  return apiClient.post<UserAddressDto>(BASE_URL, data);
};

/**
 * Cập nhật địa chỉ
 */
export const updateUserAddress = async (id: number, data: UpdateUserAddressDto): Promise<ApiResponseDto<UserAddressDto>> => {
  return apiClient.put<UserAddressDto>(`${BASE_URL}/${id}`, data);
};

/**
 * Xóa địa chỉ
 */
export const deleteUserAddress = async (id: number): Promise<ApiResponseDto<boolean>> => {
  return apiClient.delete<boolean>(`${BASE_URL}/${id}`);
};

/**
 * Đặt địa chỉ làm mặc định
 */
export const setDefaultUserAddress = async (id: number): Promise<ApiResponseDto<UserAddressDto>> => {
  return apiClient.put<UserAddressDto>(`${BASE_URL}/${id}/default`);
};
