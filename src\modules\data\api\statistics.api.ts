import { apiClient } from '@/shared/api/axios';
import type { DataOverviewStats, StorageStatistics } from '../types';

/**
 * API layer cho thống kê data
 */

/**
 * L<PERSON>y thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store
 * @returns Promise<DataOverviewStats>
 */
export const getUserStatistics = async (): Promise<DataOverviewStats> => {
  const response = await apiClient.get<DataOverviewStats>('/user/statistics');
  return response;
};

/**
 * Lấy thống kê dung lượng dữ liệu của người dùng hiện tại
 * @returns Promise<StorageStatistics>
 */
export const getUserStorageStatistics = async (): Promise<StorageStatistics> => {
  const response = await apiClient.get<StorageStatistics>('/user/statistics/storage');
  return response;
};
