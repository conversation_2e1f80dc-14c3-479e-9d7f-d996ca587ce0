import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { Card, Table, Typography, Button } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';

// Types cho SMS provider
interface SMSProviderConfig {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'error';
  accountSid?: string;
  apiKey?: string;
  brandName?: string;
  createdAt: string;
  updatedAt: string;
}

// Mock data cho demo
const mockSMSConfigs: SMSProviderConfig[] = [
  {
    id: '1',
    name: 'Twilio Production',
    type: 'twilio',
    status: 'active',
    accountSid: 'AC***************',
    apiKey: '***************',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'FTP SMS Marketing',
    type: 'ftp',
    status: 'active',
    brandName: 'COMPANY',
    apiKey: '***************',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T09:00:00Z'
  }
];

/**
 * Trang quản lý SMS Provider cụ thể
 */
const SMSProviderPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);
  const { provider } = useParams<{ provider: string }>();
  
  // State
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Slide form hooks
  const {
    isVisible: isCreateFormVisible,
    show: showCreateForm,
    hide: hideCreateForm
  } = useSlideForm();

  // Provider info mapping
  const providerInfo = useMemo(() => {
    const providers: Record<string, { title: string; description: string; icon: string }> = {
      twilio: {
        title: 'Twilio',
        description: 'Dịch vụ SMS quốc tế Twilio',
        icon: 'mail-plus'
      },
      ftp: {
        title: 'FTP SMS Brandname',
        description: 'Dịch vụ SMS brandname FTP',
        icon: 'mail-plus'
      },
      vnpt: {
        title: 'VNPT SMS',
        description: 'Dịch vụ SMS VNPT',
        icon: 'mail-plus'
      }
    };
    return providers[provider || 'twilio'] || providers.twilio;
  }, [provider]);

  // Table columns
  const columns: TableColumn<SMSProviderConfig>[] = [
    {
      title: t('common:name'),
      dataIndex: 'name',
      key: 'name',
      sortable: true,
      render: (value: string) => (
        <Typography variant="body2" className="font-medium">
          {value}
        </Typography>
      )
    },
    {
      title: t('integration:status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          status === 'active' 
            ? 'bg-green-100 text-green-800' 
            : status === 'error'
            ? 'bg-red-100 text-red-800'
            : 'bg-gray-100 text-gray-800'
        }`}>
          {t(`integration:status.${status}`)}
        </span>
      )
    },
    {
      title: t('integration:brandName'),
      dataIndex: 'brandName',
      key: 'brandName',
      render: (value: string) => value || '-'
    },
    {
      title: t('integration:accountSid'),
      dataIndex: 'accountSid',
      key: 'accountSid',
      render: (value: string) => value || '-'
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      sortable: true,
      render: (value: string) => new Date(value).toLocaleDateString('vi-VN')
    }
  ];

  // Filter data
  const filteredData = useMemo(() => {
    return mockSMSConfigs.filter(config =>
      config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      config.type.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]);

  // Handlers
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handleSortChange = () => {
    // Sort logic will be implemented when connected to real API
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
            <span className="text-2xl">📱</span>
          </div>
          <div>
            <Typography variant="h4" className="font-bold">
              {providerInfo.title}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {providerInfo.description}
            </Typography>
          </div>
        </div>

        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={() => showCreateForm()}
          items={[
            {
              id: 'all',
              label: t('common:all'),
              icon: 'list',
              onClick: () => {}
            }
          ]}
          showDateFilter={false}
          showColumnFilter={false}
        />

        {/* Table */}
        <Card>
          <Table<SMSProviderConfig>
            columns={columns}
            data={filteredData}
            rowKey="id"
            loading={false}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: filteredData.length,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true
            }}
          />
        </Card>

        {/* Create Form */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <div className="p-6">
            <Typography variant="h5" className="mb-4">
              {t('integration:addNewProvider', { provider: providerInfo.title })}
            </Typography>
            <div className="space-y-4">
              <Typography variant="body2" className="text-muted-foreground">
                {t('integration:providerFormComingSoon')}
              </Typography>
              <Button onClick={hideCreateForm} variant="outline">
                {t('common:close')}
              </Button>
            </div>
          </div>
        </SlideInForm>
      </div>
    </div>
  );
};

export default SMSProviderPage;
