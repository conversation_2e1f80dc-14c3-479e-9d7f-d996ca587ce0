import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, IconCard } from '@/shared/components/common';
import { Audience } from '../types/audience.types';

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number; // ID của custom field
  fieldId: number; // Alias cho id để tương thích với CustomFieldRenderer
  configId: string; // configId để gửi lên API
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Mock data cho custom field selector (tương tự như business module)
const mockCustomFieldSearch = async (query: string) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const mockFields = [
    {
      id: 1,
      label: 'Sở thích',
      component: 'input',
      type: 'text',
      required: false,
      configId: 'audience_hobby',
      configJson: { placeholder: 'Nhập sở thích' }
    },
    {
      id: 2,
      label: 'Độ tuổi',
      component: 'number',
      type: 'number',
      required: false,
      configId: 'audience_age',
      configJson: { placeholder: 'Nhập độ tuổi', min: 0, max: 120 }
    },
    {
      id: 3,
      label: 'Giới tính',
      component: 'select',
      type: 'select',
      required: false,
      configId: 'audience_gender',
      configJson: { 
        placeholder: 'Chọn giới tính',
        options: [
          { label: 'Nam', value: 'male' },
          { label: 'Nữ', value: 'female' },
          { label: 'Khác', value: 'other' }
        ]
      }
    },
    {
      id: 4,
      label: 'Ghi chú',
      component: 'textarea',
      type: 'text',
      required: false,
      configId: 'audience_notes',
      configJson: { placeholder: 'Nhập ghi chú' }
    },
    {
      id: 5,
      label: 'Đã đăng ký newsletter',
      component: 'checkbox',
      type: 'boolean',
      required: false,
      configId: 'audience_newsletter',
      configJson: { placeholder: 'Đăng ký nhận newsletter' }
    }
  ];

  return mockFields.filter(field => 
    field.label.toLowerCase().includes(query.toLowerCase()) ||
    field.configId.toLowerCase().includes(query.toLowerCase())
  );
};

interface AudienceCustomFieldsProps {
  audience: Audience;
}

/**
 * Component hiển thị trường tùy chỉnh của audience
 */
const AudienceCustomFields: React.FC<AudienceCustomFieldsProps> = ({ audience }) => {
  const { t } = useTranslation(['marketing', 'common']);

  // State
  const [audienceCustomFields, setAudienceCustomFields] = useState<SelectedCustomField[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Array<{
    id: number;
    label: string;
    component: string;
    type: string;
    required: boolean;
    configId: string;
    configJson: Record<string, unknown>;
  }>>([]);

  // Handle search custom fields
  const handleSearch = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const results = await mockCustomFieldSearch(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching custom fields:', error);
      setSearchResults([]);
    }
  }, []);

  // Handle add custom field
  const handleAddCustomField = useCallback((fieldData: {
    id: number;
    label: string;
    component: string;
    type: string;
    required: boolean;
    configId: string;
    configJson: Record<string, unknown>;
  }) => {
    setAudienceCustomFields(prev => {
      // Check if field already exists
      if (prev.some(f => f.fieldId === fieldData.id)) {
        return prev;
      }

      // Add new field
      const newField: SelectedCustomField = {
        id: Date.now(), // Temporary ID
        fieldId: fieldData.id,
        configId: fieldData.configId || fieldData.id.toString(),
        label: fieldData.label || `Field ${fieldData.id}`,
        component: fieldData.component || fieldData.type || 'text',
        type: fieldData.type || 'text',
        required: fieldData.required || false,
        configJson: fieldData.configJson || {},
        value: { value: '' }, // Default value
      };

      setHasChanges(true);
      return [...prev, newField];
    });
  }, []);

  // Handle update custom field value
  const handleUpdateCustomField = useCallback((fieldId: number, value: string | number | boolean) => {
    setAudienceCustomFields(prev => 
      prev.map(field => 
        field.id === fieldId 
          ? { ...field, value: { value } }
          : field
      )
    );
    setHasChanges(true);
  }, []);

  // Handle remove custom field
  const handleRemoveCustomField = useCallback((fieldId: number) => {
    setAudienceCustomFields(prev => prev.filter(field => field.id !== fieldId));
    setHasChanges(true);
  }, []);

  // Handle save custom fields
  const handleSaveCustomFields = useCallback(async () => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Saving custom fields for audience:', audience.id, audienceCustomFields);
      
      // Reset changes flag
      setHasChanges(false);
      
    } catch (error) {
      console.error('Error saving custom fields:', error);
    } finally {
      setIsSaving(false);
    }
  }, [audienceCustomFields, audience.id]);

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('marketing:audience.customFields')}
        </Typography>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* Simple search input */}
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              handleSearch(e.target.value);
            }}
            placeholder={t('marketing:audience.customFields.searchPlaceholder', 'Nhập từ khóa để tìm trường tùy chỉnh...')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          
          {/* Search results dropdown */}
          {searchQuery && searchResults.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
              {searchResults.map((field) => (
                <div
                  key={field.id}
                  onClick={() => {
                    handleAddCustomField(field);
                    setSearchQuery('');
                    setSearchResults([]);
                  }}
                  className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  <div className="font-medium">{field.label}</div>
                  <div className="text-sm text-gray-500">{field.component} • {field.configId}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Selected custom fields */}
        {audienceCustomFields.length > 0 && (
          <div className="space-y-3">
            {audienceCustomFields.map((field) => (
              <div key={field.id} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Typography variant="caption" className="font-medium">
                      {field.label}
                    </Typography>
                    {field.required && (
                      <span className="text-red-500 text-xs">*</span>
                    )}
                    <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                      {field.component || field.type || 'text'}
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveCustomField(field.id)}
                    className="text-red-500 hover:text-red-700 text-sm"
                  >
                    ✕
                  </button>
                </div>
                
                {/* Simple input for now */}
                <input
                  type="text"
                  value={field.value.value as string || ''}
                  onChange={(e) => handleUpdateCustomField(field.id, e.target.value)}
                  placeholder={`Nhập ${field.label.toLowerCase()}`}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            ))}
          </div>
        )}

        {/* Empty state */}
        {audienceCustomFields.length === 0 && (
          <div className="text-center py-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('marketing:audience.customFields.noFields', 'Chưa có trường tùy chỉnh nào. Sử dụng ô tìm kiếm trên để thêm trường.')}
            </Typography>
          </div>
        )}

        {/* Save button */}
        {hasChanges && audienceCustomFields.length > 0 && (
          <div className="flex justify-end pt-4 border-t border-border">
            <IconCard
              icon="check"
              onClick={handleSaveCustomFields}
              disabled={isSaving}
              variant="primary"
              title={t('common:save')}
              size="md"
              isLoading={isSaving}
            />
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default AudienceCustomFields;
