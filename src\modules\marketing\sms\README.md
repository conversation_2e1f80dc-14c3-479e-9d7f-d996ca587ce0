# SMS Marketing Module - Send & Template Pages

## Tổng quan

Module này cung cấp hai trang chính cho SMS Marketing:

1. **Trang Gửi SMS** (`/marketing/sms/send`) - G<PERSON>i tin nhắn SMS nhanh
2. **Trang Tạo Mẫu SMS** (`/marketing/sms/templates/create`) - Tạo mẫu tin nhắn SMS

## Cấu trúc thư mục

```
src/modules/marketing/sms/
├── components/
│   ├── SmsSendForm.tsx          # Form gửi SMS
│   ├── SmsTemplateForm.tsx      # Form tạo mẫu SMS
│   └── index.ts
├── pages/
│   ├── SmsSendPage.tsx          # Trang gửi SMS
│   ├── SmsTemplateCreatePage.tsx # Trang tạo mẫu SMS
│   └── index.ts
├── hooks/
│   ├── useSmsTemplates.ts       # Hook quản lý templates
│   ├── useSmsSend.ts           # Hook gửi SMS
│   └── index.ts
├── services/
│   ├── sms.service.ts          # API service
│   ├── sms-business.service.ts # Business logic service
│   └── index.ts
├── schemas/
│   ├── sms.schema.ts           # Validation schemas
│   └── index.ts
├── constants/
│   └── sms.constants.ts        # Constants & query keys
├── locales/
│   ├── vi.json                 # Tiếng Việt
│   └── en.json                 # English
└── README.md
```

## Tính năng

### Trang Gửi SMS (`/marketing/sms/send`)

- **Chọn Brandname**: Chọn brandname để gửi tin nhắn
- **Người nhận**: 
  - Một người nhận (single)
  - Nhiều người nhận (multiple)
  - Tải file danh sách (file upload)
- **Nội dung tin nhắn**:
  - Nhập nội dung tự do
  - Chọn từ mẫu có sẵn
  - Hiển thị số ký tự và số tin nhắn
- **Lên lịch gửi**:
  - Gửi ngay
  - Lên lịch gửi sau
- **Tùy chọn**:
  - Báo cáo gửi
  - Cho phép từ chối

### Trang Tạo Mẫu SMS (`/marketing/sms/templates/create`)

- **Thông tin cơ bản**:
  - Tên mẫu
  - Danh mục (Marketing, Giao dịch, OTP, v.v.)
  - Mô tả
- **Nội dung**:
  - Soạn nội dung mẫu
  - Sử dụng biến động `{{variable}}`
  - Hiển thị số ký tự và số tin nhắn
- **Biến**:
  - Định nghĩa các biến có thể sử dụng
  - Loại biến: text, number, date, url, phone
  - Biến bắt buộc/tùy chọn
- **Thẻ**: Gắn thẻ để phân loại
- **Cài đặt**:
  - Ngôn ngữ
  - Trạng thái
  - Mẫu mặc định

## API Endpoints

```typescript
// Brandnames
GET /api/v1/marketing/sms/brandnames

// Templates
GET /api/v1/marketing/sms/templates
POST /api/v1/marketing/sms/templates
GET /api/v1/marketing/sms/templates/:id
PUT /api/v1/marketing/sms/templates/:id
DELETE /api/v1/marketing/sms/templates/:id

// Messages
POST /api/v1/marketing/sms/messages/send
POST /api/v1/marketing/sms/messages/send-bulk
GET /api/v1/marketing/sms/messages
GET /api/v1/marketing/sms/messages/:id

// Utilities
POST /api/v1/marketing/sms/templates/:id/preview
POST /api/v1/marketing/sms/messages/validate-phones
```

## Sử dụng

### Import Components

```tsx
import { SmsSendPage, SmsTemplateCreatePage } from '@/modules/marketing/sms';
```

### Import Hooks

```tsx
import { 
  useSmsBrandnames, 
  useSmsTemplates, 
  useSendSms, 
  useCreateSmsTemplate 
} from '@/modules/marketing/sms';
```

### Import Services

```tsx
import { SmsService, SmsBusinessService } from '@/modules/marketing/sms';
```

## Validation

Sử dụng Zod schemas để validate:

- **SMS Send Form**: `smsSendFormSchema`
- **SMS Template Form**: `smsTemplateFormSchema`
- **Phone Numbers**: `phoneNumberSchema`, `phoneNumbersSchema`

## Internationalization

Hỗ trợ đa ngôn ngữ:

- Tiếng Việt: `src/modules/marketing/sms/locales/vi.json`
- English: `src/modules/marketing/sms/locales/en.json`

Sử dụng namespace `sms`:

```tsx
const { t } = useTranslation(['common', 'sms']);
t('sms:send.title') // "Gửi SMS"
t('sms:template.createTitle') // "Tạo mẫu tin nhắn SMS"
```

## Routes

Các routes đã được thêm vào `marketingRoutes.tsx`:

```tsx
// SMS Send
{
  path: '/marketing/sms/send',
  element: <SmsSendPage />
}

// SMS Template Create
{
  path: '/marketing/sms/templates/create',
  element: <SmsTemplateCreatePage />
}
```

## Dependencies

- React Hook Form + Zod cho form validation
- TanStack Query cho data fetching
- React Router cho navigation
- i18next cho internationalization

## Lưu ý

1. **Form Validation**: Tất cả form đều sử dụng Zod schema validation
2. **Error Handling**: Sử dụng `NotificationUtil` để hiển thị lỗi
3. **Loading States**: Tất cả mutations đều có loading state
4. **Phone Validation**: Hỗ trợ format số điện thoại Việt Nam
5. **Character Counting**: Tự động đếm ký tự và số tin nhắn SMS
6. **Business Logic**: Tách biệt API service và business logic service

## Testing

Để test các trang:

1. Truy cập `/marketing/sms/send` để test trang gửi SMS
2. Truy cập `/marketing/sms/templates/create` để test trang tạo mẫu
3. Kiểm tra validation bằng cách nhập dữ liệu không hợp lệ
4. Test các trường hợp edge cases như số điện thoại không hợp lệ

## Tích hợp

Module này tích hợp với:

- SMS Integration module (`/modules/integration/sms`)
- Marketing overview page (`/marketing/sms`)
- Shared components (`/shared/components/common`)
- API client (`/shared/services/api`)
