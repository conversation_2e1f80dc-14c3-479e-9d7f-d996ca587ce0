import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Select,
  Button,
  Card,
  Typography,
  RadioGroup,
  CollapsibleCard,
  Switch,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { NotificationUtil } from '@/shared/utils/notification';
import { smsSendFormSchema, type SmsSendFormData } from '../schemas';
import { useSmsBrandnames, useSmsTemplates, useSendSms, useSendBulkSms, useValidatePhoneNumbers } from '../hooks';

interface SmsSendFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const SmsSendForm: React.FC<SmsSendFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['common', 'sms']);
  const { formRef, setFormErrors } = useFormErrors<SmsSendFormData>();
  
  // State
  const [recipientType, setRecipientType] = useState<'single' | 'multiple' | 'file'>('single');
  const [scheduleType, setScheduleType] = useState<'now' | 'later'>('now');
  const [phoneNumbers, setPhoneNumbers] = useState<string>('');
  const [messageContent, setMessageContent] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [selectedBrandname, setSelectedBrandname] = useState<string>('');
  const [scheduleDateTime, setScheduleDateTime] = useState<string>('');
  const [enableDeliveryReport, setEnableDeliveryReport] = useState<boolean>(true);
  const [enableOptOut, setEnableOptOut] = useState<boolean>(true);

  // Hooks
  const { data: brandnames, isLoading: brandnamesLoading } = useSmsBrandnames();
  const { data: templatesData, isLoading: templatesLoading } = useSmsTemplates({ status: 'active' });
  const sendSmsMutation = useSendSms();
  const sendBulkSmsMutation = useSendBulkSms();
  const validatePhonesMutation = useValidatePhoneNumbers();

  // Options
  const brandnameOptions = brandnames?.map(brandname => ({
    value: brandname.id.toString(),
    label: brandname.name,
  })) || [];

  const templateOptions = [
    { value: '', label: t('sms:send.noTemplate', 'Không sử dụng mẫu') },
    ...(templatesData?.items?.map(template => ({
      value: template.id.toString(),
      label: template.name,
    })) || []),
  ];

  const recipientTypeOptions = [
    { value: 'single', label: t('sms:send.singleRecipient', 'Một người nhận') },
    { value: 'multiple', label: t('sms:send.multipleRecipients', 'Nhiều người nhận') },
    { value: 'file', label: t('sms:send.fileUpload', 'Tải file danh sách') },
  ];

  const scheduleTypeOptions = [
    { value: 'now', label: t('sms:send.sendNow', 'Gửi ngay') },
    { value: 'later', label: t('sms:send.sendLater', 'Lên lịch gửi') },
  ];

  // Handlers
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    try {
      // Validate form data
      const formData: SmsSendFormData = {
        brandnameId: selectedBrandname,
        templateId: selectedTemplate || undefined,
        recipients: {
          type: recipientType,
          phone: recipientType === 'single' ? phoneNumbers : undefined,
          phones: recipientType === 'multiple' ? phoneNumbers.split('\n').filter(p => p.trim()) : undefined,
        },
        message: {
          content: messageContent,
          variables: {},
        },
        schedule: {
          type: scheduleType,
          datetime: scheduleType === 'later' ? scheduleDateTime : undefined,
        },
        options: {
          enableDeliveryReport,
          enableOptOut,
        },
      };

      // Validate with schema
      const validatedData = smsSendFormSchema.parse(formData);

      // Send SMS
      if (recipientType === 'single') {
        await sendSmsMutation.mutateAsync({
          brandnameId: parseInt(validatedData.brandnameId),
          templateId: validatedData.templateId ? parseInt(validatedData.templateId) : undefined,
          phone: validatedData.recipients.phone!,
          content: validatedData.message.content,
          params: validatedData.message.variables,
        });
        
        NotificationUtil.success({ message: t('sms:send.singleSendSuccess', 'Gửi tin nhắn thành công') });
      } else if (recipientType === 'multiple') {
        await sendBulkSmsMutation.mutateAsync({
          brandnameId: parseInt(validatedData.brandnameId),
          templateId: validatedData.templateId ? parseInt(validatedData.templateId) : undefined,
          phones: validatedData.recipients.phones!,
          content: validatedData.message.content,
          params: validatedData.recipients.phones!.map(() => validatedData.message.variables || {}),
        });
        
        NotificationUtil.success({ message: t('sms:send.bulkSendSuccess', 'Gửi tin nhắn hàng loạt thành công') });
      }

      onSuccess?.();
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<SmsSendFormData> = {};
        const issues = (error as { issues: Array<{ path: string[]; message: string }> }).issues;
        issues.forEach((issue) => {
          const path = issue.path.join('.');
          fieldErrors[path as keyof SmsSendFormData] = issue.message;
        });
        setFormErrors(fieldErrors);
      } else {
        const errorMessage = error instanceof Error ? error.message : t('common:error.unknown', 'Có lỗi xảy ra');
        NotificationUtil.error({ message: errorMessage });
      }
    }
  };

  const handleValidatePhones = async () => {
    if (recipientType === 'multiple' && phoneNumbers) {
      try {
        const phones = phoneNumbers.split('\n').filter(p => p.trim());
        const result = await validatePhonesMutation.mutateAsync(phones);
        
        if (result.invalid.length > 0) {
          NotificationUtil.warning({
            message: t('sms:send.invalidPhones', 'Có {{count}} số điện thoại không hợp lệ', { count: result.invalid.length })
          });
        } else {
          NotificationUtil.success({
            message: t('sms:send.allPhonesValid', 'Tất cả số điện thoại đều hợp lệ')
          });
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : t('common:error.unknown', 'Có lỗi xảy ra');
        NotificationUtil.error({ message: errorMessage });
      }
    }
  };

  const characterCount = messageContent.length;
  const smsCount = Math.ceil(characterCount / 160);

  return (
    <div className="w-full bg-background text-foreground">
      <Form ref={formRef} schema={smsSendFormSchema} onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Brandname Selection */}
          <Card>
            <div className="space-y-4">
              <Typography variant="h3">
                {t('sms:send.brandnameSelection', 'Chọn Brandname')}
              </Typography>
              
              <FormItem name="brandnameId" label={t('sms:send.brandname', 'Brandname')} required>
                <Select
                  value={selectedBrandname}
                  onChange={setSelectedBrandname}
                  options={brandnameOptions}
                  placeholder={t('sms:send.selectBrandname', 'Chọn brandname')}
                  loading={brandnamesLoading}
                  fullWidth
                />
              </FormItem>
            </div>
          </Card>

          {/* Recipients */}
          <CollapsibleCard title={t('sms:send.recipients', 'Người nhận')} defaultExpanded>
            <div className="space-y-4">
              <FormItem name="recipients.type" label={t('sms:send.recipientType', 'Loại người nhận')} required>
                <RadioGroup
                  value={recipientType}
                  onChange={setRecipientType}
                  options={recipientTypeOptions}
                />
              </FormItem>

              {recipientType === 'single' && (
                <FormItem name="recipients.phone" label={t('sms:send.phoneNumber', 'Số điện thoại')} required>
                  <Input
                    type="tel"
                    value={phoneNumbers}
                    onChange={(e) => setPhoneNumbers(e.target.value)}
                    placeholder="0987654321"
                    fullWidth
                  />
                </FormItem>
              )}

              {recipientType === 'multiple' && (
                <div className="space-y-2">
                  <FormItem name="recipients.phones" label={t('sms:send.phoneNumbers', 'Danh sách số điện thoại')} required>
                    <Textarea
                      value={phoneNumbers}
                      onChange={(e) => setPhoneNumbers(e.target.value)}
                      placeholder={t('sms:send.phoneNumbersPlaceholder', 'Nhập mỗi số điện thoại trên một dòng')}
                      rows={6}
                      fullWidth
                    />
                  </FormItem>
                  
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleValidatePhones}
                    loading={validatePhonesMutation.isPending}
                    size="sm"
                  >
                    {t('sms:send.validatePhones', 'Kiểm tra số điện thoại')}
                  </Button>
                </div>
              )}

              {recipientType === 'file' && (
                <FormItem name="recipients.file" label={t('sms:send.uploadFile', 'Tải file')}>
                  <Input
                    type="file"
                    accept=".csv,.xlsx,.txt"
                    fullWidth
                  />
                  <Typography variant="caption" className="text-muted-foreground mt-1">
                    {t('sms:send.fileFormats', 'Hỗ trợ file CSV, Excel, TXT')}
                  </Typography>
                </FormItem>
              )}
            </div>
          </CollapsibleCard>

          {/* Message Content */}
          <CollapsibleCard title={t('sms:send.messageContent', 'Nội dung tin nhắn')} defaultExpanded>
            <div className="space-y-4">
              <FormItem name="templateId" label={t('sms:send.template', 'Mẫu tin nhắn')}>
                <Select
                  value={selectedTemplate}
                  onChange={setSelectedTemplate}
                  options={templateOptions}
                  placeholder={t('sms:send.selectTemplate', 'Chọn mẫu tin nhắn')}
                  loading={templatesLoading}
                  fullWidth
                />
              </FormItem>

              <FormItem name="message.content" label={t('sms:send.content', 'Nội dung')} required>
                <Textarea
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                  placeholder={t('sms:send.contentPlaceholder', 'Nhập nội dung tin nhắn')}
                  rows={4}
                  fullWidth
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-1">
                  <span>{t('sms:send.characterCount', 'Số ký tự: {{count}}', { count: characterCount })}</span>
                  <span>{t('sms:send.smsCount', 'Số tin: {{count}}', { count: smsCount })}</span>
                </div>
              </FormItem>
            </div>
          </CollapsibleCard>

          {/* Schedule */}
          <CollapsibleCard title={t('sms:send.schedule', 'Lên lịch gửi')}>
            <div className="space-y-4">
              <FormItem name="schedule.type" label={t('sms:send.scheduleType', 'Thời gian gửi')} required>
                <RadioGroup
                  value={scheduleType}
                  onChange={setScheduleType}
                  options={scheduleTypeOptions}
                />
              </FormItem>

              {scheduleType === 'later' && (
                <FormItem name="schedule.datetime" label={t('sms:send.scheduleDateTime', 'Thời gian')} required>
                  <Input
                    type="datetime-local"
                    value={scheduleDateTime}
                    onChange={(e) => setScheduleDateTime(e.target.value)}
                    fullWidth
                  />
                </FormItem>
              )}
            </div>
          </CollapsibleCard>

          {/* Options */}
          <CollapsibleCard title={t('sms:send.options', 'Tùy chọn')}>
            <div className="space-y-4">
              <FormItem name="options.enableDeliveryReport" label={t('sms:send.deliveryReport', 'Báo cáo gửi')}>
                <Switch
                  checked={enableDeliveryReport}
                  onChange={setEnableDeliveryReport}
                />
              </FormItem>

              <FormItem name="options.enableOptOut" label={t('sms:send.optOut', 'Cho phép từ chối')}>
                <Switch
                  checked={enableOptOut}
                  onChange={setEnableOptOut}
                />
              </FormItem>
            </div>
          </CollapsibleCard>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                {t('common:cancel', 'Hủy')}
              </Button>
            )}
            
            <Button
              type="submit"
              variant="primary"
              loading={sendSmsMutation.isPending || sendBulkSmsMutation.isPending}
            >
              {scheduleType === 'now' 
                ? t('sms:send.sendNow', 'Gửi ngay')
                : t('sms:send.schedule', 'Lên lịch gửi')
              }
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default SmsSendForm;
