import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SmsService, SmsBusinessService } from '../services';
import { SMS_QUERY_KEYS } from '../constants/sms.constants';
import type { SendSmsMessageDto, SendBulkSmsMessageDto } from '../../types';

/**
 * Hook for SMS brandnames
 */
export const useSmsBrandnames = () => {
  return useQuery({
    queryKey: SMS_QUERY_KEYS.BRANDNAMES_ALL,
    queryFn: () => SmsBusinessService.getActiveBrandnames(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for sending single SMS
 */
export const useSendSms = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SendSmsMessageDto) => 
      SmsBusinessService.sendMessageWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate messages list
      queryClient.invalidateQueries({
        queryKey: SMS_QUERY_KEYS.MESSAGES_ALL,
      });
    },
  });
};

/**
 * Hook for sending bulk SMS
 */
export const useSendBulkSms = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SendBulkSmsMessageDto) => 
      SmsBusinessService.sendBulkMessagesWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate messages list
      queryClient.invalidateQueries({
        queryKey: SMS_QUERY_KEYS.MESSAGES_ALL,
      });
    },
  });
};

/**
 * Hook for validating phone numbers
 */
export const useValidatePhoneNumbers = () => {
  return useMutation({
    mutationFn: (phones: string[]) => 
      SmsBusinessService.validateAndCleanPhoneNumbers(phones),
  });
};

/**
 * Hook for SMS messages
 */
export const useSmsMessages = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  brandnameId?: number;
}) => {
  return useQuery({
    queryKey: SMS_QUERY_KEYS.MESSAGES_LIST(params || {}),
    queryFn: () => SmsService.getMessages(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook for single SMS message
 */
export const useSmsMessage = (id: string) => {
  return useQuery({
    queryKey: SMS_QUERY_KEYS.MESSAGE_DETAIL(id),
    queryFn: () => SmsService.getMessage(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
