import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SmsService, SmsBusinessService } from '../services';
import { SMS_QUERY_KEYS } from '../constants/sms.constants';
import type { CreateSmsTemplateDto, UpdateSmsTemplateDto } from '../../types';

/**
 * Hook for SMS templates management
 */
export const useSmsTemplates = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  status?: string;
}) => {
  return useQuery({
    queryKey: SMS_QUERY_KEYS.TEMPLATES_LIST(params || {}),
    queryFn: () => SmsBusinessService.getTemplatesWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for single SMS template
 */
export const useSmsTemplate = (id: string) => {
  return useQuery({
    queryKey: SMS_QUERY_KEYS.TEMPLATE_DETAIL(id),
    queryFn: () => SmsService.getTemplate(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for creating SMS template
 */
export const useCreateSmsTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSmsTemplateDto) => 
      SmsBusinessService.createTemplateWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate templates list
      queryClient.invalidateQueries({
        queryKey: SMS_QUERY_KEYS.TEMPLATES_ALL,
      });
    },
  });
};

/**
 * Hook for updating SMS template
 */
export const useUpdateSmsTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSmsTemplateDto }) => 
      SmsService.updateTemplate(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate templates list and specific template
      queryClient.invalidateQueries({
        queryKey: SMS_QUERY_KEYS.TEMPLATES_ALL,
      });
      queryClient.invalidateQueries({
        queryKey: SMS_QUERY_KEYS.TEMPLATE_DETAIL(id),
      });
    },
  });
};

/**
 * Hook for deleting SMS template
 */
export const useDeleteSmsTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => SmsService.deleteTemplate(id),
    onSuccess: () => {
      // Invalidate templates list
      queryClient.invalidateQueries({
        queryKey: SMS_QUERY_KEYS.TEMPLATES_ALL,
      });
    },
  });
};

/**
 * Hook for template preview
 */
export const useSmsTemplatePreview = () => {
  return useMutation({
    mutationFn: ({ templateId, variables }: { 
      templateId: string; 
      variables: Record<string, string> 
    }) => SmsBusinessService.previewTemplateWithBusinessLogic(templateId, variables),
  });
};

/**
 * Hook for template categories with counts
 */
export const useSmsTemplateCategories = () => {
  return useQuery({
    queryKey: SMS_QUERY_KEYS.TEMPLATE_CATEGORIES,
    queryFn: () => SmsBusinessService.getTemplateCategoriesWithCounts(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
