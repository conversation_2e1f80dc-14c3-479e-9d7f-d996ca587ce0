import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Typography } from '@/shared/components/common';
import { SmsSendForm } from '../components';

/**
 * SMS Send Page - G<PERSON><PERSON> tin nhắn SMS nhanh
 */
const SmsSendPage: React.FC = () => {
  const { t } = useTranslation(['common', 'sms']);
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Redirect to SMS overview or messages list
    navigate('/marketing/sms');
  };

  const handleCancel = () => {
    // Go back to SMS overview
    navigate('/marketing/sms');
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="space-y-2">
          <Typography variant="h1">
            {t('sms:send.title', 'Gửi SMS')}
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            {t('sms:send.description', '<PERSON><PERSON><PERSON> tin nhắn SMS nhanh chóng đến khách hàng của bạn')}
          </Typography>
        </div>

        {/* Send Form */}
        <SmsSendForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default SmsSendPage;
