import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Typography } from '@/shared/components/common';
import { SmsTemplateForm } from '../components';

/**
 * SMS Template Create Page - Tạo mẫu tin nhắn SMS
 */
const SmsTemplateCreatePage: React.FC = () => {
  const { t } = useTranslation(['common', 'sms']);
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Redirect to SMS templates list
    navigate('/marketing/sms/templates');
  };

  const handleCancel = () => {
    // Go back to SMS templates list
    navigate('/marketing/sms/templates');
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="space-y-2">
          <Typography variant="h1">
            {t('sms:template.createTitle', 'Tạo mẫu tin nhắn SMS')}
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            {t('sms:template.createDescription', 'Tạo mẫu tin nhắn SMS để sử dụng trong các chiến dịch marketing')}
          </Typography>
        </div>

        {/* Template Form */}
        <SmsTemplateForm
          mode="create"
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default SmsTemplateCreatePage;
