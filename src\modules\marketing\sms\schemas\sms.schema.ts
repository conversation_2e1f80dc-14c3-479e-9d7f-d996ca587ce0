import { z } from 'zod';

/**
 * SMS Send Form Schema
 */
export const smsSendFormSchema = z.object({
  brandnameId: z.string()
    .min(1, 'Vui lòng chọn brandname'),
  
  templateId: z.string()
    .optional(),
  
  recipients: z.object({
    type: z.enum(['single', 'multiple', 'file'], {
      errorMap: () => ({ message: 'Vui lòng chọn loại người nhận' })
    }),
    
    // Single recipient
    phone: z.string()
      .optional()
      .refine((val, ctx) => {
        if (ctx.parent.type === 'single' && !val) {
          return false;
        }
        if (val && !/^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/.test(val)) {
          return false;
        }
        return true;
      }, 'Số điện thoại không hợp lệ'),
    
    // Multiple recipients
    phones: z.array(z.string())
      .optional()
      .refine((val, ctx) => {
        if (ctx.parent.type === 'multiple' && (!val || val.length === 0)) {
          return false;
        }
        if (val) {
          return val.every(phone => /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/.test(phone));
        }
        return true;
      }, 'Danh sách số điện thoại không hợp lệ'),
    
    // File upload
    file: z.any().optional(),
  }),
  
  message: z.object({
    content: z.string()
      .min(1, 'Nội dung tin nhắn không được để trống')
      .max(1000, 'Nội dung tin nhắn không được quá 1000 ký tự'),
    
    variables: z.record(z.string())
      .optional(),
  }),
  
  schedule: z.object({
    type: z.enum(['now', 'later'], {
      errorMap: () => ({ message: 'Vui lòng chọn thời gian gửi' })
    }),
    
    datetime: z.string()
      .optional()
      .refine((val, ctx) => {
        if (ctx.parent.type === 'later' && !val) {
          return false;
        }
        if (val) {
          const date = new Date(val);
          return date > new Date();
        }
        return true;
      }, 'Thời gian gửi phải sau thời điểm hiện tại'),
  }),
  
  options: z.object({
    enableDeliveryReport: z.boolean().default(true),
    enableOptOut: z.boolean().default(true),
  }).optional(),
});

/**
 * SMS Template Form Schema
 */
export const smsTemplateFormSchema = z.object({
  name: z.string()
    .min(1, 'Tên mẫu tin nhắn không được để trống')
    .max(100, 'Tên mẫu tin nhắn không được quá 100 ký tự')
    .regex(/^[a-zA-Z0-9\s\-_àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ]+$/, 
      'Tên mẫu tin nhắn chỉ được chứa chữ cái, số, dấu gạch ngang và gạch dưới'),
  
  description: z.string()
    .max(500, 'Mô tả không được quá 500 ký tự')
    .optional(),
  
  category: z.enum([
    'marketing',
    'transactional', 
    'reminder',
    'alert',
    'otp',
    'notification',
    'welcome',
    'promotional'
  ], {
    errorMap: () => ({ message: 'Vui lòng chọn danh mục' })
  }),
  
  content: z.object({
    text: z.string()
      .min(1, 'Nội dung mẫu tin nhắn không được để trống')
      .max(1000, 'Nội dung mẫu tin nhắn không được quá 1000 ký tự'),
    
    variables: z.array(z.object({
      name: z.string()
        .min(1, 'Tên biến không được để trống')
        .regex(/^[a-zA-Z][a-zA-Z0-9_]*$/, 'Tên biến phải bắt đầu bằng chữ cái và chỉ chứa chữ cái, số, gạch dưới'),
      
      description: z.string()
        .min(1, 'Mô tả biến không được để trống'),
      
      type: z.enum(['text', 'number', 'date', 'url', 'phone'], {
        errorMap: () => ({ message: 'Vui lòng chọn loại biến' })
      }),
      
      required: z.boolean().default(false),
      
      defaultValue: z.string().optional(),
      
      validation: z.object({
        minLength: z.number().min(0).optional(),
        maxLength: z.number().min(1).optional(),
        pattern: z.string().optional(),
      }).optional(),
    })).default([]),
  }),
  
  tags: z.array(z.string())
    .default([]),
  
  language: z.string()
    .default('vi'),
  
  isDefault: z.boolean()
    .default(false),
  
  status: z.enum(['draft', 'active', 'inactive'], {
    errorMap: () => ({ message: 'Vui lòng chọn trạng thái' })
  }).default('draft'),
});

/**
 * SMS Template Variable Schema
 */
export const smsTemplateVariableSchema = z.object({
  name: z.string()
    .min(1, 'Tên biến không được để trống')
    .regex(/^[a-zA-Z][a-zA-Z0-9_]*$/, 'Tên biến phải bắt đầu bằng chữ cái và chỉ chứa chữ cái, số, gạch dưới'),
  
  description: z.string()
    .min(1, 'Mô tả biến không được để trống'),
  
  type: z.enum(['text', 'number', 'date', 'url', 'phone']),
  
  required: z.boolean().default(false),
  
  defaultValue: z.string().optional(),
  
  validation: z.object({
    minLength: z.number().min(0).optional(),
    maxLength: z.number().min(1).optional(),
    pattern: z.string().optional(),
  }).optional(),
});

/**
 * Phone Number Validation Schema
 */
export const phoneNumberSchema = z.string()
  .regex(/^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/, 'Số điện thoại không hợp lệ');

/**
 * Multiple Phone Numbers Schema
 */
export const phoneNumbersSchema = z.array(phoneNumberSchema)
  .min(1, 'Phải có ít nhất 1 số điện thoại')
  .max(1000, 'Không được quá 1000 số điện thoại');

// Export types
export type SmsSendFormData = z.infer<typeof smsSendFormSchema>;
export type SmsTemplateFormData = z.infer<typeof smsTemplateFormSchema>;
export type SmsTemplateVariableData = z.infer<typeof smsTemplateVariableSchema>;
