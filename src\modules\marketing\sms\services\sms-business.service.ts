import { SmsService } from './sms.service';
import type {
  SendSmsMessageDto,
  SendBulkSmsMessageDto,
  CreateSmsTemplateDto,
  SmsTemplateDto,
  SmsMessageDto,
  SmsBrandnameDto,
} from '../../types';

/**
 * SMS Business Logic Service
 */
export class SmsBusinessService {
  /**
   * Get active SMS brandnames with business logic
   */
  static async getActiveBrandnames(): Promise<SmsBrandnameDto[]> {
    const brandnames = await SmsService.getBrandnames();
    
    // Filter only active brandnames
    return brandnames.filter(brandname => brandname.status === 'active');
  }

  /**
   * Get SMS templates with business logic
   */
  static async getTemplatesWithBusinessLogic(params?: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    status?: string;
  }): Promise<{
    items: SmsTemplateDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const defaultParams = {
      page: 1,
      limit: 20,
      ...params,
    };

    // Validate limit
    if (defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    return SmsService.getTemplates(defaultParams);
  }

  /**
   * Create SMS template with business logic
   */
  static async createTemplateWithBusinessLogic(data: CreateSmsTemplateDto): Promise<SmsTemplateDto> {
    // Validate template content
    if (data.content && data.content.length > 1000) {
      throw new Error('Template content cannot exceed 1000 characters');
    }

    // Validate template name uniqueness (would be handled by backend)
    // Additional business logic can be added here

    return SmsService.createTemplate(data);
  }

  /**
   * Send SMS message with business logic
   */
  static async sendMessageWithBusinessLogic(data: SendSmsMessageDto): Promise<SmsMessageDto> {
    // Validate phone number format
    const phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
    if (!phoneRegex.test(data.phone)) {
      throw new Error('Invalid Vietnamese phone number format');
    }

    // Validate message content
    if (data.content && data.content.length > 1000) {
      throw new Error('Message content cannot exceed 1000 characters');
    }

    // Check if brandname is active
    const brandnames = await SmsService.getBrandnames();
    const brandname = brandnames.find(b => b.id === data.brandnameId);
    if (!brandname || brandname.status !== 'active') {
      throw new Error('Selected brandname is not active');
    }

    return SmsService.sendMessage(data);
  }

  /**
   * Send bulk SMS messages with business logic
   */
  static async sendBulkMessagesWithBusinessLogic(data: SendBulkSmsMessageDto): Promise<{
    success: number;
    failed: number;
    messages: SmsMessageDto[];
  }> {
    // Validate phone numbers
    const phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
    const invalidPhones = data.phones.filter(phone => !phoneRegex.test(phone));
    
    if (invalidPhones.length > 0) {
      throw new Error(`Invalid phone numbers: ${invalidPhones.join(', ')}`);
    }

    // Validate bulk limit
    if (data.phones.length > 1000) {
      throw new Error('Cannot send to more than 1000 recipients at once');
    }

    // Validate message content
    if (data.content && data.content.length > 1000) {
      throw new Error('Message content cannot exceed 1000 characters');
    }

    // Check if brandname is active
    const brandnames = await SmsService.getBrandnames();
    const brandname = brandnames.find(b => b.id === data.brandnameId);
    if (!brandname || brandname.status !== 'active') {
      throw new Error('Selected brandname is not active');
    }

    return SmsService.sendBulkMessages(data);
  }

  /**
   * Preview template with business logic
   */
  static async previewTemplateWithBusinessLogic(
    templateId: string, 
    variables: Record<string, string>
  ): Promise<{
    content: string;
    characterCount: number;
    smsCount: number;
    estimatedCost?: number;
  }> {
    const preview = await SmsService.previewTemplate(templateId, variables);
    
    // Calculate estimated cost (example: 500 VND per SMS)
    const costPerSms = 500;
    const estimatedCost = preview.smsCount * costPerSms;

    return {
      ...preview,
      estimatedCost,
    };
  }

  /**
   * Validate and clean phone numbers
   */
  static async validateAndCleanPhoneNumbers(phones: string[]): Promise<{
    valid: string[];
    invalid: string[];
    cleaned: string[];
  }> {
    // Clean phone numbers (remove spaces, dashes, etc.)
    const cleanedPhones = phones.map(phone =>
      phone.replace(/[\s\-()]/g, '')
    );

    // Validate using API
    const validation = await SmsService.validatePhoneNumbers(cleanedPhones);

    return {
      ...validation,
      cleaned: cleanedPhones,
    };
  }

  /**
   * Get template categories with counts
   */
  static async getTemplateCategoriesWithCounts(): Promise<Array<{
    category: string;
    count: number;
    label: string;
  }>> {
    const templates = await SmsService.getTemplates({ limit: 1000 });
    
    const categories = [
      { category: 'marketing', label: 'Marketing' },
      { category: 'transactional', label: 'Giao dịch' },
      { category: 'reminder', label: 'Nhắc nhở' },
      { category: 'alert', label: 'Cảnh báo' },
      { category: 'otp', label: 'OTP' },
      { category: 'notification', label: 'Thông báo' },
      { category: 'welcome', label: 'Chào mừng' },
      { category: 'promotional', label: 'Khuyến mãi' },
    ];

    return categories.map(cat => ({
      ...cat,
      count: templates.items.filter(template => template.category === cat.category).length,
    }));
  }
}
