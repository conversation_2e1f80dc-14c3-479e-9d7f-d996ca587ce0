import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { SMS_API_ENDPOINTS } from '../constants/sms.constants';
import type {
  SendSmsMessageDto,
  SendBulkSmsMessageDto,
  CreateSmsTemplateDto,
  UpdateSmsTemplateDto,
  SmsTemplateDto,
  SmsMessageDto,
  SmsBrandnameDto,
} from '../../types';

/**
 * SMS API Service
 */
export class SmsService {
  /**
   * Get SMS brandnames
   */
  static async getBrandnames(): Promise<SmsBrandnameDto[]> {
    const response = await apiClient.get<ApiResponseDto<SmsBrandnameDto[]>>(SMS_API_ENDPOINTS.BRANDNAMES);
    return response.result;
  }

  /**
   * Get SMS templates
   */
  static async getTemplates(params?: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    status?: string;
  }): Promise<{
    items: SmsTemplateDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const response = await apiClient.get<ApiResponseDto<{
      items: SmsTemplateDto[];
      total: number;
      page: number;
      limit: number;
    }>>(SMS_API_ENDPOINTS.TEMPLATES, { params });
    return response.result;
  }

  /**
   * Get SMS template by ID
   */
  static async getTemplate(id: string): Promise<SmsTemplateDto> {
    const response = await apiClient.get<ApiResponseDto<SmsTemplateDto>>(SMS_API_ENDPOINTS.TEMPLATE_DETAIL(id));
    return response.result;
  }

  /**
   * Create SMS template
   */
  static async createTemplate(data: CreateSmsTemplateDto): Promise<SmsTemplateDto> {
    const response = await apiClient.post<ApiResponseDto<SmsTemplateDto>>(SMS_API_ENDPOINTS.TEMPLATES, data);
    return response.result;
  }

  /**
   * Update SMS template
   */
  static async updateTemplate(id: string, data: UpdateSmsTemplateDto): Promise<SmsTemplateDto> {
    const response = await apiClient.put<ApiResponseDto<SmsTemplateDto>>(SMS_API_ENDPOINTS.TEMPLATE_DETAIL(id), data);
    return response.result;
  }

  /**
   * Delete SMS template
   */
  static async deleteTemplate(id: string): Promise<void> {
    await apiClient.delete(SMS_API_ENDPOINTS.TEMPLATE_DETAIL(id));
  }

  /**
   * Send single SMS message
   */
  static async sendMessage(data: SendSmsMessageDto): Promise<SmsMessageDto> {
    const response = await apiClient.post<ApiResponseDto<SmsMessageDto>>(SMS_API_ENDPOINTS.SEND_MESSAGE, data);
    return response.result;
  }

  /**
   * Send bulk SMS messages
   */
  static async sendBulkMessages(data: SendBulkSmsMessageDto): Promise<{
    success: number;
    failed: number;
    messages: SmsMessageDto[];
  }> {
    const response = await apiClient.post<ApiResponseDto<{
      success: number;
      failed: number;
      messages: SmsMessageDto[];
    }>>(SMS_API_ENDPOINTS.SEND_BULK_MESSAGE, data);
    return response.result;
  }

  /**
   * Get SMS messages
   */
  static async getMessages(params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    brandnameId?: number;
  }): Promise<{
    items: SmsMessageDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const response = await apiClient.get<ApiResponseDto<{
      items: SmsMessageDto[];
      total: number;
      page: number;
      limit: number;
    }>>(SMS_API_ENDPOINTS.MESSAGES, { params });
    return response.result;
  }

  /**
   * Get SMS message by ID
   */
  static async getMessage(id: string): Promise<SmsMessageDto> {
    const response = await apiClient.get<ApiResponseDto<SmsMessageDto>>(SMS_API_ENDPOINTS.MESSAGE_DETAIL(id));
    return response.result;
  }

  /**
   * Preview SMS template with variables
   */
  static async previewTemplate(templateId: string, variables: Record<string, string>): Promise<{
    content: string;
    characterCount: number;
    smsCount: number;
  }> {
    const response = await apiClient.post<ApiResponseDto<{
      content: string;
      characterCount: number;
      smsCount: number;
    }>>(`${SMS_API_ENDPOINTS.TEMPLATE_DETAIL(templateId)}/preview`, { variables });
    return response.result;
  }

  /**
   * Validate phone numbers
   */
  static async validatePhoneNumbers(phones: string[]): Promise<{
    valid: string[];
    invalid: string[];
  }> {
    const response = await apiClient.post<ApiResponseDto<{
      valid: string[];
      invalid: string[];
    }>>(`${SMS_API_ENDPOINTS.MESSAGES}/validate-phones`, { phones });
    return response.result;
  }
}
