# Threads Module

Module quản lý danh sách và thao tác với threads chat trong ứng dụng RedAI.

## Tổng quan

Module này cung cấp đầy đủ chức năng để quản lý threads chat bao gồm:
- Xem danh sách threads với phân trang
- <PERSON><PERSON><PERSON> kiếm threads theo tên
- Xem chi tiết thread
- Chỉnh sửa tên thread
- Xóa thread (đơn lẻ và hàng loạt)
- Sắp xếp threads theo nhiều tiêu chí

## Cấu trúc Module

```
src/modules/threads/
├── api/                    # Raw API calls
├── services/              # Business logic layer  
├── hooks/                 # React Query hooks
├── components/            # UI components
├── pages/                 # Page components
├── constants/             # Query keys và constants
└── index.ts              # Module exports
```

## API Endpoints

Module sử dụng các API endpoints sau:

- `GET /v1/user/chat/threads` - L<PERSON>y danh sách threads
- `GET /v1/user/chat/threads/{threadId}` - <PERSON><PERSON>y chi tiết thread
- `PUT /v1/user/chat/threads/{threadId}` - Cậ<PERSON> nhật tên thread
- `DELETE /v1/user/chat/threads/{threadId}` - Xóa thread

## Sử dụng

### 1. Import Module

```typescript
import { 
  ThreadsPage, 
  ThreadsList, 
  useThreads, 
  useThreadDetail,
  ThreadsService 
} from '@/modules/threads';
```

### 2. Sử dụng Page Component

```typescript
// Sử dụng như một trang độc lập
<ThreadsPage 
  onThreadSelect={(threadId) => console.log('Selected:', threadId)}
  onCreateNewThread={() => console.log('Create new thread')}
  selectedThreadId="current-thread-id"
  layout="full" // hoặc "sidebar"
/>
```

### 3. Sử dụng Components riêng lẻ

```typescript
// Chỉ sử dụng danh sách threads
<ThreadsList
  selectedThreadId="current-thread-id"
  onThreadSelect={(threadId) => handleSelect(threadId)}
  onThreadDeleted={(threadId) => handleDeleted(threadId)}
  pageSize={20}
/>
```

### 4. Sử dụng Hooks

```typescript
// Lấy danh sách threads
const { data: threads, isLoading } = useThreads({
  page: 1,
  limit: 20,
  sortBy: 'updatedAt',
  sortDirection: 'DESC'
});

// Lấy chi tiết thread
const { data: threadDetail } = useThreadDetail(threadId);

// Cập nhật thread
const updateThread = useUpdateThread({
  onSuccess: () => console.log('Updated successfully')
});

// Xóa thread
const deleteThread = useDeleteThread({
  onSuccess: () => console.log('Deleted successfully')
});
```

### 5. Sử dụng Services

```typescript
// Gọi trực tiếp service (không khuyến khích, nên dùng hooks)
const threads = await ThreadsService.getThreads({
  page: 1,
  limit: 20
});

const threadDetail = await ThreadsService.getThreadDetail(threadId);
```

## Features

### 1. Phân trang và Sắp xếp
- Hỗ trợ phân trang với `page` và `limit`
- Sắp xếp theo `updatedAt`, `createdAt`, `name`
- Hướng sắp xếp `ASC` hoặc `DESC`

### 2. Tìm kiếm
- Tìm kiếm real-time theo tên thread
- Debounce để tối ưu performance
- Highlight kết quả tìm kiếm

### 3. Optimistic Updates
- Cập nhật UI ngay lập tức khi edit tên thread
- Rollback nếu API call thất bại
- Loading states cho UX tốt hơn

### 4. Bulk Operations
- Chọn nhiều threads để xóa hàng loạt
- Confirmation dialog cho các thao tác nguy hiểm

### 5. Responsive Design
- Hỗ trợ dark mode
- Responsive trên mobile và desktop
- Layout linh hoạt (full page hoặc sidebar)

## Customization

### 1. Styling
Components sử dụng Tailwind CSS và hỗ trợ dark mode. Có thể customize bằng cách override classes:

```typescript
<ThreadsList 
  className="custom-threads-list"
  // ... other props
/>
```

### 2. Query Options
Có thể customize React Query options:

```typescript
const { data } = useThreads(query, {
  staleTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  // ... other options
});
```

### 3. Business Logic
Có thể extend ThreadsService để thêm logic custom:

```typescript
class CustomThreadsService extends ThreadsService {
  static async getThreadsWithCustomLogic(query) {
    // Custom implementation
    return super.getThreads(query);
  }
}
```

## Error Handling

Module xử lý lỗi ở nhiều tầng:

1. **API Layer**: Axios interceptors và error responses
2. **Service Layer**: Validation và business logic errors  
3. **Hook Layer**: React Query error handling
4. **Component Layer**: UI error states và fallbacks

## Performance

- **React Query Caching**: Cache data 5 phút cho threads list
- **Optimistic Updates**: UI updates ngay lập tức
- **Debounced Search**: Giảm số lượng API calls
- **Pagination**: Load data theo chunks nhỏ
- **Memoization**: Optimize re-renders với useMemo

## Testing

```bash
# Run tests cho module
npm test src/modules/threads

# Run specific test file
npm test ThreadsList.test.tsx
```

## Dependencies

Module phụ thuộc vào:
- `@tanstack/react-query` - Data fetching và caching
- `date-fns` - Date formatting
- `@/shared/api` - API client
- `@/shared/types` - TypeScript types
