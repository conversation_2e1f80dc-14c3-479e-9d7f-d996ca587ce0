/**
 * Threads API - Raw API calls
 * Xử lý tất cả REST API calls cho threads management
 */

import { apiClient } from '@/shared/api';
import { 
  GetThreadsQuery, 
  GetThreadsResponse, 
  ThreadDetailResponse,
  UpdateThreadRequest 
} from '@/shared/types/chat-streaming.types';

/**
 * L<PERSON>y danh sách threads
 * Endpoint: GET /v1/user/chat/threads
 */
export const getThreads = async (query?: GetThreadsQuery) => {
  // Tạo query parameters
  const params = new URLSearchParams();
  if (query?.page) params.append('page', query.page.toString());
  if (query?.limit) params.append('limit', query.limit.toString());
  if (query?.sortBy) params.append('sortBy', query.sortBy);
  if (query?.sortDirection) params.append('sortDirection', query.sortDirection);

  const url = `/user/chat/threads${params.toString() ? `?${params.toString()}` : ''}`;
  
  return apiClient.get<GetThreadsResponse>(url);
};

/**
 * Lấy chi tiết thread
 * Endpoint: GET /v1/user/chat/threads/{threadId}
 */
export const getThreadDetail = async (threadId: string) => {
  return apiClient.get<ThreadDetailResponse>(`/user/chat/threads/${threadId}`);
};

/**
 * Cập nhật tên thread
 * Endpoint: PUT /v1/user/chat/threads/{threadId}
 */
export const updateThread = async (threadId: string, data: UpdateThreadRequest) => {
  return apiClient.put<ThreadDetailResponse>(`/user/chat/threads/${threadId}`, data);
};

/**
 * Xóa thread
 * Endpoint: DELETE /v1/user/chat/threads/{threadId}
 */
export const deleteThread = async (threadId: string) => {
  return apiClient.delete<void>(`/user/chat/threads/${threadId}`);
};
