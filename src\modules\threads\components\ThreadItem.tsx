/**
 * ThreadItem Component
 * Component hiển thị một thread item trong danh sách
 */

import React, { useState } from 'react';
import { ThreadItem as ThreadItemType } from '@/shared/types/chat-streaming.types';
import { useOptimisticUpdateThread, useDeleteThread } from '../hooks';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';

interface ThreadItemProps {
  /**
   * Dữ liệu thread
   */
  thread: ThreadItemType;

  /**
   * Callback khi click vào thread
   */
  onClick?: ((threadId: string) => void) | undefined;

  /**
   * Thread hiện tại đang được chọn
   */
  isSelected?: boolean;

  /**
   * Callback khi thread được xóa thành công
   */
  onDeleted?: (threadId: string) => void;
}

/**
 * Component hiển thị một thread item
 */
export const ThreadItem: React.FC<ThreadItemProps> = ({
  thread,
  onClick,
  isSelected = false,
  onDeleted
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(thread.name);
  const [showActions, setShowActions] = useState(false);

  const { optimisticUpdate, isLoading: isUpdating } = useOptimisticUpdateThread();
  const deleteThread = useDeleteThread({
    onSuccess: () => {
      onDeleted?.(thread.threadId);
    }
  });

  // Xử lý click vào thread
  const handleClick = () => {
    if (!isEditing) {
      onClick?.(thread.threadId);
    }
  };

  // Xử lý lưu tên mới
  const handleSave = async () => {
    if (editName.trim() && editName.trim() !== thread.name) {
      try {
        await optimisticUpdate(thread.threadId, editName.trim());
        setIsEditing(false);
      } catch (error) {
        console.error('Failed to update thread name:', error);
        // Reset về tên cũ nếu lỗi
        setEditName(thread.name);
      }
    } else {
      setIsEditing(false);
      setEditName(thread.name);
    }
  };

  // Xử lý hủy edit
  const handleCancel = () => {
    setIsEditing(false);
    setEditName(thread.name);
  };

  // Xử lý xóa thread
  const handleDelete = async () => {
    if (window.confirm('Bạn có chắc chắn muốn xóa cuộc hội thoại này?')) {
      try {
        await deleteThread.mutateAsync(thread.threadId);
      } catch (error) {
        console.error('Failed to delete thread:', error);
      }
    }
  };

  // Format thời gian
  const formatTime = (timestamp: number) => {
    try {
      return formatDistanceToNow(new Date(timestamp), {
        addSuffix: true,
        locale: vi
      });
    } catch {
      return 'Không xác định';
    }
  };

  return (
    <div
      className={`
        group relative p-3 rounded-lg cursor-pointer transition-all duration-200
        ${isSelected
          ? 'bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500'
          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
        }
        ${isUpdating || deleteThread.isPending ? 'opacity-50' : ''}
      `}
      onClick={handleClick}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Thread Name */}
      <div className="flex items-center justify-between mb-2">
        {isEditing ? (
          <div className="flex-1 flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
            <input
              type="text"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              className="flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-red-500"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleSave();
                if (e.key === 'Escape') handleCancel();
              }}
            />
            <button
              onClick={handleSave}
              disabled={isUpdating}
              className="p-1 text-green-600 hover:text-green-700"
              title="Lưu"
            >
              ✓
            </button>
            <button
              onClick={handleCancel}
              className="p-1 text-red-600 hover:text-red-700"
              title="Hủy"
            >
              ✕
            </button>
          </div>
        ) : (
          <>
            <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate flex-1">
              {thread.name}
              {/* Edit button */}
              {(showActions || isSelected) && (
                <button
                  onClick={() => setIsEditing(true)}
                  disabled={isUpdating || deleteThread.isPending}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  title="Chỉnh sửa tên"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
              )}
            </h3>

            {/* Actions */}
            {(showActions || isSelected) && (
              <div className="flex items-center gap-1 ml-2" onClick={(e) => e.stopPropagation()}>

                <button
                  onClick={handleDelete}
                  disabled={isUpdating || deleteThread.isPending}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  title="Xóa cuộc hội thoại"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Thread Info */}
      <div className="text-xs text-gray-500 dark:text-gray-400">
        Cập nhật {formatTime(thread.updatedAt)}
      </div>

      {/* Loading indicator */}
      {(isUpdating || deleteThread.isPending) && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-900/50 rounded-lg">
          <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
};


