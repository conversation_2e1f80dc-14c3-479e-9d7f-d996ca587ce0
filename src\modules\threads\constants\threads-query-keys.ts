/**
 * React Query keys cho Threads module
 */

export const THREADS_QUERY_KEYS = {
  /**
   * Base key cho tất cả threads queries
   */
  ALL: ['threads'] as const,

  /**
   * Key cho danh sách threads
   */
  LIST: (params?: Record<string, unknown>) => 
    [...THREADS_QUERY_KEYS.ALL, 'list', params] as const,

  /**
   * Key cho chi tiết thread
   */
  DETAIL: (threadId: string) => 
    [...THREADS_QUERY_KEYS.ALL, 'detail', threadId] as const,

  /**
   * Key cho threads với pagination
   */
  PAGINATED: (page?: number, limit?: number, sortBy?: string, sortDirection?: string) =>
    [...THREADS_QUERY_KEYS.ALL, 'paginated', { page, limit, sortBy, sortDirection }] as const,
} as const;
