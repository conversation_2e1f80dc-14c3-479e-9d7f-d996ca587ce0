/**
 * Hook để xử lý xóa active thread với integration
 * <PERSON><PERSON> gồm tìm thread tiếp theo, switch thread, và đồng bộ Redux
 */

import { useState, useCallback } from 'react';
import { useMutation, useQueryClient, InfiniteData } from '@tanstack/react-query';
import { useDispatch } from 'react-redux';
import { ThreadsService } from '../services/threads.service';
import { ThreadItem, GetThreadsResponse } from '@/shared/types/chat-streaming.types';
import { setCurrentThread, threadDeleted } from '@/shared/store/slices/threadIntegrationSlice';
import { THREADS_QUERY_KEYS } from '../constants';
import { markThreadAsDeleted } from '../services/deleted-thread-tracker.service';

interface UseActiveThreadDeleteProps {
  /**
   * Danh sách threads hiện có để tìm thread tiếp theo
   */
  availableThreads: ThreadItem[];
  
  /**
   * External chat stream để switch thread (optional)
   */
  externalChatStream?: {
    switchToThread?: (threadId: string) => Promise<void>;
    createNewThread?: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  } | undefined;
  
  /**
   * Callback khi xóa thành công
   */
  onThreadDeleted?: (deletedThreadId: string, nextThreadId: string | null) => void;
}

export const useActiveThreadDelete = ({ 
  availableThreads, 
  externalChatStream,
  onThreadDeleted 
}: UseActiveThreadDeleteProps) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [deletingThreadId, setDeletingThreadId] = useState<string | null>(null);

  // Mutation để delete active thread với integration
  const deleteActiveThreadMutation = useMutation({
    mutationFn: async (threadId: string) => {
      setDeletingThreadId(threadId);

      console.log('[useActiveThreadDelete] Deleting active thread with integration:', {
        threadId,
        availableThreadsCount: availableThreads.length
      });

      // Xóa thread từ API TRƯỚC
      await ThreadsService.deleteThread(threadId);
      console.log('[useActiveThreadDelete] Thread deleted from API successfully:', threadId);

      // Tìm thread tiếp theo để switch
      let nextThreadId: string | null = null;
      let isNewThreadCreated = false;

      const remainingThreads = availableThreads.filter(t => t.threadId !== threadId);

      console.log('[useActiveThreadDelete] Remaining threads after delete:', {
        remainingCount: remainingThreads.length,
        remainingThreads: remainingThreads.map(t => ({ id: t.threadId, name: t.name }))
      });

      if (remainingThreads.length > 0) {
        // Có threads khác, chọn thread mới nhất
        const sortedThreads = remainingThreads.sort((a, b) => b.updatedAt - a.updatedAt);
        nextThreadId = sortedThreads[0]?.threadId || null;

        console.log('[useActiveThreadDelete] Selected next thread:', nextThreadId);

        // Auto-switch nếu có external chat stream
        if (externalChatStream && externalChatStream.switchToThread && nextThreadId) {
          try {
            console.log('[useActiveThreadDelete] Switching to next thread via external chat stream...');
            await externalChatStream.switchToThread(nextThreadId);
            console.log('[useActiveThreadDelete] Successfully switched to next thread:', nextThreadId);
          } catch (switchError) {
            console.error('[useActiveThreadDelete] Failed to switch to next thread:', switchError);
            // Không set nextThreadId = null, vẫn giữ để Redux có thể update
            console.warn('[useActiveThreadDelete] Will update Redux state anyway despite switch failure');
          }
        } else {
          console.warn('[useActiveThreadDelete] No external chat stream available, will only update Redux state');
        }
      } else {
        // Không có thread nào còn lại, thử tạo thread mới
        console.log('[useActiveThreadDelete] No threads remaining, attempting to create new thread...');

        if (externalChatStream && externalChatStream.createNewThread) {
          try {
            console.log('[useActiveThreadDelete] Creating new thread via external chat stream...');
            const newThread = await externalChatStream.createNewThread('New Chat');
            nextThreadId = newThread.threadId;
            isNewThreadCreated = true;

            console.log('[useActiveThreadDelete] Successfully created new thread:', newThread);
          } catch (error) {
            console.error('[useActiveThreadDelete] Failed to create new thread:', error);
            console.log('[useActiveThreadDelete] Will clear current thread in Redux');
            nextThreadId = null;
          }
        } else {
          console.warn('[useActiveThreadDelete] No external chat stream available to create new thread');
          console.log('[useActiveThreadDelete] Will clear current thread in Redux');
          nextThreadId = null;
        }
      }

      return { deletedThreadId: threadId, nextThreadId, isNewThreadCreated };
    },
    onSuccess: ({ deletedThreadId, nextThreadId, isNewThreadCreated }) => {
      console.log('[useActiveThreadDelete] Active thread deleted successfully:', {
        deletedThreadId,
        nextThreadId,
        isNewThreadCreated
      });

      // Mark thread as deleted để prevent future API calls
      markThreadAsDeleted(deletedThreadId);

      // Update Redux state IMMEDIATELY và FORCE re-render
      console.log('[useActiveThreadDelete] Updating Redux state immediately...');

      if (nextThreadId) {
        console.log('[useActiveThreadDelete] Setting next thread as current:', nextThreadId);
        dispatch(setCurrentThread({
          threadId: nextThreadId,
          threadName: `Thread ${nextThreadId.slice(-8)}`, // Fallback name
          isNew: isNewThreadCreated || false
        }));
      } else {
        console.log('[useActiveThreadDelete] Clearing current thread (no next thread available)');
        dispatch(setCurrentThread({
          threadId: null,
          threadName: null,
          isNew: false
        }));
      }

      // Dispatch thread deleted event để ChatPanel và ThreadsPage có thể listen
      console.log('[useActiveThreadDelete] Dispatching thread deleted event...');
      dispatch(threadDeleted({
        threadId: deletedThreadId,
        nextThreadId: nextThreadId
      }));
      
      // Remove thread detail từ cache để tránh 404 errors
      console.log('[useActiveThreadDelete] Removing thread detail from cache:', deletedThreadId);
      queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(deletedThreadId) });

      // Optimistic update: Remove thread từ tất cả list queries ngay lập tức
      console.log('[useActiveThreadDelete] Performing optimistic update to remove thread from cache...');
      queryClient.setQueriesData(
        {
          queryKey: THREADS_QUERY_KEYS.ALL,
          predicate: (query) => {
            const key = query.queryKey;
            return key.includes('list') || key.includes('paginated') || key.includes('infinite');
          }
        },
        (oldData: InfiniteData<GetThreadsResponse> | GetThreadsResponse | ThreadItem[] | unknown) => {
          if (!oldData) return oldData;

          // Handle infinite query data structure
          if ((oldData as InfiniteData<GetThreadsResponse>).pages) {
            const infiniteData = oldData as InfiniteData<GetThreadsResponse>;
            const updatedData = {
              ...infiniteData,
              pages: infiniteData.pages.map((page: GetThreadsResponse) => ({
                ...page,
                items: page.items ? page.items.filter((thread: ThreadItem) => thread.threadId !== deletedThreadId) : [],
                meta: page.meta ? {
                  ...page.meta,
                  totalItems: Math.max(0, (page.meta.totalItems || 0) - 1)
                } : page.meta
              }))
            };

            console.log('[useActiveThreadDelete] Updated infinite query data:', {
              originalPagesCount: infiniteData.pages.length,
              updatedPagesCount: updatedData.pages.length,
              deletedThreadId
            });

            return updatedData;
          }

          // Handle regular query data structure
          if (Array.isArray(oldData)) {
            const threadsArray = oldData as ThreadItem[];
            const filtered = threadsArray.filter((thread: ThreadItem) => thread.threadId !== deletedThreadId);

            console.log('[useActiveThreadDelete] Updated array data:', {
              originalCount: threadsArray.length,
              filteredCount: filtered.length,
              deletedThreadId
            });

            return filtered;
          }

          return oldData;
        }
      );

      // Force invalidate và refetch tất cả list queries để đảm bảo UI update
      console.log('[useActiveThreadDelete] Force invalidating queries to refresh UI...');

      // Invalidate infinite queries
      queryClient.invalidateQueries({
        queryKey: ['threads', 'infinite'],
        refetchType: 'active'
      });

      // Invalidate list queries
      queryClient.invalidateQueries({
        queryKey: THREADS_QUERY_KEYS.ALL,
        predicate: (query) => {
          const key = query.queryKey;
          const isListQuery = key.includes('list') || key.includes('paginated') || key.includes('infinite');
          const isDetailQuery = key.includes('detail');
          return isListQuery && !isDetailQuery;
        },
        refetchType: 'active'
      });
      
      // Clear loading state
      setDeletingThreadId(null);
      
      // Trigger callback
      onThreadDeleted?.(deletedThreadId, nextThreadId);
    },
    onError: (error) => {
      console.error('[useActiveThreadDelete] Failed to delete active thread:', error);
      setDeletingThreadId(null);
    },
  });

  // Function để delete active thread
  const deleteActiveThread = useCallback((threadId: string) => {
    deleteActiveThreadMutation.mutate(threadId);
  }, [deleteActiveThreadMutation]);

  // Function để check xem thread có đang được xóa không
  const isThreadDeleting = useCallback((threadId: string) => {
    return deletingThreadId === threadId;
  }, [deletingThreadId]);

  return {
    // Actions
    deleteActiveThread,
    
    // State
    isThreadDeleting,
    isDeleting: deleteActiveThreadMutation.isPending,
    error: deleteActiveThreadMutation.error,
  };
};
