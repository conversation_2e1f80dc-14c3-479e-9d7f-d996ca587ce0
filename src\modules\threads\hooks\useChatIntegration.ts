/**
 * Hook để tích hợp với chat panel service
 */

import { useEffect, useCallback } from 'react';
import { ChatIntegrationService } from '../services/chat-integration.service';
import { UseChatStreamReturn } from '@/shared/hooks/common/useChatStream';
import { useChatPanelIntegration } from '@/shared/hooks/useChatPanelIntegration';
import { ThreadItem } from '@/shared/types/chat-streaming.types';

interface UseChatIntegrationProps {
  /**
   * Chat stream instance từ chat panel
   */
  chatStream?: UseChatStreamReturn | null;

  /**
   * Available threads để sync
   */
  availableThreads?: ThreadItem[];

  /**
   * Enable integration hay không
   */
  enabled?: boolean;
}

interface UseChatIntegrationReturn {
  /**
   * Load thread vào chat panel
   */
  loadThreadToChat: (threadId: string) => Promise<void>;

  /**
   * Switch sang thread khác
   */
  switchToThread: (threadId: string) => Promise<void>;

  /**
   * Get current active thread ID
   */
  getCurrentThreadId: () => string | null;

  /**
   * Check integration status
   */
  integrationStatus: {
    hasChatStream: boolean;
    hasFocusFunction: boolean;
    isFullyIntegrated: boolean;
  };

  /**
   * Check if integration is available
   */
  isIntegrationAvailable: boolean;
}

/**
 * Hook để tích hợp threads với chat panel
 */
export const useChatIntegration = ({
  chatStream,
  availableThreads = [],
  enabled = true
}: UseChatIntegrationProps): UseChatIntegrationReturn => {

  // Chat panel integration hook
  const { getChatStream, getFocusChatInput, isRegistered } = useChatPanelIntegration();

  // Get service instance
  const chatIntegrationService = ChatIntegrationService.getInstance();

  // Setup integration khi component mount
  useEffect(() => {
    if (!enabled) return;

    console.log('[useChatIntegration] Setting up chat panel integration');

    // Sử dụng ChatPanel's chatStream nếu có, fallback về chatStream từ props
    const activeChatStream = getChatStream() || chatStream;
    const activeFocusFunction = getFocusChatInput();

    if (activeChatStream && activeFocusFunction) {
      console.log('[useChatIntegration] Using ChatPanel integration');

      // Set chat panel integration
      chatIntegrationService.setChatPanelIntegration({
        chatStream: activeChatStream,
        focusChatInput: activeFocusFunction
      });
    } else if (chatStream) {
      console.log('[useChatIntegration] Using fallback chatStream from props');

      // Fallback to props chatStream
      chatIntegrationService.setChatPanelIntegration({
        chatStream,
        focusChatInput: () => console.log('[useChatIntegration] No focus function available')
      });
    } else {
      console.warn('[useChatIntegration] No chatStream available for integration');
    }

    return () => {
      // Cleanup khi unmount
      console.log('[useChatIntegration] Cleaning up chat panel integration');
    };
  }, [chatStream, availableThreads, enabled, chatIntegrationService, getChatStream, getFocusChatInput]);



  // Load thread to chat
  const loadThreadToChat = useCallback(async (threadId: string) => {
    if (!enabled) {
      console.warn('[useChatIntegration] Integration disabled, skipping load thread');
      return;
    }

    try {
      console.log('[useChatIntegration] Loading thread to chat:', threadId);
      await chatIntegrationService.loadThreadToChat(threadId);
      console.log('[useChatIntegration] Successfully loaded thread to chat:', threadId);
    } catch (error) {
      console.error('[useChatIntegration] Failed to load thread to chat:', error);
      throw error;
    }
  }, [enabled, chatIntegrationService]);

  // Switch to thread
  const switchToThread = useCallback(async (threadId: string) => {
    if (!enabled) {
      console.warn('[useChatIntegration] Integration disabled, skipping switch thread');
      return;
    }

    try {
      console.log('[useChatIntegration] Switching to thread:', threadId);
      await chatIntegrationService.switchToThread(threadId);
      console.log('[useChatIntegration] Successfully switched to thread:', threadId);
    } catch (error) {
      console.error('[useChatIntegration] Failed to switch to thread:', error);
      throw error;
    }
  }, [enabled, chatIntegrationService]);

  // Get current thread ID
  const getCurrentThreadId = useCallback(() => {
    return chatIntegrationService.getCurrentThreadId();
  }, [chatIntegrationService]);

  // Get integration status
  const integrationStatus = chatIntegrationService.getChatPanelIntegrationStatus();
  const isIntegrationAvailable = chatIntegrationService.isChatPanelIntegrationAvailable() || isRegistered();

  return {
    loadThreadToChat,
    switchToThread,
    getCurrentThreadId,
    integrationStatus,
    isIntegrationAvailable
  };
};
