/**
 * useChatStreamIntegration Hook
 * Hook để tích hợp useChatStream với ThreadsPage và React Query
 */

import { useCallback } from 'react';
import { useChatStream, UseChatStreamConfig, ThreadEventCallbacks } from '@/shared/hooks/common/useChatStream';
import { useThreadIntegrationService } from '../services/thread-integration.service';
import { useThreadErrorHandler } from '../services/thread-error-handler.service';

interface UseChatStreamIntegrationConfig extends Omit<UseChatStreamConfig, 'threadEvents'> {
  /**
   * Additional thread event callbacks
   */
  additionalCallbacks?: ThreadEventCallbacks;

  /**
   * Enable automatic React Query invalidation
   */
  enableQueryInvalidation?: boolean;
}

/**
 * Hook tích hợp useChatStream với thread management
 */
export const useChatStreamIntegration = (config: UseChatStreamIntegrationConfig) => {
  const {
    additionalCallbacks,
    enableQueryInvalidation = true,
    ...chatStreamConfig
  } = config;

  // Thread integration service để handle React Query invalidation
  const threadIntegrationService = useThreadIntegrationService(additionalCallbacks);

  // Error handler service
  const errorHandler = useThreadErrorHandler();

  // Get thread event callbacks
  const threadEventCallbacks = enableQueryInvalidation 
    ? threadIntegrationService.getThreadEventCallbacks()
    : additionalCallbacks;

  // Enhanced config với thread events
  const enhancedConfig: UseChatStreamConfig = {
    ...chatStreamConfig,
    threadEvents: threadEventCallbacks
  };

  // Use chat stream với enhanced config
  const chatStream = useChatStream(enhancedConfig);

  // Enhanced create thread method với better error handling
  const createNewThreadWithIntegration = useCallback(async (name?: string) => {
    try {
      console.log('[useChatStreamIntegration] Creating new thread:', name);

      // Retry operation với error handling
      const result = await errorHandler.retryOperation(
        () => chatStream.createNewThread(name),
        { maxRetries: 2, retryDelay: 1000 }
      );

      console.log('[useChatStreamIntegration] Thread created successfully:', result);
      return result;
    } catch (error) {
      console.error('[useChatStreamIntegration] Failed to create thread:', error);

      // Parse và handle error
      const threadError = errorHandler.parseError(error, 'CREATE');
      await errorHandler.handleError(threadError);

      throw new Error(errorHandler.getUserFriendlyMessage(threadError));
    }
  }, [chatStream, errorHandler]);

  // Enhanced switch thread method
  const switchToThreadWithIntegration = useCallback(async (threadId: string) => {
    try {
      console.log('[useChatStreamIntegration] Switching to thread:', threadId);

      // Retry operation với error handling
      await errorHandler.retryOperation(
        () => chatStream.switchToThread(threadId),
        { maxRetries: 2, retryDelay: 500 }
      );

      console.log('[useChatStreamIntegration] Successfully switched to thread:', threadId);
    } catch (error) {
      console.error('[useChatStreamIntegration] Failed to switch thread:', error);

      // Parse và handle error
      const threadError = errorHandler.parseError(error, 'SWITCH', threadId);
      await errorHandler.handleError(threadError);

      throw new Error(errorHandler.getUserFriendlyMessage(threadError));
    }
  }, [chatStream, errorHandler]);

  // Enhanced update thread name method
  const updateThreadNameWithIntegration = useCallback(async (threadId: string, newName: string) => {
    try {
      console.log('[useChatStreamIntegration] Updating thread name:', { threadId, newName });

      // Retry operation với error handling
      await errorHandler.retryOperation(
        () => chatStream.updateThreadName(threadId, newName),
        { maxRetries: 2, retryDelay: 500 }
      );

      console.log('[useChatStreamIntegration] Thread name updated successfully');
    } catch (error) {
      console.error('[useChatStreamIntegration] Failed to update thread name:', error);

      // Parse và handle error
      const threadError = errorHandler.parseError(error, 'UPDATE', threadId);
      await errorHandler.handleError(threadError);

      throw new Error(errorHandler.getUserFriendlyMessage(threadError));
    }
  }, [chatStream, errorHandler]);

  return {
    // Original chat stream methods and state
    ...chatStream,

    // Enhanced methods với integration
    createNewThread: createNewThreadWithIntegration,
    switchToThread: switchToThreadWithIntegration,
    updateThreadName: updateThreadNameWithIntegration,

    // Integration service
    threadIntegrationService,

    // Helper methods
    isIntegrationEnabled: enableQueryInvalidation,
  };
};
