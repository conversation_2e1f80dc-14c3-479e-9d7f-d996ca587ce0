/**
 * useThreadIntegrationValidation Hook
 * Hook để validate thread integration và monitor performance
 */

import { useCallback, useEffect, useRef } from 'react';
import { threadIntegrationValidator, ValidationResult } from '../utils/thread-integration-validator';

interface UseThreadIntegrationValidationOptions {
  /**
   * Enable automatic validation
   */
  autoValidate?: boolean;

  /**
   * Validation interval in milliseconds
   */
  validationInterval?: number;

  /**
   * Callback khi có validation errors
   */
  onValidationError?: (result: ValidationResult) => void;

  /**
   * Callback khi có warnings
   */
  onValidationWarning?: (result: ValidationResult) => void;

  /**
   * Enable performance monitoring
   */
  enablePerformanceMonitoring?: boolean;
}

/**
 * Hook để validate thread integration
 */
export const useThreadIntegrationValidation = (
  options: UseThreadIntegrationValidationOptions = {}
) => {
  const {
    autoValidate = false,
    validationInterval = 5000,
    onValidationError,
    onValidationWarning,
    enablePerformanceMonitoring = false
  } = options;

  const intervalRef = useRef<NodeJS.Timeout>();
  const performanceRef = useRef<Map<string, number>>(new Map());

  // Start performance timing
  const startTiming = useCallback((operation: string): (() => void) => {
    if (!enablePerformanceMonitoring) return () => {};
    
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      threadIntegrationValidator.recordOperation(operation, true, duration);
      
      console.log(`[ThreadIntegrationValidation] ${operation} completed in ${duration.toFixed(2)}ms`);
    };
  }, [enablePerformanceMonitoring]);

  // Record operation failure
  const recordFailure = useCallback((operation: string, error: Error, threadId?: string) => {
    const duration = performanceRef.current.get(operation) || 0;
    threadIntegrationValidator.recordOperation(operation, false, duration, threadId);
    
    console.error(`[ThreadIntegrationValidation] ${operation} failed:`, error);
  }, []);

  // Update ThreadsPage state
  const updateThreadsPageState = useCallback((threadId: string | null, threadName: string | null) => {
    threadIntegrationValidator.updateThreadsPageState(threadId, threadName);
    
    if (enablePerformanceMonitoring) {
      console.log('[ThreadIntegrationValidation] ThreadsPage state updated:', { threadId, threadName });
    }
  }, [enablePerformanceMonitoring]);

  // Update ChatPanel state
  const updateChatPanelState = useCallback((threadId: string | null, threadName: string | null) => {
    threadIntegrationValidator.updateChatPanelState(threadId, threadName);
    
    if (enablePerformanceMonitoring) {
      console.log('[ThreadIntegrationValidation] ChatPanel state updated:', { threadId, threadName });
    }
  }, [enablePerformanceMonitoring]);

  // Set thread switching state
  const setThreadSwitching = useCallback((isThreadSwitching: boolean) => {
    threadIntegrationValidator.setThreadSwitching(isThreadSwitching);
    
    if (enablePerformanceMonitoring) {
      console.log('[ThreadIntegrationValidation] Thread switching state:', isThreadSwitching);
    }
  }, [enablePerformanceMonitoring]);

  // Manual validation
  const validateIntegration = useCallback((): ValidationResult => {
    const result = threadIntegrationValidator.validateIntegration();
    
    if (!result.isValid && onValidationError) {
      onValidationError(result);
    }
    
    if (result.warnings.length > 0 && onValidationWarning) {
      onValidationWarning(result);
    }
    
    return result;
  }, [onValidationError, onValidationWarning]);

  // Get current state
  const getCurrentState = useCallback(() => {
    return threadIntegrationValidator.getCurrentState();
  }, []);

  // Get performance metrics
  const getPerformanceMetrics = useCallback(() => {
    return threadIntegrationValidator.getPerformanceMetrics();
  }, []);

  // Get operation history
  const getOperationHistory = useCallback(() => {
    return threadIntegrationValidator.getOperationHistory();
  }, []);

  // Clear history
  const clearHistory = useCallback(() => {
    threadIntegrationValidator.clearHistory();
  }, []);

  // Reset state
  const resetState = useCallback(() => {
    threadIntegrationValidator.resetState();
  }, []);

  // Auto validation effect
  useEffect(() => {
    if (!autoValidate) return;

    const runValidation = () => {
      const result = validateIntegration();
      
      if (enablePerformanceMonitoring) {
        console.log('[ThreadIntegrationValidation] Auto validation result:', result);
      }
    };

    intervalRef.current = setInterval(runValidation, validationInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoValidate, validationInterval, validateIntegration, enablePerformanceMonitoring]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    // State management
    updateThreadsPageState,
    updateChatPanelState,
    setThreadSwitching,

    // Performance monitoring
    startTiming,
    recordFailure,

    // Validation
    validateIntegration,
    getCurrentState,
    getPerformanceMetrics,
    getOperationHistory,

    // Utilities
    clearHistory,
    resetState,

    // Validator instance (for advanced usage)
    validator: threadIntegrationValidator
  };
};
