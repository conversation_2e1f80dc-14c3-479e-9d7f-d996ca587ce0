/**
 * React Query mutation hooks cho Threads operations
 */

import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';
import { THREADS_QUERY_KEYS } from '../constants';
import { ThreadsService } from '../services';
import {
  ThreadDetailResponse,
  UpdateThreadRequest,
  GetThreadsResponse,
  ThreadItem
} from '@/shared/types/chat-streaming.types';

/**
 * Hook để cập nhật thread
 */
export const useUpdateThread = (
  options?: UseMutationOptions<
    ThreadDetailResponse,
    Error,
    { threadId: string; data: UpdateThreadRequest }
  >
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ threadId, data }) => ThreadsService.updateThread(threadId, data),
    onSuccess: (updatedThread, { threadId }) => {
      // Invalidate và refetch threads list
      queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });
      
      // Update cache cho thread detail
      queryClient.setQueryData(
        THREADS_QUERY_KEYS.DETAIL(threadId),
        updatedThread
      );
    },
    ...options,
  });
};

/**
 * Hook để xóa thread
 */
export const useDeleteThread = (
  options?: UseMutationOptions<void, Error, string>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (threadId: string) => ThreadsService.deleteThread(threadId),
    onSuccess: (_, threadId) => {
      // Invalidate và refetch threads list
      queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });
      
      // Remove thread detail từ cache
      queryClient.removeQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) });
    },
    ...options,
  });
};

/**
 * Hook để optimistic update thread name
 */
export const useOptimisticUpdateThread = () => {
  const queryClient = useQueryClient();
  const updateMutation = useUpdateThread();

  const optimisticUpdate = async (threadId: string, newName: string) => {
    // Cancel any outgoing refetches
    await queryClient.cancelQueries({ queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) });
    await queryClient.cancelQueries({ queryKey: THREADS_QUERY_KEYS.ALL });

    // Snapshot the previous values
    const previousThreadDetail = queryClient.getQueryData(THREADS_QUERY_KEYS.DETAIL(threadId));
    const previousThreadsList = queryClient.getQueriesData({ queryKey: THREADS_QUERY_KEYS.ALL });

    // Optimistically update thread detail
    if (previousThreadDetail) {
      queryClient.setQueryData(THREADS_QUERY_KEYS.DETAIL(threadId), {
        ...previousThreadDetail,
        name: newName,
        updatedAt: Date.now()
      });
    }

    // Optimistically update threads in lists
    previousThreadsList.forEach(([queryKey, data]) => {
      if (data && typeof data === 'object' && 'items' in data) {
        const threadsData = data as GetThreadsResponse;
        const updatedItems = threadsData.items.map((thread: ThreadItem) =>
          thread.threadId === threadId
            ? { ...thread, name: newName, updatedAt: Date.now() }
            : thread
        );
        queryClient.setQueryData(queryKey, { ...threadsData, items: updatedItems });
      }
    });

    // Perform the mutation
    try {
      await updateMutation.mutateAsync({ 
        threadId, 
        data: { name: newName } 
      });
    } catch (error) {
      // Rollback on error
      if (previousThreadDetail) {
        queryClient.setQueryData(THREADS_QUERY_KEYS.DETAIL(threadId), previousThreadDetail);
      }
      previousThreadsList.forEach(([queryKey, data]) => {
        queryClient.setQueryData(queryKey, data);
      });
      throw error;
    }
  };

  return {
    optimisticUpdate,
    isLoading: updateMutation.isPending,
    error: updateMutation.error,
  };
};
