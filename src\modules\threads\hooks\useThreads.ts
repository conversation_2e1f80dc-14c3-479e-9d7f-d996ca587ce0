/**
 * React Query hooks cho Threads operations
 */

import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { THREADS_QUERY_KEYS } from '../constants';
import { ThreadsService } from '../services';
import { 
  GetThreadsQuery, 
  GetThreadsResponse 
} from '@/shared/types/chat-streaming.types';

/**
 * Hook để lấy danh sách threads
 */
export const useThreads = (
  query?: GetThreadsQuery,
  options?: UseQueryOptions<GetThreadsResponse>
) => {
  return useQuery({
    queryKey: THREADS_QUERY_KEYS.LIST(query),
    queryFn: () => ThreadsService.getThreads(query),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy danh sách threads với pagination
 */
export const useThreadsPaginated = (
  page: number = 1,
  limit: number = 20,
  sortBy: string = 'updatedAt',
  sortDirection: 'ASC' | 'DESC' = 'DESC',
  options?: UseQueryOptions<GetThreadsResponse>
) => {
  const query: GetThreadsQuery = {
    page,
    limit,
    sortBy,
    sortDirection
  };

  return useQuery({
    queryKey: THREADS_QUERY_KEYS.PAGINATED(page, limit, sortBy, sortDirection),
    queryFn: () => ThreadsService.getThreads(query),
    staleTime: 5 * 60 * 1000, // 5 minutes
    keepPreviousData: true, // Giữ data cũ khi chuyển trang
    ...options,
  });
};

/**
 * Hook để tìm kiếm threads
 */
export const useThreadsSearch = (
  searchTerm: string,
  query?: GetThreadsQuery,
  options?: UseQueryOptions<GetThreadsResponse>
) => {
  return useQuery({
    queryKey: [...THREADS_QUERY_KEYS.LIST(query), 'search', searchTerm],
    queryFn: () => ThreadsService.searchThreads(searchTerm, query),
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for search)
    enabled: searchTerm.length >= 2, // Chỉ search khi có ít nhất 2 ký tự
    ...options,
  });
};
