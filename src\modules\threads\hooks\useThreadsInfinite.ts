/**
 * React Query infinite hook cho Threads operations
 */

import {
  GetThreadsQuery,
  GetThreadsResponse
} from '@/shared/types/chat-streaming.types';
import { useInfiniteQuery, UseInfiniteQueryOptions, useQueryClient, InfiniteData } from '@tanstack/react-query';
import { useEffect, useRef, useMemo } from 'react';
import { ThreadsService } from '../services';

interface UseThreadsInfiniteOptions {
  /**
   * Số items per page
   */
  pageSize?: number;

  /**
   * Sort options
   */
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Search term
   */
  searchTerm?: string;

  /**
   * Additional query options
   */
  queryOptions?: Omit<
    UseInfiniteQueryOptions<GetThreadsResponse, Error, GetThreadsResponse, GetThreadsResponse, readonly unknown[], number>,
    'queryKey' | 'queryFn' | 'getNextPageParam' | 'initialPageParam'
  >;
}

/**
 * Hook để lấy danh sách threads với infinite scroll
 */
export const useThreadsInfinite = ({
  pageSize = 20,
  sortBy = 'updatedAt',
  sortDirection = 'DESC',
  searchTerm = '',
  queryOptions = {}
}: UseThreadsInfiniteOptions = {}) => {

  // Ref để track query parameters trước đó
  const prevQueryRef = useRef({ pageSize, sortBy, sortDirection, searchTerm });
  const queryClient = useQueryClient();

  // Temporarily disable debounce để test
  const debouncedSearchTerm = searchTerm;

  // Kiểm tra xem có đang search không
  const isSearching = debouncedSearchTerm && debouncedSearchTerm.length >= 2;

  // Memoized query key để tránh re-render
  const queryKey = useMemo(() =>
    ['threads', 'infinite', pageSize, sortBy, sortDirection, debouncedSearchTerm],
    [pageSize, sortBy, sortDirection, debouncedSearchTerm]
  );

  // Log query setup
  console.log('[useThreadsInfinite] Setting up query with:', {
    queryKey,
    pageSize,
    sortBy,
    sortDirection,
    searchTerm,
    debouncedSearchTerm,
    isSearching
  });

  const query = useInfiniteQuery({
    queryKey,
    queryFn: async ({ pageParam }) => {
      console.log('[useThreadsInfinite] queryFn called with pageParam:', pageParam);

      const page = pageParam as number;
      const queryParams: GetThreadsQuery = {
        page,
        limit: pageSize,
        sortBy,
        sortDirection
      };

      console.log('[useThreadsInfinite] Making API call with params:', queryParams);

      try {
        let result;
        // Sử dụng search nếu có search term
        if (debouncedSearchTerm && debouncedSearchTerm.length >= 2) {
          console.log('[useThreadsInfinite] Using search API with term:', debouncedSearchTerm);
          result = await ThreadsService.searchThreads(debouncedSearchTerm, queryParams);
        } else {
          console.log('[useThreadsInfinite] Using regular getThreads API');
          result = await ThreadsService.getThreads(queryParams);
        }

        console.log('[useThreadsInfinite] API call successful:', {
          itemsCount: result.items?.length || 0,
          meta: result.meta,
          hasItems: !!result.items,
          hasMeta: !!result.meta
        });

        return result;
      } catch (error) {
        console.error('[useThreadsInfinite] API call failed:', error);
        throw error;
      }
    },
    initialPageParam: 1,
    enabled: true, // Force enable

    getNextPageParam: (lastPage, allPages) => {
      const currentPage = allPages.length;
      // API trả về meta.totalPages, sử dụng trực tiếp
      const totalPages = lastPage.meta?.totalPages || 0;

      console.log('[useThreadsInfinite] getNextPageParam:', {
        currentPage,
        totalPages,
        hasNext: currentPage < totalPages,
        lastPageMeta: lastPage.meta,
        isSearching: debouncedSearchTerm && debouncedSearchTerm.length >= 2
      });

      // Khi search, disable infinite scroll vì search chỉ làm việc với single page
      // Search results được filter từ current page, không cần load thêm pages
      if (debouncedSearchTerm && debouncedSearchTerm.length >= 2) {
        console.log('[useThreadsInfinite] Search mode - disabling infinite scroll:', {
          searchTerm: debouncedSearchTerm,
          currentPage,
          totalPages,
          lastPageItems: lastPage.items.length
        });
        return undefined; // Disable infinite scroll cho search
      }

      // Trả về page tiếp theo nếu còn data
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },

    staleTime: isSearching ? 0 : 5 * 60 * 1000, // Fresh data cho search, cache 5 phút cho browse
    gcTime: isSearching ? 0 : 10 * 60 * 1000, // No cache cho search, cache 10 phút cho browse
    refetchOnMount: isSearching ? true : false, // Chỉ refetch khi search
    refetchOnWindowFocus: false, // Tắt để tránh unnecessary calls
    refetchOnReconnect: true,
    ...queryOptions,
  });

  // Effect để reset query khi search/sort parameters thay đổi
  useEffect(() => {
    const prevQuery = prevQueryRef.current;
    const currentQuery = { pageSize, sortBy, sortDirection, searchTerm: debouncedSearchTerm };

    // Kiểm tra xem có parameter nào thay đổi không
    const hasQueryChanged =
      prevQuery.pageSize !== currentQuery.pageSize ||
      prevQuery.sortBy !== currentQuery.sortBy ||
      prevQuery.sortDirection !== currentQuery.sortDirection ||
      prevQuery.searchTerm !== currentQuery.searchTerm;

    if (hasQueryChanged) {
      console.log('[useThreadsInfinite] Query parameters changed, resetting...', {
        prev: prevQuery,
        current: currentQuery
      });

      // Reset infinite query về page đầu tiên bằng cách xóa cache
      queryClient.removeQueries({ queryKey });

      // Cập nhật ref
      prevQueryRef.current = currentQuery;
    }
  }, [pageSize, sortBy, sortDirection, debouncedSearchTerm, queryClient, queryKey]);

  // Flatten tất cả pages thành một array
  const infiniteData = query.data as InfiniteData<GetThreadsResponse, number> | undefined;
  const pages = infiniteData?.pages || [];
  const allThreads = pages.flatMap((page: GetThreadsResponse) => page.items);

  // Debug logging cho search results
  if (isSearching) {
    console.log('[useThreadsInfinite] Search debug:', {
      searchTerm: debouncedSearchTerm,
      queryData: query.data,
      infiniteData,
      pagesCount: pages.length,
      pagesData: pages.map((page, index) => ({
        pageIndex: index,
        itemsCount: page.items?.length || 0,
        items: page.items?.slice(0, 3).map(item => ({ id: item.threadId, name: item.name })) || [],
        meta: page.meta
      })),
      allThreadsCount: allThreads.length,
      allThreadsSample: allThreads.slice(0, 3).map(item => ({ id: item.threadId, name: item.name }))
    });
  }

  // Tính toán metadata từ API response
  const totalItems = pages[0]?.meta?.totalItems || 0;
  const hasNextPage = query.hasNextPage;
  const isFetchingNextPage = query.isFetchingNextPage;



  console.log('[useThreadsInfinite] Hook state:', {
    totalItems,
    hasNextPage,
    isFetchingNextPage,
    threadsCount: allThreads.length,
    pagesCount: pages.length,
    firstPageMeta: pages[0]?.meta,
    isSearching,
    searchTerm: debouncedSearchTerm,
    isLoading: query.isLoading,
    isError: query.isError,
    errorMessage: query.error?.message
  });

  // Function để reset query manually
  const resetQuery = () => {
    console.log('[useThreadsInfinite] Manual reset triggered');
    queryClient.removeQueries({ queryKey });
    query.refetch();
  };

  // Force initial fetch if not loading and no data
  useEffect(() => {
    console.log('[useThreadsInfinite] Initial effect check:', {
      isLoading: query.isLoading,
      isFetching: query.isFetching,
      hasData: !!query.data,
      dataPages: infiniteData?.pages?.length || 0,
      isError: query.isError,
      error: query.error?.message
    });

    // If not loading, not fetching, and no data, force refetch
    if (!query.isLoading && !query.isFetching && !query.data && !query.isError) {
      console.log('[useThreadsInfinite] Force triggering initial fetch');
      setTimeout(() => {
        query.refetch();
      }, 100);
    }
  }, [query, infiniteData?.pages?.length]);

  return {
    // Data
    threads: allThreads,
    totalItems,

    // Pagination
    hasNextPage,
    fetchNextPage: query.fetchNextPage,
    isFetchingNextPage,

    // Query states
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    resetQuery,

    // Search state
    isSearching,

    // Raw query object
    query,
  };
};

/**
 * Hook để search threads với infinite scroll
 */
export const useThreadsSearchInfinite = (
  searchTerm: string,
  options: Omit<UseThreadsInfiniteOptions, 'searchTerm'> = {}
) => {
  return useThreadsInfinite({
    ...options,
    searchTerm,
    queryOptions: {
      enabled: searchTerm.length >= 2, // Chỉ search khi có ít nhất 2 ký tự
      ...options.queryOptions,
    }
  });
};
