/**
 * Threads module routes
 */

import { RouteObject } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import { Loading } from '@/shared';
import MainLayout from '@/shared/layouts/MainLayout';

// Lazy load components
const ThreadsPage = lazy(() => import('../pages/ThreadsPage').then(module => ({
  default: module.ThreadsPage
})));

const ThreadDetailPage = lazy(() => import('../pages/ThreadDetailPage').then(module => ({
  default: module.ThreadDetailPage
})));

/**
 * Routes cho Threads module
 */
export const threadsRoutes: RouteObject[] = [
  // Trang quản lý threads chính
  {
    path: '/threads',
    element: (
      <MainLayout title="Quản lý cuộc hội thoại">
        <Suspense fallback={<Loading />}>
          <ThreadsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chi tiết thread
  {
    path: '/threads/:threadId',
    element: (
      <MainLayout title="Chi tiết cuộc hội thoại">
        <Suspense fallback={<Loading />}>
          <ThreadDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default threadsRoutes;
