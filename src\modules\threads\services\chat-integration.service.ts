/**
 * Chat Integration Service
 * Service tích hợp threads với chat panel
 */

import { ThreadItem } from '@/shared/types/chat-streaming.types';
import { UseChatStreamReturn } from '@/shared/hooks/common/useChatStream';

/**
 * Interface cho chat integration events
 */
export interface ChatIntegrationEvents {
  onThreadLoaded?: (threadId: string) => void;
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;
  onThreadDeleted?: (threadId: string) => void;
}

/**
 * Interface cho chat panel integration
 */
interface ChatPanelIntegration {
  chatStream?: UseChatStreamReturn;
  focusChatInput?: () => void;
}

/**
 * Service class cho chat integration
 */
export class ChatIntegrationService {
  private static instance: ChatIntegrationService;
  private currentThreadId: string | null = null;
  private events: ChatIntegrationEvents = {};
  private chatPanelIntegration: ChatPanelIntegration = {};

  private constructor() {}

  /**
   * Singleton instance
   */
  static getInstance(): ChatIntegrationService {
    if (!ChatIntegrationService.instance) {
      ChatIntegrationService.instance = new ChatIntegrationService();
    }
    return ChatIntegrationService.instance;
  }

  /**
   * Đăng ký event listeners
   */
  setEventListeners(events: ChatIntegrationEvents) {
    this.events = { ...this.events, ...events };
  }

  /**
   * Set chat panel integration
   */
  setChatPanelIntegration(integration: ChatPanelIntegration): void {
    this.chatPanelIntegration = { ...this.chatPanelIntegration, ...integration };
    console.log('[ChatIntegrationService] Chat panel integration set:', integration);
  }



  /**
   * Check if chat panel integration is available
   */
  isChatPanelIntegrationAvailable(): boolean {
    return !!(this.chatPanelIntegration.chatStream && this.chatPanelIntegration.focusChatInput);
  }

  /**
   * Get chat panel integration status
   */
  getChatPanelIntegrationStatus(): {
    hasChatStream: boolean;
    hasFocusFunction: boolean;
    isFullyIntegrated: boolean;
  } {
    return {
      hasChatStream: !!this.chatPanelIntegration.chatStream,
      hasFocusFunction: !!this.chatPanelIntegration.focusChatInput,
      isFullyIntegrated: this.isChatPanelIntegrationAvailable()
    };
  }

  /**
   * Lấy current thread ID
   */
  getCurrentThreadId(): string | null {
    return this.currentThreadId;
  }

  /**
   * Load thread vào chat panel
   */
  async loadThreadToChat(threadId: string): Promise<void> {
    try {
      console.log('[ChatIntegrationService] Loading thread to chat:', threadId);

      const previousThreadId = this.currentThreadId;
      this.currentThreadId = threadId;

      // Trigger event
      this.events.onThreadLoaded?.(threadId);

      if (previousThreadId && previousThreadId !== threadId) {
        this.events.onThreadSwitched?.(previousThreadId, threadId);
      }

      // Tích hợp với chat panel service
      await this.integrateWithChatPanel(threadId);

    } catch (error) {
      console.error('[ChatIntegrationService] Failed to load thread:', error);
      throw error;
    }
  }

  /**
   * Tích hợp với chat panel service
   */
  private async integrateWithChatPanel(threadId: string): Promise<void> {
    try {
      console.log('[ChatIntegrationService] Integrating with chat panel:', threadId);

      // 1. Load messages của thread thông qua chatStream
      if (this.chatPanelIntegration.chatStream) {
        console.log('[ChatIntegrationService] Switching chat stream to thread:', threadId);

        // Sử dụng switchToThread để load thread và messages
        await this.chatPanelIntegration.chatStream.switchToThread(threadId);

        console.log('[ChatIntegrationService] Successfully switched chat stream to thread:', threadId);
      } else {
        console.warn('[ChatIntegrationService] No chat stream available for integration');
      }

      // 2. Focus vào chat input
      if (this.chatPanelIntegration.focusChatInput) {
        console.log('[ChatIntegrationService] Focusing chat input');

        // Delay nhỏ để đảm bảo thread đã được load
        setTimeout(() => {
          this.chatPanelIntegration.focusChatInput?.();
        }, 300);
      } else {
        console.warn('[ChatIntegrationService] No focus function available for chat input');
      }

      console.log('[ChatIntegrationService] Chat panel integration completed');

    } catch (error) {
      console.error('[ChatIntegrationService] Failed to integrate with chat panel:', error);
      throw error;
    }
  }

  /**
   * Switch sang thread khác
   */
  async switchToThread(threadId: string): Promise<void> {
    if (this.currentThreadId === threadId) {
      console.log('[ChatIntegrationService] Already on thread:', threadId);
      return;
    }

    await this.loadThreadToChat(threadId);
  }

  /**
   * Xử lý khi thread bị xóa
   */
  async handleThreadDeleted(deletedThreadId: string, availableThreads: ThreadItem[]): Promise<string | null> {
    try {
      console.log('[ChatIntegrationService] Handling deleted thread:', deletedThreadId);
      
      // Trigger event
      this.events.onThreadDeleted?.(deletedThreadId);

      // Nếu thread bị xóa không phải current thread, không cần làm gì
      if (this.currentThreadId !== deletedThreadId) {
        return this.currentThreadId;
      }

      // Tìm thread tiếp theo để switch
      const nextThread = this.findNextThread(deletedThreadId, availableThreads);
      
      if (nextThread) {
        await this.loadThreadToChat(nextThread.threadId);
        return nextThread.threadId;
      } else {
        // Không có thread nào khác, clear current thread
        this.currentThreadId = null;
        return null;
      }
      
    } catch (error) {
      console.error('[ChatIntegrationService] Failed to handle deleted thread:', error);
      throw error;
    }
  }

  /**
   * Tìm thread tiếp theo để switch sau khi xóa
   */
  private findNextThread(deletedThreadId: string, availableThreads: ThreadItem[]): ThreadItem | null {
    // Filter ra thread bị xóa
    const remainingThreads = availableThreads.filter(t => t.threadId !== deletedThreadId);
    
    if (remainingThreads.length === 0) {
      return null;
    }

    // Ưu tiên thread được update gần nhất
    const sortedThreads = remainingThreads.sort((a, b) => b.updatedAt - a.updatedAt);
    return sortedThreads[0] || null;
  }

  /**
   * Clear current thread
   */
  clearCurrentThread(): void {
    this.currentThreadId = null;
  }

  /**
   * Check xem thread có đang active không
   */
  isThreadActive(threadId: string): boolean {
    return this.currentThreadId === threadId;
  }

  /**
   * Get thread status
   */
  getThreadStatus(threadId: string): 'active' | 'inactive' {
    return this.isThreadActive(threadId) ? 'active' : 'inactive';
  }
}

/**
 * Export singleton instance
 */
export const chatIntegrationService = ChatIntegrationService.getInstance();
