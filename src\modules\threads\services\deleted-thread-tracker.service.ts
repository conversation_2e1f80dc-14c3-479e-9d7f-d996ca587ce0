/**
 * Deleted Thread Tracker Service
 * Service để track các thread đã bị xóa và prevent API calls với deleted threadId
 */

class DeletedThreadTrackerService {
  private static instance: DeletedThreadTrackerService;
  private deletedThreadIds: Set<string> = new Set();
  private deletionTimestamps: Map<string, number> = new Map();
  
  // Cleanup deleted threads sau 5 phút để tránh memory leak
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private cleanupTimer?: NodeJS.Timeout;

  private constructor() {
    this.startCleanupTimer();
  }

  static getInstance(): DeletedThreadTrackerService {
    if (!DeletedThreadTrackerService.instance) {
      DeletedThreadTrackerService.instance = new DeletedThreadTrackerService();
    }
    return DeletedThreadTrackerService.instance;
  }

  /**
   * Mark thread as deleted
   */
  markAsDeleted(threadId: string): void {
    console.log('[DeletedThreadTracker] Marking thread as deleted:', threadId);
    this.deletedThreadIds.add(threadId);
    this.deletionTimestamps.set(threadId, Date.now());
  }

  /**
   * Check if thread is deleted
   */
  isDeleted(threadId: string): boolean {
    return this.deletedThreadIds.has(threadId);
  }

  /**
   * Remove thread from deleted list (nếu thread được restore)
   */
  markAsRestored(threadId: string): void {
    console.log('[DeletedThreadTracker] Marking thread as restored:', threadId);
    this.deletedThreadIds.delete(threadId);
    this.deletionTimestamps.delete(threadId);
  }

  /**
   * Get all deleted thread IDs
   */
  getDeletedThreadIds(): string[] {
    return Array.from(this.deletedThreadIds);
  }

  /**
   * Clear all deleted threads
   */
  clearAll(): void {
    console.log('[DeletedThreadTracker] Clearing all deleted threads');
    this.deletedThreadIds.clear();
    this.deletionTimestamps.clear();
  }

  /**
   * Start cleanup timer để remove old deleted threads
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldDeletedThreads();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Cleanup old deleted threads (older than 5 minutes)
   */
  private cleanupOldDeletedThreads(): void {
    const now = Date.now();
    const threadsToRemove: string[] = [];

    this.deletionTimestamps.forEach((timestamp, threadId) => {
      if (now - timestamp > this.CLEANUP_INTERVAL) {
        threadsToRemove.push(threadId);
      }
    });

    if (threadsToRemove.length > 0) {
      console.log('[DeletedThreadTracker] Cleaning up old deleted threads:', threadsToRemove);
      threadsToRemove.forEach(threadId => {
        this.deletedThreadIds.delete(threadId);
        this.deletionTimestamps.delete(threadId);
      });
    }
  }

  /**
   * Stop cleanup timer
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.clearAll();
  }
}

/**
 * Export singleton instance
 */
export const deletedThreadTracker = DeletedThreadTrackerService.getInstance();

/**
 * Helper function để check nếu thread đã bị xóa
 */
export const isThreadDeleted = (threadId: string): boolean => {
  return deletedThreadTracker.isDeleted(threadId);
};

/**
 * Helper function để mark thread as deleted
 */
export const markThreadAsDeleted = (threadId: string): void => {
  deletedThreadTracker.markAsDeleted(threadId);
};

/**
 * Helper function để mark thread as restored
 */
export const markThreadAsRestored = (threadId: string): void => {
  deletedThreadTracker.markAsRestored(threadId);
};
