/**
 * Thread Error Handler Service
 * Service để handle errors và implement retry logic cho thread operations
 */

import { useQueryClient, QueryClient } from '@tanstack/react-query';
import { THREADS_QUERY_KEYS } from '../constants';

export interface ThreadError {
  type: 'NETWORK' | 'VALIDATION' | 'PERMISSION' | 'NOT_FOUND' | 'UNKNOWN';
  message: string;
  originalError?: Error;
  threadId?: string;
  operation?: 'CREATE' | 'UPDATE' | 'DELETE' | 'SWITCH' | 'LOAD';
}

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff: boolean;
}

/**
 * Thread Error Handler Service Class
 */
export class ThreadErrorHandlerService {
  private queryClient: QueryClient;
  private defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true
  };

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }

  /**
   * Parse error thành ThreadError
   */
  parseError(error: unknown, operation?: ThreadError['operation'], threadId?: string): ThreadError {
    if (error instanceof Error) {
      // Network errors
      if (error.message.includes('fetch') || error.message.includes('network')) {
        return {
          type: 'NETWORK',
          message: 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.',
          originalError: error,
          threadId,
          operation
        };
      }

      // Validation errors
      if (error.message.includes('validation') || error.message.includes('invalid')) {
        return {
          type: 'VALIDATION',
          message: 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.',
          originalError: error,
          threadId,
          operation
        };
      }

      // Permission errors
      if (error.message.includes('permission') || error.message.includes('unauthorized')) {
        return {
          type: 'PERMISSION',
          message: 'Bạn không có quyền thực hiện thao tác này.',
          originalError: error,
          threadId,
          operation
        };
      }

      // Not found errors
      if (error.message.includes('not found') || error.message.includes('404')) {
        return {
          type: 'NOT_FOUND',
          message: 'Thread không tồn tại hoặc đã bị xóa.',
          originalError: error,
          threadId,
          operation
        };
      }
    }

    // Unknown errors
    return {
      type: 'UNKNOWN',
      message: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại.',
      originalError: error instanceof Error ? error : new Error(String(error)),
      threadId,
      operation
    };
  }

  /**
   * Handle error với rollback logic
   */
  async handleError(
    error: ThreadError,
    rollbackFn?: () => Promise<void> | void
  ): Promise<void> {
    console.error('[ThreadErrorHandler] Handling error:', error);

    // Thực hiện rollback nếu có
    if (rollbackFn) {
      try {
        await rollbackFn();
        console.log('[ThreadErrorHandler] Rollback completed successfully');
      } catch (rollbackError) {
        console.error('[ThreadErrorHandler] Rollback failed:', rollbackError);
      }
    }

    // Invalidate cache nếu cần
    if (error.operation && ['CREATE', 'UPDATE', 'DELETE'].includes(error.operation)) {
      this.queryClient.invalidateQueries({ queryKey: THREADS_QUERY_KEYS.ALL });
    }

    // Log error cho monitoring
    this.logError(error);
  }

  /**
   * Retry operation với exponential backoff
   */
  async retryOperation<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const retryConfig = { ...this.defaultRetryConfig, ...config };
    let lastError: Error;

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === retryConfig.maxRetries) {
          break;
        }

        // Calculate delay với exponential backoff
        const delay = retryConfig.exponentialBackoff
          ? retryConfig.retryDelay * Math.pow(2, attempt)
          : retryConfig.retryDelay;

        console.log(`[ThreadErrorHandler] Retry attempt ${attempt + 1}/${retryConfig.maxRetries} after ${delay}ms`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Create optimistic update với rollback
   */
  createOptimisticUpdate<T>(
    queryKey: unknown[],
    optimisticData: T,
    operation: () => Promise<T>
  ) {
    return {
      execute: async (): Promise<T> => {
        // Cancel any outgoing refetches
        await this.queryClient.cancelQueries({ queryKey });

        // Snapshot the previous value
        const previousData = this.queryClient.getQueryData(queryKey);

        // Optimistically update
        this.queryClient.setQueryData(queryKey, optimisticData);

        try {
          // Perform the actual operation
          const result = await operation();
          
          // Update with real data
          this.queryClient.setQueryData(queryKey, result);
          
          return result;
        } catch (error) {
          // Rollback on error
          this.queryClient.setQueryData(queryKey, previousData);
          throw error;
        }
      },
      rollback: () => {
        const previousData = this.queryClient.getQueryData(queryKey);
        this.queryClient.setQueryData(queryKey, previousData);
      }
    };
  }

  /**
   * Log error cho monitoring
   */
  private logError(error: ThreadError): void {
    // Có thể integrate với external logging service
    console.error('[ThreadErrorHandler] Error logged:', {
      type: error.type,
      message: error.message,
      threadId: error.threadId,
      operation: error.operation,
      timestamp: new Date().toISOString(),
      stack: error.originalError?.stack
    });
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error: ThreadError): boolean {
    return error.type === 'NETWORK' || error.type === 'UNKNOWN';
  }

  /**
   * Get user-friendly error message
   */
  getUserFriendlyMessage(error: ThreadError): string {
    switch (error.type) {
      case 'NETWORK':
        return 'Lỗi kết nối. Vui lòng kiểm tra internet và thử lại.';
      case 'VALIDATION':
        return 'Thông tin không hợp lệ. Vui lòng kiểm tra lại.';
      case 'PERMISSION':
        return 'Bạn không có quyền thực hiện thao tác này.';
      case 'NOT_FOUND':
        return 'Thread không tồn tại. Có thể đã bị xóa.';
      default:
        return 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
    }
  }
}

/**
 * Hook để sử dụng ThreadErrorHandlerService
 */
export const useThreadErrorHandler = () => {
  const queryClient = useQueryClient();
  
  const errorHandler = new ThreadErrorHandlerService(queryClient);
  
  return errorHandler;
};
