/**
 * Thread Performance Service
 * Service để optimize performance cho thread operations
 */

import { useCallback, useMemo, useRef } from 'react';
import { useQueryClient, QueryClient } from '@tanstack/react-query';
import { THREADS_QUERY_KEYS } from '../constants';

/**
 * Debounce utility
 */
export function useDebounce<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }) as T,
    [callback, delay]
  );
}

/**
 * Throttle utility
 */
export function useThrottle<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number
): T {
  const lastCallRef = useRef<number>(0);

  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now;
        callback(...args);
      }
    }) as T,
    [callback, delay]
  );
}

/**
 * Memoized selector cho thread data
 */
export const useThreadSelector = <T>(
  threads: unknown[],
  selector: (threads: unknown[]) => T,
  deps: unknown[] = []
): T => {
  return useMemo(() => selector(threads), [threads, selector, deps]);
};

/**
 * Performance monitoring
 */
export class ThreadPerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();

  /**
   * Start timing an operation
   */
  startTiming(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.recordMetric(operation, duration);
    };
  }

  /**
   * Record a metric
   */
  recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const metrics = this.metrics.get(operation)!;
    metrics.push(duration);
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  /**
   * Get average duration for an operation
   */
  getAverageDuration(operation: string): number {
    const metrics = this.metrics.get(operation);
    if (!metrics || metrics.length === 0) return 0;
    
    return metrics.reduce((sum, duration) => sum + duration, 0) / metrics.length;
  }

  /**
   * Get performance report
   */
  getReport(): Record<string, { avg: number; count: number; latest: number }> {
    const report: Record<string, { avg: number; count: number; latest: number }> = {};
    
    for (const [operation, metrics] of this.metrics.entries()) {
      if (metrics.length > 0) {
        report[operation] = {
          avg: this.getAverageDuration(operation),
          count: metrics.length,
          latest: metrics[metrics.length - 1]
        };
      }
    }
    
    return report;
  }

  /**
   * Clear metrics
   */
  clear(): void {
    this.metrics.clear();
  }
}

/**
 * Thread Performance Service
 */
export class ThreadPerformanceService {
  private queryClient: QueryClient;
  private monitor: ThreadPerformanceMonitor;
  private cacheConfig = {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false
  };

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
    this.monitor = new ThreadPerformanceMonitor();
  }

  /**
   * Optimize React Query cache invalidation
   */
  optimizedInvalidation = {
    /**
     * Invalidate specific thread detail only
     */
    invalidateThreadDetail: (threadId: string) => {
      this.queryClient.invalidateQueries({ 
        queryKey: THREADS_QUERY_KEYS.DETAIL(threadId),
        exact: true 
      });
    },

    /**
     * Invalidate threads list with smart strategy
     */
    invalidateThreadsList: () => {
      // Only invalidate list queries, not detail queries
      this.queryClient.invalidateQueries({
        queryKey: THREADS_QUERY_KEYS.ALL,
        predicate: (query: { queryKey: unknown[] }) => {
          return query.queryKey.some(key => typeof key === 'string' && (key.includes('list') || key.includes('paginated')));
        }
      });
    },

    /**
     * Selective invalidation based on operation
     */
    invalidateByOperation: (operation: 'CREATE' | 'UPDATE' | 'DELETE', threadId?: string) => {
      const endTiming = this.monitor.startTiming(`invalidation_${operation.toLowerCase()}`);
      
      switch (operation) {
        case 'CREATE':
          // Only invalidate list queries
          this.optimizedInvalidation.invalidateThreadsList();
          break;
          
        case 'UPDATE':
          // Invalidate specific thread and lists
          if (threadId) {
            this.optimizedInvalidation.invalidateThreadDetail(threadId);
          }
          this.optimizedInvalidation.invalidateThreadsList();
          break;
          
        case 'DELETE':
          // Remove specific thread and invalidate lists
          if (threadId) {
            this.queryClient.removeQueries({ 
              queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) 
            });
          }
          this.optimizedInvalidation.invalidateThreadsList();
          break;
      }
      
      endTiming();
    }
  };

  /**
   * Optimized cache updates
   */
  optimizedCacheUpdate = {
    /**
     * Update thread in cache without full refetch
     */
    updateThreadInCache: (threadId: string, updates: Partial<Record<string, unknown>>) => {
      const endTiming = this.monitor.startTiming('cache_update_thread');
      
      // Update thread detail cache
      this.queryClient.setQueryData(
        THREADS_QUERY_KEYS.DETAIL(threadId),
        (oldData: unknown) => oldData && typeof oldData === 'object' ? { ...oldData as Record<string, unknown>, ...updates } : oldData
      );

      // Update thread in lists cache
      const listQueries = this.queryClient.getQueriesData({ 
        queryKey: THREADS_QUERY_KEYS.ALL 
      });

      listQueries.forEach(([queryKey, data]) => {
        if (data && typeof data === 'object' && 'items' in data) {
          const threadsData = data as Record<string, unknown>;
          if (Array.isArray(threadsData.items)) {
            const updatedItems = (threadsData.items as Array<Record<string, unknown>>).map((thread: Record<string, unknown>) =>
              thread.threadId === threadId ? { ...thread, ...updates } : thread
            );
            this.queryClient.setQueryData(queryKey, {
              ...threadsData,
              items: updatedItems
            });
          }
        }
      });
      
      endTiming();
    },

    /**
     * Add new thread to cache
     */
    addThreadToCache: (newThread: Record<string, unknown>) => {
      const endTiming = this.monitor.startTiming('cache_add_thread');
      
      // Add to thread detail cache
      this.queryClient.setQueryData(
        THREADS_QUERY_KEYS.DETAIL(newThread.threadId),
        newThread
      );

      // Add to lists cache (prepend to maintain sort order)
      const listQueries = this.queryClient.getQueriesData({ 
        queryKey: THREADS_QUERY_KEYS.ALL 
      });

      listQueries.forEach(([queryKey, data]) => {
        if (data && typeof data === 'object' && 'items' in data) {
          const threadsData = data as Record<string, unknown>;
          if (Array.isArray(threadsData.items)) {
            this.queryClient.setQueryData(queryKey, {
              ...threadsData,
              items: [newThread, ...threadsData.items],
              meta: {
                ...(threadsData.meta as Record<string, unknown>),
                totalItems: ((threadsData.meta as Record<string, unknown>)?.totalItems as number || 0) + 1
              }
            });
          }
        }
      });
      
      endTiming();
    },

    /**
     * Remove thread from cache
     */
    removeThreadFromCache: (threadId: string) => {
      const endTiming = this.monitor.startTiming('cache_remove_thread');
      
      // Remove thread detail cache
      this.queryClient.removeQueries({ 
        queryKey: THREADS_QUERY_KEYS.DETAIL(threadId) 
      });

      // Remove from lists cache
      const listQueries = this.queryClient.getQueriesData({ 
        queryKey: THREADS_QUERY_KEYS.ALL 
      });

      listQueries.forEach(([queryKey, data]) => {
        if (data && typeof data === 'object' && 'items' in data) {
          const threadsData = data as Record<string, unknown>;
          if (Array.isArray(threadsData.items)) {
            const filteredItems = (threadsData.items as Array<Record<string, unknown>>).filter(
              (thread: Record<string, unknown>) => thread.threadId !== threadId
            );
            this.queryClient.setQueryData(queryKey, {
              ...threadsData,
              items: filteredItems,
              meta: {
                ...(threadsData.meta as Record<string, unknown>),
                totalItems: Math.max(((threadsData.meta as Record<string, unknown>)?.totalItems as number || 0) - 1, 0)
              }
            });
          }
        }
      });
      
      endTiming();
    }
  };

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    return this.monitor.getReport();
  }

  /**
   * Clear performance metrics
   */
  clearMetrics() {
    this.monitor.clear();
  }

  /**
   * Get cache config
   */
  getCacheConfig() {
    return this.cacheConfig;
  }
}

/**
 * Hook để sử dụng ThreadPerformanceService
 */
export const useThreadPerformance = () => {
  const queryClient = useQueryClient();
  
  const performanceService = useMemo(
    () => new ThreadPerformanceService(queryClient),
    [queryClient]
  );
  
  return performanceService;
};
