/**
 * Threads Service - Business logic layer
 * Xử lý logic nghiệp vụ cho threads management
 */

import {
  getThreads as getThreadsApi,
  getThreadDetail as getThreadDetail<PERSON>pi,
  updateThread as updateThread<PERSON><PERSON>,
  deleteThread as deleteThread<PERSON><PERSON>
} from '../api';
import {
  GetThreadsQuery,
  GetThreadsResponse,
  ThreadDetailResponse,
  UpdateThreadRequest
} from '@/shared/types/chat-streaming.types';

/**
 * Service class cho Threads operations
 */
export class ThreadsService {
  /**
   * Lấy danh sách threads với default parameters
   */
  static async getThreads(query?: GetThreadsQuery): Promise<GetThreadsResponse> {
    const defaultQuery: GetThreadsQuery = {
      page: 1,
      limit: 20,
      sortBy: 'updatedAt',
      sortDirection: 'DESC',
      ...query
    };

    console.log('[ThreadsService] getThreads called with query:', defaultQuery);

    const response = await getThreadsApi(defaultQuery);

    console.log('[ThreadsService] API response:', {
      result: response.result,
      hasItems: response.result?.items?.length || 0,
      hasMeta: !!response.result?.meta,
      meta: response.result?.meta
    });

    return response.result;
  }

  /**
   * Lấy chi tiết thread
   */
  static async getThreadDetail(threadId: string): Promise<ThreadDetailResponse> {
    const response = await getThreadDetailApi(threadId);
    return response.result;
  }

  /**
   * Cập nhật tên thread với validation
   */
  static async updateThread(threadId: string, data: UpdateThreadRequest): Promise<ThreadDetailResponse> {
    // Validate tên thread
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Tên thread không được để trống');
    }

    if (data.name.trim().length > 100) {
      throw new Error('Tên thread không được vượt quá 100 ký tự');
    }

    const requestData: UpdateThreadRequest = {
      name: data.name.trim()
    };

    const response = await updateThreadApi(threadId, requestData);
    return response.result;
  }

  /**
   * Xóa thread với confirmation
   */
  static async deleteThread(threadId: string): Promise<void> {
    await deleteThreadApi(threadId);
  }

  /**
   * Tìm kiếm threads theo tên với single page strategy (compatible với infinite scroll)
   */
  static async searchThreads(searchTerm: string, query?: GetThreadsQuery): Promise<GetThreadsResponse> {
    console.log('[ThreadsService] searchThreads called with:', { searchTerm, query });

    // Nếu không có search term, trả về kết quả bình thường
    if (!searchTerm || searchTerm.trim().length === 0) {
      return this.getThreads(query);
    }

    const searchTermLower = searchTerm.toLowerCase().trim();

    // Sử dụng single page strategy để tương thích với infinite scroll
    // Lấy nhiều items hơn để có kết quả search tốt hơn
    const searchQuery: GetThreadsQuery = {
      ...query,
      limit: Math.min(query?.limit || 100, 100), // Tối đa 100 items per page
      page: query?.page || 1
    };

    console.log('[ThreadsService] Making single API call with query:', searchQuery);

    try {
      const response = await this.getThreads(searchQuery);

      console.log('[ThreadsService] API response:', {
        itemsCount: response.items.length,
        totalPages: response.meta?.totalPages,
        currentPage: response.meta?.currentPage,
        searchTerm: searchTermLower
      });

      // Filter items dựa trên search term
      const filteredItems = response.items.filter(thread => {
        const threadName = thread.name || '';
        const threadNameLower = threadName.toLowerCase();
        const matches = threadNameLower.includes(searchTermLower);

        // Debug individual thread matching
        if (response.items.length <= 10) { // Only log for small datasets
          console.log('[ThreadsService] Thread match check:', {
            threadId: thread.threadId,
            threadName,
            threadNameLower,
            searchTermLower,
            matches
          });
        }

        return matches;
      });

      console.log('[ThreadsService] Search results:', {
        originalCount: response.items.length,
        filteredCount: filteredItems.length,
        searchTerm: searchTermLower,
        sampleResults: filteredItems.slice(0, 3).map(item => item.name),
        originalItems: response.items.slice(0, 3).map(item => ({ id: item.threadId, name: item.name })),
        filteredItems: filteredItems.slice(0, 3).map(item => ({ id: item.threadId, name: item.name })),
        responseStructure: {
          hasItems: !!response.items,
          itemsType: Array.isArray(response.items) ? 'array' : typeof response.items,
          firstItemStructure: response.items[0] ? Object.keys(response.items[0]) : 'no items'
        }
      });

      // Tạo meta cho kết quả search - giữ nguyên pagination structure
      const limit = searchQuery.limit || 20;
      const page = searchQuery.page || 1;

      const searchMeta = {
        totalItems: filteredItems.length,
        itemCount: filteredItems.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(filteredItems.length / limit),
        currentPage: page,
        hasItems: filteredItems.length > 0
      };

      return {
        items: filteredItems,
        meta: searchMeta,
        total: filteredItems.length
      };
    } catch (error) {
      console.error('[ThreadsService] Search API call failed:', error);
      throw error;
    }
  }
}
