/**
 * Thread Integration Validator
 * Utility để validate thread integration flow và detect issues
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  performance: {
    operationTime: number;
    cacheHits: number;
    cacheMisses: number;
  };
}

export interface ThreadIntegrationState {
  threadsPageThreadId: string | null;
  chatPanelThreadId: string | null;
  threadsPageThreadName: string | null;
  chatPanelThreadName: string | null;
  isThreadSwitching: boolean;
  lastOperation: string | null;
  operationTimestamp: number;
}

/**
 * Thread Integration Validator Class
 */
export class ThreadIntegrationValidator {
  private state: ThreadIntegrationState = {
    threadsPageThreadId: null,
    chatPanelThreadId: null,
    threadsPageThreadName: null,
    chatPanelThreadName: null,
    isThreadSwitching: false,
    lastOperation: null,
    operationTimestamp: 0
  };

  private operationHistory: Array<{
    operation: string;
    timestamp: number;
    threadId?: string;
    threadName?: string;
    success: boolean;
    duration: number;
  }> = [];

  /**
   * Update state từ ThreadsPage
   */
  updateThreadsPageState(threadId: string | null, threadName: string | null): void {
    this.state.threadsPageThreadId = threadId;
    this.state.threadsPageThreadName = threadName;
    this.state.operationTimestamp = Date.now();
  }

  /**
   * Update state từ ChatPanel
   */
  updateChatPanelState(threadId: string | null, threadName: string | null): void {
    this.state.chatPanelThreadId = threadId;
    this.state.chatPanelThreadName = threadName;
    this.state.operationTimestamp = Date.now();
  }

  /**
   * Set thread switching state
   */
  setThreadSwitching(isThreadSwitching: boolean): void {
    this.state.isThreadSwitching = isThreadSwitching;
  }

  /**
   * Record operation
   */
  recordOperation(
    operation: string,
    success: boolean,
    duration: number,
    threadId?: string,
    threadName?: string
  ): void {
    this.operationHistory.push({
      operation,
      timestamp: Date.now(),
      threadId,
      threadName,
      success,
      duration
    });

    // Keep only last 50 operations
    if (this.operationHistory.length > 50) {
      this.operationHistory.shift();
    }

    this.state.lastOperation = operation;
    this.state.operationTimestamp = Date.now();
  }

  /**
   * Validate thread synchronization
   */
  validateSynchronization(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const startTime = performance.now();

    // Check thread ID synchronization
    if (this.state.threadsPageThreadId !== this.state.chatPanelThreadId) {
      errors.push(
        `Thread ID mismatch: ThreadsPage(${this.state.threadsPageThreadId}) vs ChatPanel(${this.state.chatPanelThreadId})`
      );
    }

    // Check thread name synchronization
    if (this.state.threadsPageThreadName !== this.state.chatPanelThreadName) {
      if (this.state.threadsPageThreadId === this.state.chatPanelThreadId) {
        errors.push(
          `Thread name mismatch for same thread: ThreadsPage(${this.state.threadsPageThreadName}) vs ChatPanel(${this.state.chatPanelThreadName})`
        );
      }
    }

    // Check for stuck switching state
    if (this.state.isThreadSwitching) {
      const timeSinceSwitching = Date.now() - this.state.operationTimestamp;
      if (timeSinceSwitching > 10000) { // 10 seconds
        errors.push(`Thread switching stuck for ${timeSinceSwitching}ms`);
      } else if (timeSinceSwitching > 5000) { // 5 seconds
        warnings.push(`Thread switching taking longer than expected: ${timeSinceSwitching}ms`);
      }
    }

    const endTime = performance.now();

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      performance: {
        operationTime: endTime - startTime,
        cacheHits: this.calculateCacheHits(),
        cacheMisses: this.calculateCacheMisses()
      }
    };
  }

  /**
   * Validate operation flow
   */
  validateOperationFlow(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const startTime = performance.now();

    // Check for failed operations
    const recentOperations = this.operationHistory.slice(-10);
    const failedOperations = recentOperations.filter(op => !op.success);
    
    if (failedOperations.length > 3) {
      errors.push(`Too many failed operations: ${failedOperations.length}/10`);
    } else if (failedOperations.length > 1) {
      warnings.push(`Some operations failed: ${failedOperations.length}/10`);
    }

    // Check operation performance
    const slowOperations = recentOperations.filter(op => op.duration > 5000);
    if (slowOperations.length > 0) {
      warnings.push(`Slow operations detected: ${slowOperations.map(op => `${op.operation}(${op.duration}ms)`).join(', ')}`);
    }

    // Check for operation patterns
    const operationTypes = recentOperations.map(op => op.operation);
    const duplicateOperations = this.findDuplicateOperations(operationTypes);
    if (duplicateOperations.length > 0) {
      warnings.push(`Duplicate operations detected: ${duplicateOperations.join(', ')}`);
    }

    const endTime = performance.now();

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      performance: {
        operationTime: endTime - startTime,
        cacheHits: this.calculateCacheHits(),
        cacheMisses: this.calculateCacheMisses()
      }
    };
  }

  /**
   * Comprehensive validation
   */
  validateIntegration(): ValidationResult {
    const syncResult = this.validateSynchronization();
    const flowResult = this.validateOperationFlow();

    return {
      isValid: syncResult.isValid && flowResult.isValid,
      errors: [...syncResult.errors, ...flowResult.errors],
      warnings: [...syncResult.warnings, ...flowResult.warnings],
      performance: {
        operationTime: syncResult.performance.operationTime + flowResult.performance.operationTime,
        cacheHits: syncResult.performance.cacheHits + flowResult.performance.cacheHits,
        cacheMisses: syncResult.performance.cacheMisses + flowResult.performance.cacheMisses
      }
    };
  }

  /**
   * Get current state
   */
  getCurrentState(): ThreadIntegrationState {
    return { ...this.state };
  }

  /**
   * Get operation history
   */
  getOperationHistory(): typeof this.operationHistory {
    return [...this.operationHistory];
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    const recentOperations = this.operationHistory.slice(-20);
    
    return {
      totalOperations: this.operationHistory.length,
      successRate: this.calculateSuccessRate(),
      averageDuration: this.calculateAverageDuration(),
      operationsByType: this.groupOperationsByType(),
      recentFailures: recentOperations.filter(op => !op.success),
      slowestOperations: recentOperations
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 5)
    };
  }

  /**
   * Clear history
   */
  clearHistory(): void {
    this.operationHistory = [];
  }

  /**
   * Reset state
   */
  resetState(): void {
    this.state = {
      threadsPageThreadId: null,
      chatPanelThreadId: null,
      threadsPageThreadName: null,
      chatPanelThreadName: null,
      isThreadSwitching: false,
      lastOperation: null,
      operationTimestamp: 0
    };
  }

  // Private helper methods
  private calculateCacheHits(): number {
    // Mock implementation - would integrate with actual cache metrics
    return Math.floor(Math.random() * 10);
  }

  private calculateCacheMisses(): number {
    // Mock implementation - would integrate with actual cache metrics
    return Math.floor(Math.random() * 3);
  }

  private calculateSuccessRate(): number {
    if (this.operationHistory.length === 0) return 100;
    
    const successfulOps = this.operationHistory.filter(op => op.success).length;
    return (successfulOps / this.operationHistory.length) * 100;
  }

  private calculateAverageDuration(): number {
    if (this.operationHistory.length === 0) return 0;
    
    const totalDuration = this.operationHistory.reduce((sum, op) => sum + op.duration, 0);
    return totalDuration / this.operationHistory.length;
  }

  private groupOperationsByType(): Record<string, number> {
    const groups: Record<string, number> = {};
    
    this.operationHistory.forEach(op => {
      groups[op.operation] = (groups[op.operation] || 0) + 1;
    });
    
    return groups;
  }

  private findDuplicateOperations(operations: string[]): string[] {
    const seen = new Set<string>();
    const duplicates = new Set<string>();
    
    operations.forEach(op => {
      if (seen.has(op)) {
        duplicates.add(op);
      }
      seen.add(op);
    });
    
    return Array.from(duplicates);
  }
}

/**
 * Global validator instance
 */
export const threadIntegrationValidator = new ThreadIntegrationValidator();
