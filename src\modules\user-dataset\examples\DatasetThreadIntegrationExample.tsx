/**
 * <PERSON><PERSON> dụ về cách sử dụng Dataset Thread Integration
 * Component này demo cách tích hợp Dataset ChatPanel với ThreadsPage
 */

import React, { useState } from 'react';
import { ChatLayout } from '../components';
import { ImportedConversation } from '../user-data-fine-tune/types/user-data-fine-tune.types';
// import { useTranslation } from 'react-i18next';

interface DatasetThreadIntegrationExampleProps {
  /**
   * Dataset ID để tạo unique thread identifier
   */
  datasetId?: string;

  /**
   * Có enable thread integration hay không
   */
  enableIntegration?: boolean;
}

/**
 * Component ví dụ về Dataset Thread Integration
 */
const DatasetThreadIntegrationExample: React.FC<DatasetThreadIntegrationExampleProps> = ({
  datasetId = 'example-dataset-123',
  enableIntegration = true
}) => {
  // const { t } = useTranslation();
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [integrationEnabled, setIntegrationEnabled] = useState(enableIntegration);

  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    console.log('[DatasetThreadIntegrationExample] Conversations updated:', {
      count: updatedConversations.length,
      conversations: updatedConversations.map(c => ({ id: c.id, title: c.title, messagesCount: c.messages.length }))
    });
    setConversations(updatedConversations);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header với controls */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Dataset Thread Integration Example
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Demo tích hợp Dataset ChatPanel với ThreadsPage
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Integration Toggle */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Thread Integration:
              </label>
              <button
                onClick={() => setIntegrationEnabled(!integrationEnabled)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  integrationEnabled 
                    ? 'bg-primary' 
                    : 'bg-gray-200 dark:bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    integrationEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={`text-sm font-medium ${
                integrationEnabled 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-gray-500 dark:text-gray-400'
              }`}>
                {integrationEnabled ? 'ON' : 'OFF'}
              </span>
            </div>

            {/* Dataset ID Display */}
            <div className="text-sm">
              <span className="text-gray-500 dark:text-gray-400">Dataset ID:</span>
              <span className="ml-1 font-mono text-gray-900 dark:text-white">
                {datasetId}
              </span>
            </div>

            {/* Conversations Count */}
            <div className="text-sm">
              <span className="text-gray-500 dark:text-gray-400">Conversations:</span>
              <span className="ml-1 font-semibold text-primary">
                {conversations.length}
              </span>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-start space-x-2">
            <div className="text-blue-500 dark:text-blue-400 mt-0.5">
              ℹ️
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300">
              <p className="font-medium mb-1">Cách test Thread Integration:</p>
              <ol className="list-decimal list-inside space-y-1 text-xs">
                <li>Mở Developer Tools và xem Console tab</li>
                <li>Đảm bảo ThreadsPage đang mở ở tab/window khác</li>
                <li>Bật Thread Integration (toggle ở trên)</li>
                <li>Tạo conversation mới hoặc chọn conversation trống</li>
                <li>Gửi message đầu tiên</li>
                <li>Kiểm tra Console logs để xem events được dispatch</li>
                <li>Kiểm tra ThreadsPage - sẽ tự động refresh và hiển thị thread mới</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Status Indicator */}
        <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-green-700 dark:text-green-300 font-medium">
              Integration Ready - Sẵn sàng đồng bộ với ThreadsPage
            </span>
          </div>
        </div>
      </div>

      {/* Main Content - ChatLayout */}
      <div className="flex-1 min-h-0">
        <ChatLayout
          onConversationsChange={handleConversationsChange}
          enableThreadIntegration={integrationEnabled}
          datasetId={datasetId}
        />
      </div>

      {/* Debug Info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="flex-shrink-0 p-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <details className="text-xs">
            <summary className="cursor-pointer text-gray-600 dark:text-gray-400 font-medium">
              Debug Info (Development Only)
            </summary>
            <div className="mt-2 space-y-1 text-gray-500 dark:text-gray-400">
              <div>Integration Enabled: {integrationEnabled ? 'Yes' : 'No'}</div>
              <div>Dataset ID: {datasetId}</div>
              <div>Conversations Count: {conversations.length}</div>
              <div>
                Thread ID Format: {datasetId ? `dataset-${datasetId}-{timestamp}` : 'dataset-conversation-{timestamp}'}
              </div>
              <div>
                Thread Name Format: Dataset: {'{conversation.title}'}
              </div>
            </div>
          </details>
        </div>
      )}
    </div>
  );
};

export default DatasetThreadIntegrationExample;
