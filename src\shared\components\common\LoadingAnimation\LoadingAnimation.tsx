import React from 'react';
import symbolImage from '@/shared/assets/images/symbol.png';
import './LoadingAnimation.css';

export interface LoadingAnimationProps {
  /**
   * <PERSON><PERSON>ch thước của loading animation
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Tốc độ xoay (giây)
   */
  speed?: number;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Hiển thị text loading
   */
  showText?: boolean;

  /**
   * Text hiển thị
   */
  text?: string;

  /**
   * Sử dụng màu chủ đạo thay vì logo (chỉ áp dụng cho light mode)
   */
  usePrimaryColor?: boolean;
}

/**
 * Component hiển thị loading animation với logo xoay hoặc spinner màu chủ đạo
 */
const LoadingAnimation: React.FC<LoadingAnimationProps> = ({
  size = 'md',
  speed = 2,
  className = '',
  showText = false,
  text = 'Đang xử lý...',
  usePrimaryColor = false
}) => {
  // Xác định kích thước
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  }[size];

  // Xác định kích thước text
  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  }[size];

  // Style cho animation
  const animationStyle = {
    animation: `spin ${speed}s linear infinite`,
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="flex flex-col items-center">
        {/* Loading logo */}
        <div
          className={`${sizeClasses} flex items-center justify-center`}
          style={animationStyle}
        >
          <img
            src={symbolImage}
            alt="Loading"
            className={`w-full h-full object-contain ${
              usePrimaryColor ? 'loading-logo-primary' : ''
            }`}
          />
        </div>

        {/* Text loading (tùy chọn) */}
        {showText && (
          <div className={`mt-2 ${textSizeClasses} text-gray-600 dark:text-gray-400 animate-pulse`}>
            {text}
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadingAnimation;
