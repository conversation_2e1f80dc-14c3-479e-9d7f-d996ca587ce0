import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { Input, CountrySelect } from '@/shared/components/common';
import { Country, findCountryByCode } from '@/shared/data/countries';

export interface PhoneInputWithCountryProps {
  /**
   * Giá trị số điện thoại
   */
  value?: string;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string, countryCode: string) => void;

  /**
   * Placeholder cho input số điện thoại
   */
  placeholder?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Name attribute
   */
  name?: string;

  /**
   * ID attribute
   */
  id?: string;

  /**
   * CSS class
   */
  className?: string;

  /**
   * Error message
   */
  error?: string;

  /**
   * Helper text
   */
  helperText?: string;

  /**
   * Size
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Full width
   */
  fullWidth?: boolean;

  /**
   * Quốc gia mặc định (country code)
   */
  defaultCountry?: string;

  /**
   * Auto complete
   */
  autoComplete?: string;
}

/**
 * Component PhoneInputWithCountry - Input số điện thoại với chọn quốc gia
 */
const PhoneInputWithCountry = forwardRef<HTMLInputElement, PhoneInputWithCountryProps>(
  (
    {
      value = '',
      onChange,
      placeholder = '',
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      defaultCountry = 'VN',
      autoComplete = 'off',
    },
    ref
  ) => {
    // State cho country selection
    const [selectedCountry, setSelectedCountry] = useState<string>(() => {
      // Lấy country từ localStorage hoặc default
      return localStorage.getItem('country') || defaultCountry;
    });

    const inputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    useImperativeHandle(ref, () => inputRef.current as HTMLInputElement);

    // Handler cho country change
    const handleCountryChange = (country: Country) => {
      setSelectedCountry(country.code);
      // Lưu country vào localStorage
      localStorage.setItem('country', country.code);

      // Gọi onChange với country code mới
      if (onChange) {
        const selectedCountryData = findCountryByCode(country.code);
        const countryCode = selectedCountryData?.dialCode || '+84';
        onChange(value, countryCode);
      }
    };

    // Handler cho phone input change
    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const phoneValue = e.target.value;
      
      if (onChange) {
        const selectedCountryData = findCountryByCode(selectedCountry);
        const countryCode = selectedCountryData?.dialCode || '+84';
        onChange(phoneValue, countryCode);
      }
    };

    // Cập nhật countryCode khi selectedCountry thay đổi
    useEffect(() => {
      if (onChange) {
        const selectedCountryData = findCountryByCode(selectedCountry);
        const countryCode = selectedCountryData?.dialCode || '+84';
        onChange(value, countryCode);
      }
    }, [selectedCountry, onChange, value]); // Thêm các dependencies cần thiết

    return (
      <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
        <div className="flex gap-2">
          <CountrySelect
            value={selectedCountry}
            onChange={handleCountryChange}
            compact={true}
            size={size}
            disabled={disabled}
          />
          <Input
            ref={inputRef}
            type="text"
            value={value}
            onChange={handlePhoneChange}
            placeholder={placeholder}
            disabled={disabled}
            name={name}
            id={id}
            size={size}
            fullWidth
            autoComplete={autoComplete}
            error={error}
          />
        </div>

        {/* Error message */}
        {error && (
          <p className="mt-1 text-sm text-destructive">{error}</p>
        )}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-muted-foreground">{helperText}</p>
        )}
      </div>
    );
  }
);

PhoneInputWithCountry.displayName = 'PhoneInputWithCountry';

export default PhoneInputWithCountry;
