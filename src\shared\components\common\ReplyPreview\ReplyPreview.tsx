import React from 'react';
import { ChatRole, getRoleDisplayName, getAvatarForRole } from '@/shared/utils/avatarMapping';

export interface ReplyMessage {
  id?: string;
  content: React.ReactNode;
  sender: ChatRole;
  timestamp: Date;
}

export interface ReplyPreviewProps {
  /**
   * Tin nhắn được reply
   */
  replyMessage: ReplyMessage;

  /**
   * Callback khi cancel reply
   */
  onCancel: () => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị preview của tin nhắn được reply
 */
const ReplyPreview: React.FC<ReplyPreviewProps> = ({
  replyMessage,
  onCancel,
  className = ''
}) => {
  const avatarConfig = getAvatarForRole(replyMessage.sender);
  const roleDisplayName = getRoleDisplayName(replyMessage.sender);

  // Truncate content nếu quá dài
  const truncateContent = (content: React.ReactNode): string => {
    if (typeof content === 'string') {
      return content.length > 100 ? content.substring(0, 100) + '...' : content;
    }
    return 'Message content';
  };

  return (
    <div className={`bg-gray-50 dark:bg-gray-800 border-l-4 border-primary p-3 rounded-lg ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start flex-1 min-w-0">
          {/* Content - Bỏ avatar theo yêu cầu */}
          <div className="flex-1 min-w-0">
            {/* Role name */}
            <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
              Replying to {roleDisplayName}
            </div>

            {/* Message content */}
            <div className="text-sm text-gray-700 dark:text-gray-300 opacity-75 truncate">
              {truncateContent(replyMessage.content)}
            </div>
          </div>
        </div>

        {/* Cancel button */}
        <button
          onClick={onCancel}
          className="flex-shrink-0 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 
            text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200
            transition-colors duration-200"
          title="Cancel reply"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default ReplyPreview;
