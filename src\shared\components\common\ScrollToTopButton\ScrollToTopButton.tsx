/**
 * ScrollToTopButton Component
 * N<PERSON>t scroll về đầu trang, hiển thị khi người dùng scroll xuống
 */

import React, { useEffect, useState } from 'react';

interface ScrollToTopButtonProps {
  /**
   * Container ID để scroll (nếu không có sẽ scroll window)
   */
  containerId?: string;

  /**
   * Khoảng cách scroll tối thiểu để hiển thị button (px)
   */
  showAfter?: number;

  /**
   * Vị trí button
   */
  position?: {
    top?: string;
    bottom?: string;
    right?: string;
    left?: string;
    transform?: string;
  };

  /**
   * Custom className
   */
  className?: string;

  /**
   * Size của button
   */
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Component nút scroll to top
 */
export const ScrollToTopButton: React.FC<ScrollToTopButtonProps> = ({
  containerId,
  showAfter = 300,
  position = { top: '140px', left: '50%', transform: 'translateX(-50%)' },
  className = '',
  size = 'md'
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // Size configurations
  const sizeConfig = {
    sm: {
      button: 'w-10 h-10',
      icon: 'sm'
    },
    md: {
      button: 'w-12 h-12',
      icon: 'md'
    },
    lg: {
      button: 'w-14 h-14',
      icon: 'lg'
    }
  } as const;

  const config = sizeConfig[size];

  useEffect(() => {
    const handleScroll = () => {
      const container = containerId ? document.getElementById(containerId) : window;

      if (!container) return;

      const scrollTop = containerId
        ? (container as HTMLElement).scrollTop
        : window.pageYOffset || document.documentElement.scrollTop;

      setIsVisible(scrollTop > showAfter);
    };

    // Delay setup để đảm bảo DOM đã ready
    const setupScrollListener = () => {
      const container = containerId ? document.getElementById(containerId) : window;

      if (container) {
        container.addEventListener('scroll', handleScroll, { passive: true });
        handleScroll(); // Check initial scroll position
        return container;
      }
      return null;
    };

    // Try immediate setup
    let container = setupScrollListener();

    // If container not found, retry after a short delay
    let retryTimeout: NodeJS.Timeout | null = null;
    if (!container && containerId) {
      retryTimeout = setTimeout(() => {
        container = setupScrollListener();
      }, 100);
    }

    return () => {
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [containerId, showAfter]);

  const scrollToTop = () => {
    const container = containerId ? document.getElementById(containerId) : window;

    if (!container) return;

    if (containerId) {
      // Scroll container element
      (container as HTMLElement).scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } else {
      // Scroll window
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  if (!isVisible) return null;

  return (
    <button
      onClick={scrollToTop}
      className={`
        absolute z-[9999] ${config.button}
        bg-gray-900 hover:bg-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700
        text-red-500 rounded-full shadow-lg hover:shadow-xl
        border border-gray-700 dark:border-gray-600
        transition-all duration-300 ease-in-out
        hover:scale-110 active:scale-95
        flex items-center justify-center
        pointer-events-auto
        backdrop-blur-sm bg-opacity-95
        ${className}
      `}
      style={position}
      title="Scroll to top"
      aria-label="Scroll to top"
    >
      {/* Custom arrow up icon */}
      <svg
        className={`${config.icon === 'sm' ? 'w-4 h-4' : config.icon === 'md' ? 'w-5 h-5' : 'w-6 h-6'} text-red-500`}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        strokeWidth={3}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M5 15l7-7 7 7"
        />
      </svg>
    </button>
  );
};

export default ScrollToTopButton;
