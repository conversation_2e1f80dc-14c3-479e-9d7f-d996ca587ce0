# API Documentation cho FileSelectionModal

## Tổng quan
FileSelectionModal sử dụng 2 API chính từ module `src/modules/data/` để lấy dữ liệu file tri thức và media.

## 1. API File Tri thức (Knowledge Files)

### Hook: `useKnowledgeFiles`
**Đường dẫn:** `src/modules/data/knowledge-files/hooks/useKnowledgeFileQuery.ts`

**Endpoint:** `GET /user/knowledge-files`

**Tham số query:**
```typescript
interface QueryFileDto {
  page?: number;           // Số trang (mặc định: 1)
  limit?: number;          // Số item/trang (mặc định: 10)
  search?: string;         // Tìm kiếm theo tên file
  vectorStoreId?: string;  // Lọc theo vector store
  extensions?: string;     // Lọc theo phần mở rộng file
  sortBy?: string;         // Trường sắp xếp (mặc định: 'createdAt')
  sortDirection?: SortDirection; // Hướng sắp xếp (ASC/DESC)
}
```

**Response:**
```typescript
interface PaginatedFileResult {
  items: FileResponseDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

interface FileResponseDto {
  id: string;
  name: string;
  extension: string;
  storage: number;          // Kích thước file (bytes)
  vectorStoreId?: string;
  vectorStoreName?: string;
  viewUrl: string;          // URL để xem file
  createdAt: number;        // Unix timestamp
  updatedAt: number;        // Unix timestamp
}
```

**Cách sử dụng:**
```typescript
import { useKnowledgeFiles } from '@/modules/data/knowledge-files/hooks/useKnowledgeFileQuery';

const { data, isLoading, error } = useKnowledgeFiles({
  page: 1,
  limit: 10,
  search: 'tên file',
});
```

## 2. API Media Files

### Hook: `useMediaList`
**Đường dẫn:** `src/modules/data/media/hooks/useMediaQuery.ts`

**Endpoint:** `GET /media/my-media`

**Tham số query:**
```typescript
interface MediaQueryDto {
  page?: number;              // Số trang
  limit?: number;             // Số item/trang
  search?: string;            // Tìm kiếm theo tên
  status?: MediaStatusEnum;   // Lọc theo trạng thái
  sortBy?: string;            // Trường sắp xếp
  sortDirection?: SortDirection; // Hướng sắp xếp
}
```

**Response:**
```typescript
interface PaginatedMediaResult {
  items: MediaDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

interface MediaDto {
  id: string;
  name: string;
  description: string;
  size: number;             // Kích thước file (bytes)
  tags?: string[];          // Thẻ phân loại
  storageKey: string;       // Khóa lưu trữ
  ownedBy: number;          // ID người sở hữu
  createdAt: number;        // Unix timestamp
  updatedAt: number;        // Unix timestamp
  status: MediaStatusEnum;  // Trạng thái file
  viewUrl?: string;         // URL để xem file
}
```

**Cách sử dụng:**
```typescript
import { useMediaList } from '@/modules/data/media/hooks/useMediaQuery';

const { data, isLoading, error } = useMediaList({
  page: 1,
  limit: 10,
  search: 'tên media',
});
```

## 3. Enum và Constants

### MediaStatusEnum
```typescript
enum MediaStatusEnum {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DELETED = 'DELETED',
}
```

### SortDirection
```typescript
enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}
```

## 4. Cách tích hợp trong FileSelectionModal

```typescript
// Import hooks
import { useKnowledgeFiles } from '@/modules/data/knowledge-files/hooks/useKnowledgeFileQuery';
import { useMediaList } from '@/modules/data/media/hooks/useMediaQuery';

// Sử dụng trong component
const FileSelectionModal = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const pageSize = 10;

  // Query file tri thức
  const { data: knowledgeFilesData, isLoading: isLoadingKnowledge } = useKnowledgeFiles({
    page: currentPage,
    limit: pageSize,
    search: searchTerm || undefined,
  });

  // Query file media
  const { data: mediaFilesData, isLoading: isLoadingMediaFiles } = useMediaList({
    page: currentPage,
    limit: pageSize,
    search: searchTerm || undefined,
  });

  // Xử lý dữ liệu...
};
```

## 5. Lưu ý quan trọng

1. **Phân trang:** Cả 2 API đều hỗ trợ phân trang với `page` và `limit`
2. **Tìm kiếm:** Sử dụng tham số `search` để tìm kiếm theo tên file
3. **Loading states:** Cần xử lý `isLoading` cho UX tốt
4. **Error handling:** Cần xử lý lỗi từ API
5. **Cache:** React Query tự động cache dữ liệu
6. **Real-time:** Dữ liệu sẽ được invalidate khi có thay đổi

## 6. Ví dụ hoàn chỉnh

```typescript
const FileSelectionModal = ({ isOpen, onClose, onSelectFiles }) => {
  const [activeTab, setActiveTab] = useState<'knowledge' | 'media'>('knowledge');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  // API calls
  const knowledgeQuery = useKnowledgeFiles({
    page: currentPage,
    limit: 10,
    search: searchTerm || undefined,
  });

  const mediaQuery = useMediaList({
    page: currentPage,
    limit: 10,
    search: searchTerm || undefined,
  });

  // Chuyển đổi dữ liệu
  const fileItems = activeTab === 'knowledge' 
    ? knowledgeQuery.data?.items || []
    : mediaQuery.data?.items || [];

  const isLoading = activeTab === 'knowledge' 
    ? knowledgeQuery.isLoading 
    : mediaQuery.isLoading;

  // Render UI...
};
```

Tài liệu này cung cấp đầy đủ thông tin về API được sử dụng trong FileSelectionModal.
