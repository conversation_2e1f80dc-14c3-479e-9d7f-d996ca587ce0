import React, { useRef, useEffect, useMemo, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ChatMessage, ScrollArea, ReplyMessage, StreamErrorMessage, ScrollToBottomButton } from '@/shared/components/common';
import NotificationMessage from './NotificationMessage';
import { NotificationItem } from './NotificationContainer';
import MessageHistoryLoader from './MessageHistoryLoader';
import InlineThinkingBox from './InlineThinkingBox';
import { ChatMessage as ChatMessageType, ContentBlock, MessageStatus } from '@/shared/types/chat-streaming.types';
import { useSimpleCharacterStreaming } from '@/shared/hooks/common/useSimpleCharacterStreaming';
import './chat-animations.css';

interface ChatContentProps {
  messages: ChatMessageType[];
  isLoading?: boolean;
  isThinking?: boolean;
  notifications: NotificationItem[];
  onRemoveNotification: (id: string) => void;
  isStreaming?: boolean;

  // Center Notification Props
  centerNotification?: {
    message: string;
    type?: 'info' | 'success' | 'warning' | 'error';
    duration?: number;
  } | null;
  onCenterNotificationDismiss?: () => void;

  // Message History Props
  historyMessages?: ChatMessageType[];
  isLoadingHistory?: boolean;
  isLoadingMoreHistory?: boolean;
  hasMoreHistory?: boolean;
  historyError?: string | null;
  totalHistoryItems?: number;
  onLoadMoreHistory?: () => void;

  // Enhanced Lazy Loading Props
  enableLazyLoading?: boolean;
  lazyLoadTriggerDistance?: string;
  showLoadingProgress?: boolean;
  maxVisibleMessages?: number;
  enableScrollOptimization?: boolean;
  debug?: boolean;

  // Reply Message Props
  onReply?: (message: ReplyMessage) => void;
  onFocusMessage?: (messageId: string) => void;

  // Stream Error Props
  streamError?: {
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null;
  onRetryMessage?: () => void;

  // Simple Streaming Props
  currentStreamingText?: string;
  onTokenReceived?: (handler: (token: { text: string; messageId: string; isLast?: boolean }) => void) => void;
  enableSmoothStreaming?: boolean;
  streamingSpeed?: number;

  // Agent Avatar Props
  agentAvatar?: string;

  // Worker Thinking Props
  workerThinking?: {
    isVisible: boolean;
    content: string;
    isStreaming: boolean;
  };
}

const ChatContent = ({
  messages,
  notifications,
  onRemoveNotification,
  isStreaming = false,
  isThinking = false,

  // Center Notification Props
  centerNotification,
  onCenterNotificationDismiss,

  // Message History Props
  historyMessages = [],
  isLoadingHistory = false,
  isLoadingMoreHistory = false,
  hasMoreHistory = false,
  historyError = null,
  totalHistoryItems = 0,
  onLoadMoreHistory,

  // Enhanced Lazy Loading Props
  showLoadingProgress = false,
  maxVisibleMessages = 200,
  enableScrollOptimization = true,
  debug = false,

  // Reply Message Props
  onReply,
  onFocusMessage,

  // Stream Error Props
  streamError,
  onRetryMessage,

  // Simple Streaming Props
  currentStreamingText = '',
  onTokenReceived,
  enableSmoothStreaming = true,
  streamingSpeed = 50,

  // Agent Avatar Props
  agentAvatar,

  // Worker Thinking Props
  workerThinking,
}: ChatContentProps) => {
  const { t } = useTranslation();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [previousScrollHeight, setPreviousScrollHeight] = useState(0);
  const [isNearTop, setIsNearTop] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [hasInitialScrolled, setHasInitialScrolled] = useState(false);
  const [showCenterNotification, setShowCenterNotification] = useState(false);
  const [collapsedMessages, setCollapsedMessages] = useState<Set<string>>(new Set());
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const initialScrollTimeoutRef = useRef<NodeJS.Timeout>();
  const centerNotificationTimeoutRef = useRef<NodeJS.Timeout>();

  // Simple Character Streaming
  const characterStreaming = useSimpleCharacterStreaming({
    speed: streamingSpeed,
    debug: debug
  });

  /**
   * Debug logging
   */
  const log = useCallback((message: string, data?: unknown) => {
    if (debug) {
      console.log(`[ChatContent] ${message}`, data || '');
    }
  }, [debug]);

  /**
   * Handle token received from external source
   */
  const handleTokenReceived = useCallback((token: { text: string; messageId: string; isLast?: boolean }) => {
    if (!enableSmoothStreaming) {
      return;
    }

    log('Token received for streaming', {
      messageId: token.messageId,
      text: token.text,
      isLast: token.isLast
    });

    // Add token to character streaming
    characterStreaming.addToken(token);
  }, [enableSmoothStreaming, characterStreaming, log]);



  /**
   * Expose token handler to parent via callback
   */
  useEffect(() => {
    if (onTokenReceived) {
      onTokenReceived(handleTokenReceived);
    }
  }, [onTokenReceived, handleTokenReceived]);

  /**
   * Force complete streaming when streaming ends
   */
  useEffect(() => {
    if (!isStreaming && enableSmoothStreaming) {
      log('Force completing all streaming due to streaming end');
      // Force complete all active streaming messages
      Object.keys(characterStreaming.isStreaming).forEach(messageId => {
        if (characterStreaming.isStreaming[messageId]) {
          characterStreaming.forceComplete(messageId);
        }
      });
    }
  }, [isStreaming, enableSmoothStreaming, characterStreaming, log]);

  /**
   * Enhanced retry handler
   */
  const handleRetry = useCallback(() => {
    setRetryCount(prev => prev + 1);
    if (onLoadMoreHistory) {
      log('Retrying load more history', { attempt: retryCount + 1 });
      onLoadMoreHistory();
    }
  }, [onLoadMoreHistory, retryCount, log]);

  /**
   * Handler cho focus message
   */
  const handleFocusMessage = useCallback((messageId: string) => {
    if (onFocusMessage) {
      onFocusMessage(messageId);
    }

    // Scroll đến message
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });

      // Highlight effect
      messageElement.classList.add('highlight-message');
      setTimeout(() => {
        messageElement.classList.remove('highlight-message');
      }, 2000);
    }
  }, [onFocusMessage]);

  /**
   * Handler cho toggle collapse worker messages
   */
  const handleToggleCollapse = useCallback((messageId: string) => {
    setCollapsedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  }, []);

  /**
   * Handler khi InlineThinkingBox yêu cầu parent scroll
   */
  const handleThinkingBoxScroll = useCallback(() => {
    // Scroll xuống để hiển thị thinking box đầy đủ
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      });
      log('Scrolled to show thinking box');
    }, 150); // Delay để đảm bảo thinking box đã render
  }, [log]);

  /**
   * Optimized scroll handler với debouncing và auto-load more history
   *
   * Features:
   * - Auto-trigger loadMoreHistory khi scroll gần top (< 50px)
   * - Debounced để tối ưu performance (150ms)
   * - Track isNearTop state cho auto-scroll logic
   */
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    if (!enableScrollOptimization) return;

    // Clear previous timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Debounce scroll events
    scrollTimeoutRef.current = setTimeout(() => {
      try {
        // Null check để tránh lỗi khi component unmount
        if (!e.currentTarget) {
          log('Scroll event target is null, skipping');
          return;
        }

        // Sử dụng scrollAreaRef làm fallback nếu e.currentTarget null
        const scrollElement = e.currentTarget || scrollAreaRef.current;
        if (!scrollElement) {
          log('No scroll element available, skipping');
          return;
        }

        const { scrollTop, scrollHeight, clientHeight } = scrollElement;
        const isAtTop = scrollTop < 50;
        const isNearTopThreshold = scrollTop < 200;

        setIsNearTop(isNearTopThreshold);

        // Auto-trigger load more history khi scroll gần đến top
        if (isAtTop && hasMoreHistory && !isLoadingMoreHistory && !isLoadingHistory && onLoadMoreHistory) {
          log('Auto-triggering load more history due to scroll to top');
          onLoadMoreHistory();
        }

        // Debug scroll position
        log('Scroll position', {
          scrollTop,
          scrollHeight,
          clientHeight,
          isAtTop,
          isNearTop: isNearTopThreshold,
          hasMoreHistory,
          isLoadingMoreHistory,
          autoTriggered: isAtTop && hasMoreHistory && !isLoadingMoreHistory
        });
      } catch (error) {
        console.error('[ChatContent] Error in scroll handler:', error);
        log('Scroll handler error', error);
      }
    }, 150); // Increased debounce to 150ms for better performance
  }, [enableScrollOptimization, hasMoreHistory, isLoadingMoreHistory, isLoadingHistory, onLoadMoreHistory, log]);

  // Kết hợp history messages, current messages và notifications theo thứ tự thời gian
  const combinedItems = useMemo(() => {
    // Tạo mảng kết hợp tin nhắn và thông báo - hiển thị tất cả messages
    const items: Array<{
      type: 'message' | 'notification';
      id: string;
      timestamp: Date;
      data: ChatMessageType | NotificationItem;
      isHistory?: boolean;
    }> = [
        // Thêm history messages (cũ hơn)
        ...historyMessages.map(message => ({
          type: 'message' as const,
          id: `history-${message.id}`,
          timestamp: message.timestamp,
          data: message,
          isHistory: true,
        })),

        // Thêm current messages (mới hơn)
        ...messages.map(message => ({
          type: 'message' as const,
          id: message.id,
          timestamp: message.timestamp,
          data: message,
          isHistory: false,
        })),

        // Thêm thông báo với timestamp là thời điểm hiện tại
        ...notifications.map(notification => ({
          type: 'notification' as const,
          id: notification.id,
          // Tạo timestamp từ ID của thông báo (giả định ID có chứa timestamp)
          timestamp: new Date(parseInt(notification.id.split('-')[1] || Date.now().toString())),
          data: notification,
          isHistory: false,
        })),
      ];

    // Sắp xếp theo thời gian tăng dần
    const sortedItems = items.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    log('Combined items', {
      total: sortedItems.length,
      historyCount: historyMessages.length,
      currentCount: messages.length,
      notificationCount: notifications.length,
      // Debug: show first and last timestamps
      firstTimestamp: sortedItems[0]?.timestamp,
      lastTimestamp: sortedItems[sortedItems.length - 1]?.timestamp,
      // Debug: show history vs current message ranges
      historyRange: historyMessages.length > 0 ? {
        first: historyMessages[0]?.timestamp,
        last: historyMessages[historyMessages.length - 1]?.timestamp
      } : null,
      currentRange: messages.length > 0 ? {
        first: messages[0]?.timestamp,
        last: messages[messages.length - 1]?.timestamp
      } : null
    });

    return sortedItems;
  }, [historyMessages, messages, notifications, log]);

  /**
   * Optimized visible items với performance limit
   */
  const visibleItems = useMemo(() => {
    if (combinedItems.length <= maxVisibleMessages) {
      return combinedItems;
    }

    // Nếu có quá nhiều items, chỉ hiển thị những items gần đây nhất
    const startIndex = Math.max(0, combinedItems.length - maxVisibleMessages);
    const limited = combinedItems.slice(startIndex);

    log('Limited visible items', {
      total: combinedItems.length,
      visible: limited.length,
      startIndex
    });

    return limited;
  }, [combinedItems, maxVisibleMessages, log]);

  // Auto-scroll to bottom on initial load (when entering chat)
  // Ensures user sees the latest messages when first opening chat interface
  useEffect(() => {
    if (!hasInitialScrolled && visibleItems.length > 0) {
      // Delay để đảm bảo DOM đã render
      initialScrollTimeoutRef.current = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        setHasInitialScrolled(true);
        log('Initial auto-scroll to bottom', { itemCount: visibleItems.length });
      }, 200); // 200ms delay để đảm bảo DOM ready
    }

    return () => {
      if (initialScrollTimeoutRef.current) {
        clearTimeout(initialScrollTimeoutRef.current);
      }
    };
  }, [visibleItems.length, hasInitialScrolled, log]);

  // Scroll to bottom when new messages arrive (not history)
  useEffect(() => {
    log('Combined items updated', { count: combinedItems.length });

    // Chỉ scroll to bottom khi có new messages (không phải history)
    const hasNewMessages = messages.length > 0 || notifications.length > 0;
    const hasStreamingMessage = isStreaming;

    if ((hasNewMessages || hasStreamingMessage) && !isNearTop && hasInitialScrolled) {
      // Chỉ auto-scroll nếu:
      // 1. Có tin nhắn mới hoặc đang streaming
      // 2. User không đang xem tin nhắn cũ (scroll lên trên)
      // 3. Đã thực hiện initial scroll
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      log('Auto-scrolled to bottom', {
        hasNewMessages,
        hasStreamingMessage,
        isNearTop,
        hasInitialScrolled
      });
    }
  }, [messages, notifications, isStreaming, isNearTop, hasInitialScrolled, combinedItems.length, log]);

  // Auto-scroll khi InlineThinkingBox xuất hiện
  useEffect(() => {
    if (workerThinking?.isVisible && !isNearTop && hasInitialScrolled) {
      // Delay để đảm bảo thinking box đã render
      const scrollTimeout = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'end'
        });
        log('Auto-scrolled for thinking box visibility', {
          isVisible: workerThinking.isVisible,
          isNearTop,
          hasInitialScrolled
        });
      }, 300); // Delay lâu hơn để đảm bảo animation hoàn thành

      return () => clearTimeout(scrollTimeout);
    }
    return; // Explicit return for other cases
  }, [workerThinking?.isVisible, isNearTop, hasInitialScrolled, log]);

  // Enhanced scroll position management khi load more history
  useEffect(() => {
    if (isLoadingMoreHistory && scrollAreaRef.current) {
      const currentScrollHeight = scrollAreaRef.current.scrollHeight;
      setPreviousScrollHeight(currentScrollHeight);
      log('Saved scroll position before loading more', {
        scrollHeight: currentScrollHeight,
        scrollTop: scrollAreaRef.current.scrollTop
      });
    }
  }, [isLoadingMoreHistory, log]);

  // Enhanced restore scroll position sau khi load more history
  useEffect(() => {
    if (!isLoadingMoreHistory && previousScrollHeight > 0 && scrollAreaRef.current) {
      // Delay để đảm bảo DOM đã update
      const timeoutId = setTimeout(() => {
        if (scrollAreaRef.current) {
          const newScrollHeight = scrollAreaRef.current.scrollHeight;
          const scrollDiff = newScrollHeight - previousScrollHeight;
          const newScrollTop = scrollDiff;

          scrollAreaRef.current.scrollTop = newScrollTop;
          setPreviousScrollHeight(0);

          log('Restored scroll position after loading more', {
            previousHeight: previousScrollHeight,
            newHeight: newScrollHeight,
            scrollDiff,
            newScrollTop
          });
        }
      }, 100); // 100ms delay

      return () => clearTimeout(timeoutId);
    }

    // Return undefined explicitly for other cases
    return undefined;
  }, [isLoadingMoreHistory, previousScrollHeight, log]);

  // Handle center notification display
  useEffect(() => {
    if (centerNotification) {
      setShowCenterNotification(true);

      // Auto dismiss after duration (default 3000ms)
      const duration = centerNotification.duration || 3000;
      centerNotificationTimeoutRef.current = setTimeout(() => {
        setShowCenterNotification(false);
        // Call dismiss callback after fade out animation
        setTimeout(() => {
          onCenterNotificationDismiss?.();
        }, 300); // Wait for fade out animation
      }, duration);
    } else {
      setShowCenterNotification(false);
    }

    return () => {
      if (centerNotificationTimeoutRef.current) {
        clearTimeout(centerNotificationTimeoutRef.current);
      }
    };
  }, [centerNotification, onCenterNotificationDismiss]);

  // Cleanup scroll timeouts on unmount
  useEffect(() => {
    return () => {
      // Clear all timeouts to prevent memory leaks và stale closures
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = undefined;
      }
      if (initialScrollTimeoutRef.current) {
        clearTimeout(initialScrollTimeoutRef.current);
        initialScrollTimeoutRef.current = undefined;
      }
      if (centerNotificationTimeoutRef.current) {
        clearTimeout(centerNotificationTimeoutRef.current);
        centerNotificationTimeoutRef.current = undefined;
      }
    };
  }, []);

  // Không cần formatTime nữa vì đã được xử lý trong ChatMessage

  return (
    <div className="relative h-full">
      {/* Scroll to Bottom Button */}
      <ScrollToBottomButton
        containerId="chat-panel-scroll"
        showAfter={200}
        position={{ bottom: '20px', right: '24px' }}
        size="md"
        onClick={() => {
          messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        }}
      />
      <ScrollArea
        ref={scrollAreaRef}
        id="chat-panel-scroll"
        className="h-full p-4"
        height="100%"
        invisible={true}
        isChatPanel={true}
        onScroll={enableScrollOptimization ? handleScroll : undefined}
      >
        {visibleItems.length === 0 ? (
          <div
            className="flex flex-col items-center justify-center h-full text-center"
            style={{ marginTop: '0vh' }}
          >
            <div className="text-4xl mb-4">👋</div>
            <h2 className="text-xl font-semibold mb-2">{t('viewPanel.welcome')}</h2>
            <p className="text-gray-500 dark:text-gray-400 max-w-md">{t('chat.typeMessage')}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Enhanced Message History Loader - hiển thị ở đầu */}
            {(hasMoreHistory || isLoadingHistory || isLoadingMoreHistory || historyError) && (
              <MessageHistoryLoader
                isLoading={isLoadingMoreHistory}
                hasMore={hasMoreHistory}
                onLoadMore={historyError ? handleRetry : (onLoadMoreHistory || (() => { }))}
                error={historyError}
                totalItems={totalHistoryItems}
                currentItems={historyMessages.length}
                className="mb-4"
              />
            )}

            {/* Progress indicator nếu enabled */}
            {showLoadingProgress && isLoadingMoreHistory && totalHistoryItems > 0 && (
              <div className="mb-4 px-4">
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                  <span>Đã tải {historyMessages.length} / {totalHistoryItems} tin nhắn</span>
                  <span>{Math.round((historyMessages.length / totalHistoryItems) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                  <div
                    className="bg-blue-500 h-1.5 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${Math.min((historyMessages.length / totalHistoryItems) * 100, 100)}%` }}
                  />
                </div>
              </div>
            )}

            {/* Performance indicator nếu có giới hạn items */}
            {combinedItems.length > maxVisibleMessages && (
              <div className="mb-4 px-4 py-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <div className="flex items-center text-xs text-yellow-700 dark:text-yellow-300">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Hiển thị {visibleItems.length} / {combinedItems.length} tin nhắn gần đây nhất để tối ưu hiệu suất
                </div>
              </div>
            )}

            {/* Hiển thị tin nhắn và thông báo theo thứ tự thời gian */}
            {visibleItems.map((item, index) => {
              const isStreamingMessage = item.type === 'message' &&
                isStreaming &&
                (item.data as ChatMessageType).status === MessageStatus.STREAMING;

              // Tìm tin nhắn user cuối cùng trong danh sách để hiển thị thinking box
              const lastUserMessageIndex = visibleItems.map((item, idx) => ({ item, idx }))
                .filter(({ item }) => item.type === 'message' && (item.data as ChatMessageType).sender === 'user')
                .pop()?.idx;

              const shouldShowThinkingBox = item.type === 'message' &&
                (item.data as ChatMessageType).sender === 'user' &&
                index === lastUserMessageIndex &&
                workerThinking?.isVisible;

              return (
                <React.Fragment key={item.id}>
                  {item.type === 'message' ? (
                    // Hiển thị tin nhắn với staggered animation (không delay cho streaming messages)
                    <div
                      className="animate-fadeInUp"
                      style={{
                        animationDelay: isStreamingMessage ? '0ms' : `${Math.min(index * 100, 500)}ms`,
                        animationFillMode: 'both'
                      }}
                    >
                      <ChatMessage
                        content={(() => {
                          const messageData = item.data as ChatMessageType;
                          const messageId = messageData.id;

                          // Use streamed text if available and message is streaming
                          if (enableSmoothStreaming && isStreamingMessage) {
                            const streamedText = characterStreaming.getDisplayedText(messageId);
                            if (streamedText) {
                              return streamedText;
                            }
                          }

                          // Use current streaming text as fallback
                          if (isStreamingMessage && currentStreamingText) {
                            return currentStreamingText;
                          }

                          // Default content handling
                          const content = messageData.content;
                          if (typeof content === 'string') {
                            return content;
                          } else if (Array.isArray(content)) {
                            return content.map((block: ContentBlock) => block.content || '').join(' ');
                          } else {
                            return String(content);
                          }
                        })()}
                        sender={(item.data as ChatMessageType).sender}
                        timestamp={(item.data as ChatMessageType).timestamp}
                        {...(() => {
                          const messageData = item.data as ChatMessageType;

                          // ✅ Sử dụng agentId từ message metadata thay vì global agentAvatar
                          if (messageData.sender === 'supervisor') {
                            // Nếu message có agentId trong metadata, sử dụng avatar của agent đó
                            if (messageData.metadata?.agentId) {
                              // TODO: Implement getAgentAvatarById service
                              // return { avatar: getAgentAvatarById(messageData.metadata.agentId) };

                              // Tạm thời fallback về agentAvatar global nếu có
                              return agentAvatar ? { avatar: agentAvatar } : {};
                            }
                            // Fallback về agentAvatar global
                            return agentAvatar ? { avatar: agentAvatar } : {};
                          } else if (messageData.sender === 'ai') {
                            return { avatar: '/assets/images/ai-agents/assistant-robot.svg' };
                          }
                          return {};
                        })()}
                        className="mb-4"
                        messageId={(item.data as ChatMessageType).id}
                        {...(onReply && { onReply })}
                        {...(onFocusMessage && { onFocusMessage: handleFocusMessage })}
                        isStreaming={isStreamingMessage}
                        isWaiting={enableSmoothStreaming ? characterStreaming.isMessageWaiting((item.data as ChatMessageType).id) : false}
                        isCollapsed={(() => {
                          const messageData = item.data as ChatMessageType;
                          // Worker messages có thể collapsed từ metadata hoặc manual toggle
                          if (messageData.sender === 'worker') {
                            return messageData.metadata?.collapsed || collapsedMessages.has(messageData.id);
                          }
                          return false;
                        })()}
                        {...(() => {
                          const messageData = item.data as ChatMessageType;
                          const extraProps: Record<string, unknown> = {};

                          // Pass metadata cho animation
                          if (messageData.metadata) {
                            extraProps['metadata'] = messageData.metadata;
                          }

                          // Pass messageStatus cho animation
                          if (messageData.status) {
                            extraProps['messageStatus'] = messageData.status === MessageStatus.STREAMING ? 'streaming' :
                                                       messageData.status === MessageStatus.COMPLETED ? 'completed' : 'error';
                          }

                          // Worker-specific props
                          if (messageData.sender === 'worker') {
                            extraProps['onToggleCollapse'] = () => handleToggleCollapse(messageData.id);

                            // Chỉ thêm contentPreview nếu có giá trị
                            if (messageData.metadata?.contentPreview) {
                              extraProps['contentPreview'] = messageData.metadata.contentPreview;
                            }
                          }

                          return extraProps;
                        })()}
                      />
                    </div>
                  ) : (
                    // Hiển thị thông báo
                    <div
                      className="animate-fadeInUp"
                      style={{
                        animationDelay: `${Math.min(index * 100, 500)}ms`,
                        animationFillMode: 'both'
                      }}
                    >
                      <NotificationMessage
                        id={(item.data as NotificationItem).id}
                        type={(item.data as NotificationItem).type}
                        message={(item.data as NotificationItem).message}
                        duration={(item.data as NotificationItem).duration || 5000}
                        onRemove={onRemoveNotification}
                        className="mb-4"
                      />
                    </div>
                  )}

                  {/* Hiển thị InlineThinkingBox ngay sau tin nhắn user cuối cùng */}
                  {shouldShowThinkingBox && (
                    <div className="mt-2 mb-4">
                      <InlineThinkingBox
                        content={workerThinking.content}
                        isVisible={workerThinking.isVisible}
                        isStreaming={workerThinking.isStreaming}
                        onHide={() => {
                          console.log('[ChatContent] Inline worker thinking box hidden');
                        }}
                        onRequestParentScroll={handleThinkingBoxScroll}
                        className="animate-fadeInUp"
                      />
                    </div>
                  )}
                </React.Fragment>
              );
            })}

            {/* Hiển thị InlineThinkingBox ở cuối nếu không có tin nhắn user nào */}
            {workerThinking?.isVisible && !visibleItems.some(item =>
              item.type === 'message' && (item.data as ChatMessageType).sender === 'user'
            ) && (
              <div className="mt-2 mb-4">
                <InlineThinkingBox
                  content={workerThinking.content}
                  isVisible={workerThinking.isVisible}
                  isStreaming={workerThinking.isStreaming}
                  onHide={() => {
                    console.log('[ChatContent] Inline worker thinking box hidden (fallback)');
                  }}
                  onRequestParentScroll={handleThinkingBoxScroll}
                  className="animate-fadeInUp"
                />
              </div>
            )}

            {/* Stream Error Message - hiển thị sau message cuối cùng */}
            {(() => {
              console.log('[ChatContent] 🚨 Stream error check (bottom):', {
                streamError,
                hasRetryCallback: !!onRetryMessage,
                shouldShow: !!(streamError && onRetryMessage)
              });

              if (streamError && onRetryMessage) {
                return (
                  <div className="mt-4">
                    <StreamErrorMessage
                      error={streamError}
                      onRetry={onRetryMessage}
                      onDismiss={() => {
                        console.log('[ChatContent] 🚨 Stream error dismissed');
                        // Có thể thêm callback để clear error nếu cần
                      }}
                      className="mb-4"
                    />
                  </div>
                );
              }
              return null;
            })()}

            {/* Thinking Indicator - hiển thị khi AI đang thinking */}
            {isThinking && (
              <div className="mt-4 mb-4">
                <div className="flex items-center space-x-3 px-4 py-3 bg-purple-50 dark:bg-purple-950/30 rounded-lg border border-purple-200 dark:border-purple-800/50">
                  {/* Thinking animation */}
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>

                  {/* Thinking text */}
                  <span className="text-sm text-purple-700 dark:text-purple-300 font-medium">
                    {t('chat.aiThinking', 'AI đang suy nghĩ...')}
                  </span>
                </div>
              </div>
            )}

            {/* Streaming message đã được tích hợp vào messages array */}
            <div ref={messagesEndRef} />
          </div>
        )}
      </ScrollArea>
      {/* Bottom Notification - Positioned above chat input */}
      {centerNotification && (
        <div
          className={`absolute bottom-4 left-4 right-4 z-50 transition-all duration-300 ease-out ${
            showCenterNotification ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
          }`}
        >
          <div
            className={`
              w-full px-4 py-3 rounded-lg border shadow-sm
              transform transition-all duration-300 ease-out
              ${showCenterNotification ? 'scale-100' : 'scale-95'}
              ${
                centerNotification.type === 'error'
                  ? 'bg-red-50 border-red-200 dark:bg-red-950/30 dark:border-red-800/50'
                  : centerNotification.type === 'warning'
                  ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-950/30 dark:border-yellow-800/50'
                  : centerNotification.type === 'success'
                  ? 'bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-800/50'
                  : 'bg-blue-50 border-blue-200 dark:bg-blue-950/30 dark:border-blue-800/50'
              }
            `}
            onClick={() => {
              setShowCenterNotification(false);
              setTimeout(() => {
                onCenterNotificationDismiss?.();
              }, 300);
            }}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                {/* Icon based on type */}
                <div className="flex-shrink-0 mt-0.5">
                  {centerNotification.type === 'error' ? (
                    <svg className="w-5 h-5 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  ) : centerNotification.type === 'warning' ? (
                    <svg className="w-5 h-5 text-yellow-500 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  ) : centerNotification.type === 'success' ? (
                    <svg className="w-5 h-5 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  {/* Title */}
                  <div className={`text-sm font-medium mb-1 ${
                    centerNotification.type === 'error'
                      ? 'text-red-800 dark:text-red-200'
                      : centerNotification.type === 'warning'
                      ? 'text-yellow-800 dark:text-yellow-200'
                      : centerNotification.type === 'success'
                      ? 'text-green-800 dark:text-green-200'
                      : 'text-blue-800 dark:text-blue-200'
                  }`}>
                    {centerNotification.type === 'error' ? 'Error Text' :
                     centerNotification.type === 'warning' ? 'Warning Text' :
                     centerNotification.type === 'success' ? 'Success Text' :
                     'Info Text'}
                  </div>

                  {/* Message */}
                  <div className={`text-sm ${
                    centerNotification.type === 'error'
                      ? 'text-red-700 dark:text-red-300'
                      : centerNotification.type === 'warning'
                      ? 'text-yellow-700 dark:text-yellow-300'
                      : centerNotification.type === 'success'
                      ? 'text-green-700 dark:text-green-300'
                      : 'text-blue-700 dark:text-blue-300'
                  }`}>
                    {centerNotification.message}
                  </div>
                </div>
              </div>

              {/* Close button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowCenterNotification(false);
                  setTimeout(() => {
                    onCenterNotificationDismiss?.();
                  }, 300);
                }}
                className={`ml-4 flex-shrink-0 p-1 rounded-md transition-colors ${
                  centerNotification.type === 'error'
                    ? 'text-red-400 hover:text-red-600 hover:bg-red-100 dark:hover:bg-red-900/50'
                    : centerNotification.type === 'warning'
                    ? 'text-yellow-400 hover:text-yellow-600 hover:bg-yellow-100 dark:hover:bg-yellow-900/50'
                    : centerNotification.type === 'success'
                    ? 'text-green-400 hover:text-green-600 hover:bg-green-100 dark:hover:bg-green-900/50'
                    : 'text-blue-400 hover:text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/50'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatContent;
