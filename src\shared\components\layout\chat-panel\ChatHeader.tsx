import { Avatar, Dropdown, IconCard, Tooltip } from '@/shared/components/common';
import { useSimpleAIAgents } from '@/shared/hooks/useAIAgents';
import { AgentSimpleDto } from '@/shared/types/ai-agents.types';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

interface ChatHeaderProps {
  onNewChat: () => void;
  onClose: () => void;

  // Thread integration props
  threadId?: string | null;
  threadName?: string | null;
  showThreadName?: boolean;
  onThreadNameChange?: (threadId: string, newName: string) => void | Promise<void>;
  isThreadSwitching?: boolean;

  // Agent selection callback
  onAgentChange?: (agent: AgentSimpleDto) => void;
}

const ChatHeader = ({
  onNewChat,
  onClose,
  threadId,
  threadName,
  showThreadName = false,
  onThreadNameChange,
  isThreadSwitching = false,
  onAgentChange
}: ChatHeaderProps) => {
  const { t } = useTranslation();

  // Fetch AI agents data từ API
  const { data: agentsData, isLoading } = useSimpleAIAgents({
    page: 1,
    limit: 20, // Lấy đủ agents để hiển thị
    sortBy: 'createdAt',
    sortDirection: 'DESC'
  });

  // Memoize agents array để tránh re-render không cần thiết
  const agents = useMemo(() => {
    // Lấy danh sách agents từ response
    const apiAgents = agentsData?.items || [];

    // Agent mặc định
    const defaultAgent: AgentSimpleDto = {
      id: 'default',
      name: 'RedAI Agent',
      avatar: '/assets/images/ai-agents/assistant-robot.svg'
    };

    // Chèn agent mặc định lên đầu mảng
    return [defaultAgent, ...apiAgents];
  }, [agentsData?.items]);

  // State cho selected agent
  const [selectedAgent, setSelectedAgent] = useState<AgentSimpleDto | null>(null);
  const navigate = useNavigate();

  // Set agent đầu tiên làm default khi có data
  useEffect(() => {
    if (agents.length > 0 && !selectedAgent) {
      const defaultAgent = agents[0] || null;
      setSelectedAgent(defaultAgent);
      // Notify parent component về agent selection
      if (defaultAgent && onAgentChange) {
        onAgentChange(defaultAgent);
      }
    }
  }, [agents, selectedAgent, onAgentChange]);

  // Debug logging for thread props
  useEffect(() => {
    console.log('[ChatHeader] Thread props changed:', {
      threadId,
      threadName,
      showThreadName,
      isThreadSwitching,
      hasOnThreadNameChange: !!onThreadNameChange,
    });
  }, [threadId, threadName, showThreadName, isThreadSwitching, onThreadNameChange]);

  const handleThreadNameClick = () => {
    navigate('/threads');
  };

  // Nếu đang loading hoặc chưa có agent nào được chọn
  if (isLoading || !selectedAgent) {
    return (
      <div className="flex items-center justify-between p-3 bg-white dark:bg-dark">
        <div className="flex items-center">
          <div className="flex items-center p-2">
            {isLoading && (
              <>
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                <div className="ml-2 w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Tooltip content={t('chat.newChat')} position="bottom">
            <IconCard icon="plus" variant="primary" onClick={onNewChat} title={t('chat.newChat')} />
          </Tooltip>
          <Tooltip content={t('common.close')} position="bottom">
            <IconCard
              icon="chevron-left"
              variant="default"
              onClick={onClose}
              title={t('common.close')}
            />
          </Tooltip>
        </div>
      </div>
    );
  }

  // Create dropdown items from AI agents
  const agentItems = agents.map((agent: AgentSimpleDto) => ({
    id: agent.id,
    label: (
      <div className="flex items-center">
        <Avatar src={agent.avatar} alt={agent.name} size="sm" className="mr-2" />
        <span>{agent.name}</span>
      </div>
    ),
    onClick: () => {
      setSelectedAgent(agent);
      // Notify parent component về agent selection
      if (onAgentChange) {
        onAgentChange(agent);
      }
    },
  }));

  return (
    <div className="flex items-center justify-between p-3 bg-white dark:bg-dark">
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <Dropdown
          trigger={
            <div className="flex items-center cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-lighter p-2 rounded flex-shrink-0">
              <Avatar src={selectedAgent.avatar} alt={selectedAgent.name} size="sm" />
              <span className="ml-2 font-medium">{selectedAgent.name}</span>
              <svg
                className="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </div>
          }
          items={agentItems}
          placement="bottom-left"
        />

        {/* Thread Name Display */}
        {showThreadName && (
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            {isThreadSwitching && (
              <div className="flex items-center space-x-2 animate-pulse">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                <span className="text-sm text-gray-500 dark:text-gray-400">Đang chuyển thread...</span>
              </div>
            )}
          </div>
        )}
      </div>
      <div className="flex items-center space-x-2">
        <Tooltip content={t('chat.newChat')} position="bottom">
          <IconCard icon="plus" variant="default" onClick={onNewChat} title={t('chat.newChat')} />
        </Tooltip>
        <Tooltip content={t('common.toThreads')} position="bottom">
          <IconCard icon="menu" variant='default' onClick={handleThreadNameClick} title={t('common.toThreads')} />
        </Tooltip>
        <Tooltip content={t('common.close')} position="bottom">
          <IconCard
            icon="chevron-left"
            variant="default"
            onClick={onClose}
            title={t('common.close')}
          />
        </Tooltip>
      </div>
    </div>
  );
};

export default ChatHeader;
