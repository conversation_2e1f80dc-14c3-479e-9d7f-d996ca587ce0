import { useEffect, useState } from 'react';
import Cha<PERSON><PERSON>eader from './ChatHeader';
import ChatContent from './ChatContent';
import ChatInput from './ChatInput';
import { ReplyMessage } from '@/shared/components/common';
import useChatNotification from '@/shared/hooks/common/useChatNotification';
import { useChatStream } from '@/shared/hooks/common';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { useRPointUpdate } from '@/shared/hooks/common/useRPointUpdate';
import { chatConfigService } from '@/shared/services/chat-config.service';
import { useChatPanelIntegration } from '@/shared/hooks/useChatPanelIntegration';
import { useChatInputFocus } from '@/shared/hooks/useGlobalKeyboardShortcuts';
import { useAppDispatch, useAppSelector } from '@/shared/store';
import {
  setCurrentThread,
  updateThreadName,
  setThreadSwitching,
  threadCreated,
  threadDeleted
} from '@/shared/store/slices/threadIntegrationSlice';


interface ChatPanelProps {
  onClose: () => void;
  onKeywordDetected?: (keyword: string) => void;
  isVisible?: boolean;

  // Thread integration props
  enableThreadIntegration?: boolean;
  onThreadCreated?: (threadId: string, threadName: string) => void;
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;
  onThreadNameChanged?: (threadId: string, newName: string) => void;
  onThreadDeleted?: (threadId: string) => void;
}

const ChatPanel = ({
  onClose,
  onKeywordDetected,
  isVisible = true,
  enableThreadIntegration = false,
  onThreadCreated,
  onThreadSwitched,
  onThreadNameChanged,
  onThreadDeleted
}: ChatPanelProps) => {
  const { notifications, addNotification, removeNotification } = useChatNotification();
  const [replyMessage, setReplyMessage] = useState<ReplyMessage | null>(null);
  const [centerNotification, setCenterNotification] = useState<{
    message: string;
    type?: 'info' | 'success' | 'warning' | 'error';
    duration?: number;
  } | null>(null);

  // State cho selected agent
  const [selectedAgent, setSelectedAgent] = useState<{ id: string; name: string; avatar?: string } | null>(null);

  // RPoint update handler
  const { handleRPointUpdate } = useRPointUpdate({
    useUpdatedBalance: true, // Sử dụng updatedBalance thay vì trừ cost
    onUpdate: (newBalance, cost, timestamp) => {
      console.log('[ChatPanel] 🪙 R-Point updated silently:', {
        newBalance,
        cost,
        timestamp: new Date(timestamp).toLocaleString()
      });
      console.log('[ChatPanel] 🪙 ViewHeader should now show new balance:', newBalance);
      // Chỉ log, không hiển thị notification
    },
    onError: (error) => {
      console.error('[ChatPanel] 🚨 R-Point update error:', error);
      // Chỉ log error, không hiển thị notification
    }
  });

  // Wrapper để log SSE RPoint events
  const wrappedHandleRPointUpdate = (rPointCost: number, updatedBalance: string, timestamp: number) => {
    console.log('[ChatPanel] 🪙 SSE RPoint event received:', {
      rPointCost,
      updatedBalance,
      timestamp,
      parsedBalance: parseInt(updatedBalance, 10)
    });
    handleRPointUpdate(rPointCost, updatedBalance, timestamp);
  };

  // Auth để lấy token
  const { getToken } = useAuthCommon();

  // Lấy chat config
  const chatConfig = chatConfigService.getConfig();

  // Chat panel integration
  const { registerChatPanel } = useChatPanelIntegration();
  const { focusChatInput } = useChatInputFocus();

  // Redux for thread integration
  const dispatch = useAppDispatch();
  const threadIntegration = useAppSelector(state => state.threadIntegration);



  // Chat streaming hook với thread events
  const chatStream = useChatStream({
    agentId: chatConfig.agentId,
    apiBaseUrl: chatConfig.apiBaseUrl,
    sseBaseUrl: chatConfig.sseBaseUrl,
    alwaysApproveToolCall: chatConfig.alwaysApproveToolCall,
    getAuthToken: async () => {
      const token = getToken() || '';
      return token;
    },
    debug: chatConfig.debug,
    onRPointUpdate: wrappedHandleRPointUpdate, // Pass wrapped RPoint handler
    ...(enableThreadIntegration && {
      threadEvents: {
        onThreadCreated: (threadId, threadName) => {
          console.log('[ChatPanel] Thread created:', { threadId, threadName });

          // Dispatch thread creation event với full info
          dispatch(threadCreated({
            threadId,
            threadName
          }));

          onThreadCreated?.(threadId, threadName);
        },
        onThreadLoaded: (threadId, threadName) => {
          console.log('[ChatPanel] Thread loaded:', { threadId, threadName });

          // Set current thread với full info
          dispatch(setCurrentThread({
            threadId,
            threadName,
            isNew: false
          }));
          dispatch(setThreadSwitching(false));

          console.log('[ChatPanel] Thread loaded and Redux state updated');
        },
        onThreadSwitched: (fromThreadId, toThreadId) => {
          console.log('[ChatPanel] Thread switched:', { fromThreadId, toThreadId });

          // Note: Thread info sẽ được update trong onThreadLoaded event
          // Ở đây chỉ cần set threadId và switching state
          dispatch(setCurrentThread({
            threadId: toThreadId,
            threadName: null // Sẽ được update trong onThreadLoaded
          }));
          dispatch(setThreadSwitching(false));

          console.log('[ChatPanel] Thread switched and Redux state updated');
          onThreadSwitched?.(fromThreadId, toThreadId);
        },
        onThreadNameChanged: (threadId, newName) => {
          console.log('[ChatPanel] Thread name changed:', {
            threadId,
            newName,
            currentThreadName: threadIntegration.currentThreadName,
            currentThreadId: threadIntegration.currentThreadId
          });

          // Always update Redux state để đảm bảo sync
          console.log('[ChatPanel] Dispatching updateThreadName to Redux');
          dispatch(updateThreadName({ threadId, newName }));

          // If this is current thread, also update the current thread name immediately
          if (threadId === threadIntegration.currentThreadId) {
            console.log('[ChatPanel] Updating current thread name in Redux');
            dispatch(setCurrentThread({
              threadId,
              threadName: newName,
              isNew: threadIntegration.currentThreadInfo?.isNew || false
            }));
          }

          onThreadNameChanged?.(threadId, newName);
        },
        onThreadDeleted: (threadId) => {
          console.log('[ChatPanel] Thread deleted:', {
            threadId,
            currentThreadId: threadIntegration.currentThreadId,
            isCurrentThread: threadId === threadIntegration.currentThreadId
          });

          // Dispatch thread deletion event to Redux (nextThreadId sẽ được xử lý bởi useThreadManagement)
          dispatch(threadDeleted({ threadId, nextThreadId: null }));

          onThreadDeleted?.(threadId);
        }
      }
    })
  });

  // Use ChatMessage directly without conversion
  const messages = chatStream.messages || [];
  const historyMessages = chatStream.historyMessages || [];

  // Debug logging for thread integration state
  useEffect(() => {
    console.log('[ChatPanel] Thread integration state changed:', {
      enableThreadIntegration,
      currentThreadId: threadIntegration.currentThreadId,
      currentThreadName: threadIntegration.currentThreadName,
      isThreadSwitching: threadIntegration.isThreadSwitching,
      lastUpdated: threadIntegration.lastUpdated,
      chatStreamThreadId: chatStream.threadId,
      chatStreamThreadName: chatStream.threadName
    });
  }, [enableThreadIntegration, threadIntegration, chatStream.threadId, chatStream.threadName]);

  // TODO: Thread switching will be handled through Redux actions
  // Components will dispatch actions and listen to state changes

  // Sync current thread với Redux khi ChatPanel mount hoặc thread change
  useEffect(() => {
    if (chatStream.threadId && chatStream.threadName) {
      // Chỉ update nếu khác với current Redux state
      if (threadIntegration.currentThreadId !== chatStream.threadId) {
        console.log('[ChatPanel] Syncing current thread to Redux:', {
          threadId: chatStream.threadId,
          threadName: chatStream.threadName,
          currentReduxThreadId: threadIntegration.currentThreadId
        });
        dispatch(setCurrentThread({
          threadId: chatStream.threadId,
          threadName: chatStream.threadName,
          isNew: false
        }));
      }
    }
  }, [chatStream.threadId, chatStream.threadName, threadIntegration.currentThreadId, dispatch]);

  // Listen to Redux state changes và sync với chatStream
  useEffect(() => {
    // Nếu Redux có thread switching request và khác với current chatStream thread
    if (threadIntegration.isThreadSwitching &&
        threadIntegration.currentThreadId &&
        threadIntegration.currentThreadId !== chatStream.threadId) {

      console.log('[ChatPanel] Redux requested thread switch:', {
        targetThreadId: threadIntegration.currentThreadId,
        currentChatStreamThreadId: chatStream.threadId,
        isThreadSwitching: threadIntegration.isThreadSwitching
      });

      // Switch chatStream to target thread
      if (chatStream.switchToThread) {
        chatStream.switchToThread(threadIntegration.currentThreadId)
          .then(() => {
            console.log('[ChatPanel] Successfully switched chatStream to thread:', threadIntegration.currentThreadId);
          })
          .catch((error) => {
            console.error('[ChatPanel] Failed to switch chatStream to thread:', error);
            // Clear switching state on error
            dispatch(setThreadSwitching(false));
          });
      }
    }
  }, [threadIntegration.isThreadSwitching, threadIntegration.currentThreadId, chatStream.threadId, chatStream.switchToThread, dispatch]);

  // Register ChatPanel với integration manager
  useEffect(() => {
    console.log('[ChatPanel] Registering with integration manager');
    registerChatPanel({
      chatStream,
      focusChatInput
    });

    return () => {
      console.log('[ChatPanel] Unregistering from integration manager');
      registerChatPanel({
        chatStream: null,
        focusChatInput: null
      });
    };
  }, [chatStream, focusChatInput, registerChatPanel]);

  // Handle errors
  useEffect(() => {
    if (chatStream.error) {
      addNotification('error', chatStream.error, 5000);
    }
  }, [chatStream.error, addNotification]);

  // Handle new chat
  const handleNewChat = async () => {
    try {
      // 1. Cancel current SSE and API calls if any
      if (chatStream.isStreaming || chatStream.isLoading || chatStream.isConnected) {
        console.log('[ChatPanel] Cancelling current operations before creating new thread...');
        await chatStream.stopStreaming();
      }

      // 2. Clear messages và reset thread
      chatStream.clearMessages();
      // Clear reply message
      setReplyMessage(null);

      // 3. Tạo thread mới
      console.log('[ChatPanel] Creating new thread...');
      const result = await chatStream.createNewThread();

      // 4. Show success notification
      setCenterNotification({
        message: 'Đã tạo cuộc hội thoại mới!',
        type: 'success',
        duration: 3000
      });

      console.log('[ChatPanel] New thread created successfully:', result);
    } catch (error) {
      console.error('[ChatPanel] Failed to create new thread:', error);
      setCenterNotification({
        message: 'Không thể tạo cuộc hội thoại mới. Vui lòng thử lại.',
        type: 'error',
        duration: 5000
      });
    }
  };

  // Handle reply message
  const handleReply = (message: ReplyMessage) => {
    setReplyMessage(message);
  };

  // Handle clear reply
  const handleClearReply = () => {
    setReplyMessage(null);
  };

  // Handler cho agent change
  const handleAgentChange = (agent: { id: string; name: string; avatar?: string }) => {
    console.log('[ChatPanel] Agent changed:', agent);
    setSelectedAgent(agent);
  };

  // Handle focus message
  const handleFocusMessage = (messageId: string) => {
    console.log('[ChatPanel] Focus message:', messageId);
    // Logic để focus message đã được implement trong ChatContent
  };

  // Logic gửi tin nhắn đã được di chuyển vào ChatInputBox

  // Custom notification handler
  // const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
  //   console.log(`[ChatPanel] Showing notification: ${type} - ${message}`);

  //   // Thêm notification mới và tự động xóa sau 5 giây
  //   const id = addNotification(type, message, 5000);
  //   console.log(`[ChatPanel] Added notification with ID: ${id}`);

  //   // Kiểm tra xem notification đã được thêm vào state chưa
  //   console.log(`[ChatPanel] Current notifications:`, notifications);

  //   // Đảm bảo ChatContent được cập nhật
  //   setTimeout(() => {
  //     console.log(
  //       `[ChatPanel] Checking if notification ${id} is visible:`,
  //       notifications.some((n: unknown) => (n as unknown as { id: string }).id === id)
  //     );
  //   }, 500);

  //   return id;
  // };

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-dark relative w-full ${
      isVisible ? '' : 'pointer-events-none'
    }`}>
      {/* Header cố định ở trên cùng với z-index cao */}
      <div className="sticky top-0 z-10 bg-white dark:bg-dark shadow-sm mb-4 flex-shrink-0">
        <ChatHeader
          onNewChat={handleNewChat}
          onClose={onClose}
          onAgentChange={handleAgentChange}
          {...(enableThreadIntegration && {
            threadId: threadIntegration.currentThreadId,
            threadName: threadIntegration.currentThreadName,
            showThreadName: true,
            onThreadNameChange: chatStream.updateThreadName,
            isThreadSwitching: threadIntegration.isThreadSwitching
          })}
        />
      </div>

      {/* Messages area - scrollable */}
      <div className="flex-1 overflow-hidden">
        <ChatContent
          messages={messages}
          isLoading={chatStream.isLoading}
          isThinking={chatStream.isThinking}
          notifications={notifications}
          onRemoveNotification={removeNotification}
          isStreaming={chatStream.isStreaming}

          // Center Notification Props
          centerNotification={centerNotification}
          onCenterNotificationDismiss={() => setCenterNotification(null)}

          // Message History Props
          historyMessages={historyMessages}
          isLoadingHistory={chatStream.isLoadingHistory}
          isLoadingMoreHistory={chatStream.isLoadingMoreHistory}
          hasMoreHistory={chatStream.hasMoreHistory}
          historyError={chatStream.historyError}
          totalHistoryItems={chatStream.totalHistoryItems}
          onLoadMoreHistory={chatStream.loadMoreHistory}

          // Agent Avatar Props - lấy từ header
          {...(selectedAgent?.avatar && { agentAvatar: selectedAgent.avatar })}

          // Worker Thinking Props
          workerThinking={chatStream.workerThinking}

          // Enhanced Lazy Loading Props
          enableLazyLoading={true}
          lazyLoadTriggerDistance="100px"
          maxVisibleMessages={200}
          enableScrollOptimization={true}

          // Reply Message Props
          onReply={handleReply}
          onFocusMessage={handleFocusMessage}

          // Stream Error Props
          streamError={chatStream.streamError}
          onRetryMessage={chatStream.retryLastMessage}
        />
      </div>

      {/* Input area - fixed at bottom */}
      <div className="flex-shrink-0">
        <ChatInput
          {...(onKeywordDetected && { onKeywordDetected })}
          chatStream={chatStream}
          addNotification={addNotification}
          replyMessage={replyMessage}
          onClearReply={handleClearReply}
          setCenterNotification={setCenterNotification}
        />
      </div>

    </div>
  );
};

export default ChatPanel;
