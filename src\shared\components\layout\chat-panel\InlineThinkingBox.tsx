/**
 * InlineThinkingBox Component
 * Hiển thị worker messages inline trong chat flow
 * Tự động ẩn sau 4000ms khi hoàn thành hoặc disconnect
 * Animation ẩn mượt mà trong 800ms, tránh nháy màn hình
 * Auto-scroll chỉ trong nội bộ component, tránh xung đột với ChatContent
 */

import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import '@/shared/styles/scrollbar.css';
import './thinking-box-animations.css';

interface InlineThinkingBoxProps {
  /**
   * Nội dung thinking text
   */
  content: string;

  /**
   * Có đang hiển thị hay không
   */
  isVisible: boolean;

  /**
   * Callback khi thinking box được ẩn
   */
  onHide?: () => void;

  /**
   * Có đang streaming hay không
   */
  isStreaming?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Tắt auto-scroll để tránh xung đột với parent scroll
   */
  disableAutoScroll?: boolean;

  /**
   * Callback khi thinking box xuất hiện và cần parent scroll
   */
  onRequestParentScroll?: () => void;
}

/**
 * Component hiển thị thinking box inline trong chat flow
 */
const InlineThinkingBox: React.FC<InlineThinkingBoxProps> = ({
  content,
  isVisible,
  onHide,
  isStreaming = false,
  className = '',
  disableAutoScroll = false,
  onRequestParentScroll
}) => {
  const { t } = useTranslation();
  const [shouldShow, setShouldShow] = useState(isVisible);
  const [isEntering, setIsEntering] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const scrollAnchorRef = useRef<HTMLDivElement>(null);

  // Handle visibility changes với smooth animation
  useEffect(() => {
    if (isVisible) {
      // Hiện thị: mount component trước, sau đó trigger animation
      setShouldShow(true);
      setIsLeaving(false);

      // Delay nhỏ để đảm bảo DOM đã render
      const enterTimeout = setTimeout(() => {
        setIsEntering(true);

        // Thông báo cho parent scroll xuống để hiển thị thinking box
        if (onRequestParentScroll) {
          // Delay thêm để đảm bảo animation đã bắt đầu
          setTimeout(() => {
            onRequestParentScroll();
          }, 100);
        }
      }, 10);

      return () => clearTimeout(enterTimeout);
    } else {
      // Ẩn đi: trigger animation trước, sau đó unmount
      setIsEntering(false);
      setIsLeaving(true);

      // Đợi animation hoàn thành trước khi unmount
      const leaveTimeout = setTimeout(() => {
        setShouldShow(false);
        setIsLeaving(false);
        onHide?.();
      }, 800); // Tăng thời gian animation lên 800ms để phù hợp với CSS

      return () => clearTimeout(leaveTimeout);
    }
  }, [isVisible, onHide, onRequestParentScroll]);

  // Auto hide khi không streaming và có content
  useEffect(() => {
    if (!isStreaming && content && isVisible && isEntering) {
      const autoHideTimeout = setTimeout(() => {
        // Trigger leave animation
        setIsEntering(false);
        setIsLeaving(true);

        // Unmount sau khi animation hoàn thành
        const hideTimeout = setTimeout(() => {
          setShouldShow(false);
          setIsLeaving(false);
          onHide?.();
        }, 800); // Tăng thời gian animation lên 800ms để phù hợp với CSS

        return () => clearTimeout(hideTimeout);
      }, 4000); // Đợi 4s sau khi stop streaming

      return () => clearTimeout(autoHideTimeout);
    }
    return; // Explicit return for other cases
  }, [isStreaming, content, isVisible, isEntering, onHide]);

  // Auto-scroll khi content thay đổi và đang streaming (chỉ scroll nội bộ thinking box)
  useEffect(() => {
    if (!disableAutoScroll && isStreaming && content && contentRef.current && scrollAnchorRef.current) {
      // Chỉ scroll trong thinking box, không ảnh hưởng ChatContent
      const scrollContainer = contentRef.current;

      // Sử dụng requestAnimationFrame để đảm bảo DOM đã update
      requestAnimationFrame(() => {
        if (scrollContainer && scrollAnchorRef.current) {
          // Chỉ scroll nội bộ thinking box
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      });
    }
  }, [content, isStreaming, disableAutoScroll]);

  // Scroll to bottom khi component được mount và có content (chỉ nội bộ)
  useEffect(() => {
    if (!disableAutoScroll && isEntering && content && contentRef.current) {
      const scrollContainer = contentRef.current;

      // Delay nhỏ để đảm bảo animation đã bắt đầu
      setTimeout(() => {
        if (scrollContainer) {
          // Chỉ scroll nội bộ thinking box
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      }, 150);
    }
  }, [isEntering, content, disableAutoScroll]);

  // Gentle scroll to bottom khi có content mới (tránh xung đột với ChatContent)
  useEffect(() => {
    if (!disableAutoScroll && contentRef.current && content && isStreaming) {
      const scrollContainer = contentRef.current;

      // Chỉ scroll nếu user đang ở gần bottom của thinking box
      const isNearBottom = scrollContainer.scrollHeight - scrollContainer.scrollTop - scrollContainer.clientHeight < 20;

      if (isNearBottom) {
        const timeoutId = setTimeout(() => {
          if (scrollContainer) {
            // Gentle scroll chỉ trong thinking box
            scrollContainer.scrollTop = scrollContainer.scrollHeight;
          }
        }, 50); // Longer delay để tránh xung đột

        return () => clearTimeout(timeoutId);
      }
    }
    return; // Explicit return for other cases
  });

  if (!shouldShow) {
    return null;
  }

  return (
    <div className={`
      thinking-box-wrapper w-full
      ${isEntering && !isLeaving ? 'thinking-box-enter' : ''}
      ${isLeaving ? 'thinking-box-leave' : ''}
      ${className}
    `}>
      {/* Inline Thinking Box */}
      <div className={`
        thinking-box-container
        bg-red-50 dark:bg-red-950/30 rounded-xl border border-red-200 dark:border-red-800/50
        p-4 mx-auto max-w-2xl
      `}>
        {/* Header */}
        <div className={`flex items-center gap-3 mb-3 ${isEntering && !isLeaving ? 'thinking-box-content-enter' : ''} ${isLeaving ? 'thinking-box-content-leave' : ''}`}>
          {/* Thinking Icon */}
          <div className="relative flex-shrink-0">
            <div className={`w-6 h-6 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center ${isStreaming ? 'thinking-box-icon-pulse' : ''}`}>
              <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>

            {/* Pulsing animation */}
            {isStreaming && (
              <div className="absolute inset-0 w-6 h-6 bg-red-400 rounded-full animate-ping opacity-20" />
            )}
          </div>

          {/* Title */}
          <div className="flex-1">
            <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
              {t('chat:thinking.title', 'Đang suy nghĩ...')}
            </h4>
            <p className="text-xs text-red-600 dark:text-red-400">
              {isStreaming
                ? t('chat:thinking.processing', 'Đang xử lý yêu cầu của bạn')
                : t('chat:thinking.completed', 'Hoàn thành')
              }
            </p>
          </div>

          {/* Close button (chỉ hiển thị khi không streaming) */}
          {!isStreaming && (
            <button
              onClick={() => {
                // Trigger leave animation
                setIsEntering(false);
                setIsLeaving(true);

                // Unmount sau khi animation hoàn thành
                setTimeout(() => {
                  setShouldShow(false);
                  setIsLeaving(false);
                  onHide?.();
                }, 800);
              }}
              className="flex-shrink-0 p-1 text-red-400 hover:text-red-600 dark:hover:text-red-300 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Content */}
        <div
          ref={contentRef}
          className={`bg-red-100/50 dark:bg-red-900/30 rounded-lg p-3 min-h-[60px] max-h-[200px] overflow-y-auto thinking-box-scrollbar ${isEntering && !isLeaving ? 'thinking-box-content-enter' : ''} ${isLeaving ? 'thinking-box-content-leave' : ''}`}
          style={{
            scrollBehavior: 'smooth',
            animationDelay: isLeaving ? '0s' : '0.1s'
          }}
        >
          {content ? (
            <>
              <div className="text-sm text-red-800 dark:text-red-200 whitespace-pre-wrap leading-relaxed">
                {content}
                {isStreaming && (
                  <span className="inline-block w-1.5 h-3 bg-red-500 ml-1 animate-pulse" />
                )}
              </div>
              {/* Scroll anchor - invisible element để scroll target */}
              <div ref={scrollAnchorRef} className="h-0 w-0" />
            </>
          ) : (
            <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
              <div className="flex gap-1">
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
              <span className="text-xs">{t('chat:thinking.waiting', 'Đang chờ phản hồi...')}</span>
            </div>
          )}
        </div>

        {/* Progress bar */}
        {isStreaming && (
          <div className={`mt-3 ${isEntering && !isLeaving ? 'thinking-box-content-enter' : ''} ${isLeaving ? 'thinking-box-content-leave' : ''}`} style={{ animationDelay: isLeaving ? '0s' : '0.2s' }}>
            <div className="w-full bg-red-200 dark:bg-red-800 rounded-full h-1 thinking-box-progress">
              <div className="bg-red-500 h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InlineThinkingBox;
