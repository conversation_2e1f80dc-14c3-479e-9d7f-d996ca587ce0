import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/shared/components/common';
import FileSelectionModal from './FileSelectionModal';

interface UploadMenuProps {
  onUploadFromComputer: (files: FileList) => void;
  onUploadFromGoogleDrive: () => void;
  onSelectFromLibrary?: (files: Array<{ id: string; name: string; type: 'knowledge' | 'media'; url?: string }>) => void;
  isOpen: boolean;
  onClose: () => void;
}

const UploadMenu: React.FC<UploadMenuProps> = ({
  onUploadFromComputer,
  onUploadFromGoogleDrive,
  onSelectFromLibrary,
  isOpen,
  onClose,
}) => {
  const { t } = useTranslation();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [processingFile, setProcessingFile] = useState(false);
  const [showFileSelection, setShowFileSelection] = useState(false);

  if (!isOpen) return null;

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      console.log('Files selected:', e.target.files.length);

      // Set processing state to prevent menu from closing
      setProcessingFile(true);

      // Process files
      onUploadFromComputer(e.target.files);

      // Reset the file input after processing
      setTimeout(() => {
        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }

        // Reset processing state
        setProcessingFile(false);

        // Đóng menu sau khi xử lý file
        onClose();
      }, 100);
    }
  };

  const handleClickOutside = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Don't close if we're processing a file
    if (!processingFile) {
      onClose();
    }
  };

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Xử lý chọn file từ thư viện
  const handleSelectFromLibrary = (files: Array<{ id: string; name: string; type: 'knowledge' | 'media'; url?: string }>) => {
    if (onSelectFromLibrary) {
      onSelectFromLibrary(files);
    }
    onClose();
  };

  return (
    <>
      <div className="fixed inset-0 z-40" onClick={handleClickOutside} />
      <div
        className="absolute bottom-full left-0 mb-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-50 overflow-hidden animate-fade-in"
        style={{ width: '250px' }}
        onClick={handleMenuClick}
      >
        <div className="p-1">
          <button
            className="flex items-center w-full px-3 py-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
            onClick={() => {
              onUploadFromGoogleDrive();
              onClose();
            }}
          >
            <div className="mr-2 flex-shrink-0">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 shrink-0"
              >
                <path
                  d="M3.51117 18.4269L4.39319 19.9615C4.57646 20.2846 4.83992 20.5384 5.1492 20.723L8.29926 15.2307H1.99915C1.99915 15.5884 2.09078 15.9461 2.27406 16.2692L3.51117 18.4269Z"
                  fill="#0066DA"
                ></path>
                <path
                  d="M11.9991 8.76916L8.84909 3.27686C8.53981 3.46147 8.27635 3.71532 8.09307 4.03839L2.27406 14.1922C2.09415 14.5083 1.99939 14.8663 1.99915 15.2307H8.29926L11.9991 8.76916Z"
                  fill="#00AC47"
                ></path>
                <path
                  d="M18.8492 20.723C19.1585 20.5384 19.422 20.2846 19.6053 19.9615L19.9718 19.3269L21.7244 16.2692C21.9077 15.9461 21.9993 15.5884 21.9993 15.2307H15.6987L17.0394 17.8846L18.8492 20.723Z"
                  fill="#EA4335"
                ></path>
                <path
                  d="M11.9981 8.76923L15.1482 3.27692C14.8389 3.09231 14.4838 3 14.1173 3H9.87901C9.51246 3 9.15736 3.10385 8.84808 3.27692L11.9981 8.76923Z"
                  fill="#00832D"
                ></path>
                <path
                  d="M15.6984 15.2307H8.29862L5.14856 20.723C5.45784 20.9076 5.81294 20.9999 6.17949 20.9999H17.8175C18.1841 20.9999 18.5392 20.8961 18.8484 20.723L15.6984 15.2307Z"
                  fill="#2684FC"
                ></path>
                <path
                  d="M18.8148 9.11532L15.9053 4.03839C15.722 3.71532 15.4585 3.46147 15.1493 3.27686L11.9992 8.76916L15.6991 15.2307H21.9878C21.9878 14.873 21.8961 14.5153 21.7128 14.1922L18.8148 9.11532Z"
                  fill="#FFBA00"
                ></path>
              </svg>
            </div>
            <span className="whitespace-nowrap">{t('chat.uploadFromGoogleDrive')}</span>
          </button>

          <button
            className="flex items-center w-full px-3 py-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
            onClick={() => {
              // Mở hộp thoại chọn file ngay lập tức
              fileInputRef.current?.click();
            }}
          >
            <div className="mr-2 text-gray-600 dark:text-gray-300">
              <Icon name="upload" size="sm" />
            </div>
            <span className="whitespace-nowrap">{t('chat.uploadFromComputer')}</span>
          </button>

          {/* Button chọn từ kho của bạn */}
          {onSelectFromLibrary && (
            <button
              className="flex items-center w-full px-3 py-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              onClick={() => {
                setShowFileSelection(true);
              }}
            >
              <div className="mr-2 text-gray-600 dark:text-gray-300">
                <Icon name="folder" size="sm" />
              </div>
              <span className="whitespace-nowrap">Chọn từ kho của bạn</span>
            </button>
          )}
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          multiple
          accept="image/*,.pdf,.xlsx,.csv,.docx,.json,.md,.jsonl"
        />
      </div>
      <FileSelectionModal
        isOpen={showFileSelection}
        onClose={() => setShowFileSelection(false)}
        onSelectFiles={handleSelectFromLibrary}
      />
    </>
  );
};

export default UploadMenu;
