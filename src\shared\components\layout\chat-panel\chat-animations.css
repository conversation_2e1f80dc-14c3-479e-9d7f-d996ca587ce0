/* Chat Panel Animations */

/* Fade in up animation for messages */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Slide in from right for user messages */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide in from left for AI messages */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Bounce in animation for new messages */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Typing indicator animation */
@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Pulse animation for streaming messages */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Animation classes */
.animate-fadeInUp {
  animation: fadeInUp 0.4s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.3s ease-out;
}

.animate-bounceIn {
  animation: bounceIn 0.6s ease-out;
}

.animate-typing {
  animation: typing 1.4s infinite ease-in-out;
}

.animate-pulse-soft {
  animation: pulse 2s infinite ease-in-out;
}

/* Smooth transitions */
.transition-message {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects */
.message-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Dark mode adjustments */
.dark .message-hover:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Highlight message animation */
@keyframes highlightMessage {
  0% {
    background-color: rgba(239, 68, 68, 0.1);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
  }
  50% {
    background-color: rgba(239, 68, 68, 0.2);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
  }
  100% {
    background-color: transparent;
    box-shadow: none;
  }
}

.highlight-message {
  animation: highlightMessage 2s ease-out;
  border-radius: 8px;
}

/* Dark mode highlight */
.dark .highlight-message {
  animation: highlightMessageDark 2s ease-out;
}

@keyframes highlightMessageDark {
  0% {
    background-color: rgba(239, 68, 68, 0.2);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.4);
  }
  50% {
    background-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.3);
  }
  100% {
    background-color: transparent;
    box-shadow: none;
  }
}

/* Custom scrollbar for chat input */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.6);
}

/* Dark mode scrollbar */
.dark .custom-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.4) transparent;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.4);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}
