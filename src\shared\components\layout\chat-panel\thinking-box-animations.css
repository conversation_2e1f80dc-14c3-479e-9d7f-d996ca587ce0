/* InlineThinkingBox Animation Styles */

/* Keyframes cho enter animation */
@keyframes thinkingBoxEnter {
  0% {
    opacity: 0;
    transform: translateY(16px) scale(0.9);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-2px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Keyframes cho leave animation - m<PERSON><PERSON><PERSON> m<PERSON>, tr<PERSON>h nh<PERSON>y màn */
@keyframes thinkingBoxLeave {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
    visibility: visible;
  }
  20% {
    opacity: 0.9;
    transform: translateY(1px) scale(0.995);
    visibility: visible;
  }
  40% {
    opacity: 0.7;
    transform: translateY(3px) scale(0.985);
    visibility: visible;
  }
  60% {
    opacity: 0.5;
    transform: translateY(6px) scale(0.97);
    visibility: visible;
  }
  80% {
    opacity: 0.3;
    transform: translateY(10px) scale(0.95);
    visibility: visible;
  }
  95% {
    opacity: 0.05;
    transform: translateY(14px) scale(0.92);
    visibility: visible;
  }
  100% {
    opacity: 0;
    transform: translateY(16px) scale(0.9);
    visibility: hidden;
  }
}

/* Keyframes cho content fade in */
@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Keyframes cho content fade out - tránh nháy màn */
@keyframes contentFadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
  30% {
    opacity: 0.8;
    transform: translateY(-1px);
    visibility: visible;
  }
  60% {
    opacity: 0.5;
    transform: translateY(-2px);
    visibility: visible;
  }
  90% {
    opacity: 0.1;
    transform: translateY(-3px);
    visibility: visible;
  }
  100% {
    opacity: 0;
    transform: translateY(-4px);
    visibility: hidden;
  }
}

/* Keyframes cho icon pulse */
@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Keyframes cho progress bar */
@keyframes progressFlow {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Animation classes */
.thinking-box-enter {
  animation: thinkingBoxEnter 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  /* Đảm bảo không có flicker */
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

.thinking-box-leave {
  animation: thinkingBoxLeave 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
  /* Đảm bảo không có flicker */
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

.thinking-box-content-enter {
  animation: contentFadeIn 0.3s ease-out 0.1s both;
}

.thinking-box-content-leave {
  animation: contentFadeOut 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.thinking-box-icon-pulse {
  animation: iconPulse 2s ease-in-out infinite;
}

.thinking-box-progress {
  position: relative;
  overflow: hidden;
}

.thinking-box-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(239, 68, 68, 0.3),
    transparent
  );
  animation: progressFlow 2s ease-in-out infinite;
}

/* Container base styles - tránh layout shift */
.thinking-box-container {
  transition: all 0.2s ease-out;
  /* Tránh flicker và layout shift */
  backface-visibility: hidden;
  transform-style: preserve-3d;
  will-change: transform;
  /* Đảm bảo container stable */
  contain: layout style paint;
}

/* Hover effects */
.thinking-box-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.15);
}

/* Responsive animations */
@media (prefers-reduced-motion: reduce) {
  .thinking-box-enter,
  .thinking-box-leave,
  .thinking-box-content-enter,
  .thinking-box-icon-pulse,
  .thinking-box-progress::after {
    animation: none;
  }
  
  .thinking-box-container:hover {
    transform: none;
  }
}

/* Wrapper để tránh nháy màn */
.thinking-box-wrapper {
  /* Đảm bảo smooth transition */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  /* Tránh layout shift */
  contain: layout style paint;
  /* Smooth rendering */
  will-change: auto;
}

/* Dark mode adjustments */
.dark .thinking-box-progress::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(248, 113, 113, 0.3),
    transparent
  );
}

.dark .thinking-box-container:hover {
  box-shadow: 0 8px 25px rgba(248, 113, 113, 0.1);
}
