/**
 * ThreadIntegrationContext
 * Context để share thread state gi<PERSON><PERSON> và ThreadsPage
 */

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface ThreadIntegrationState {
  currentThreadId: string | null;
  currentThreadName: string | null;
  isThreadSwitching: boolean;
}

interface ThreadIntegrationContextType {
  // State
  currentThreadId: string | null;
  currentThreadName: string | null;
  isThreadSwitching: boolean;

  // Actions
  setCurrentThread: (threadId: string | null, threadName?: string | null) => void;
  updateThreadName: (threadId: string, newName: string) => void;
  setThreadSwitching: (switching: boolean) => void;
  clearCurrentThread: () => void;

  // Events
  onThreadCreated: ((threadId: string, threadName: string) => void) | undefined;
  onThreadSwitched: ((fromId: string, toId: string) => void) | undefined;
  onThreadNameChanged: ((threadId: string, newName: string) => void) | undefined;
  onThreadDeleted: ((threadId: string) => void) | undefined;

  // Event setters
  setOnThreadCreated: (callback: (threadId: string, threadName: string) => void) => void;
  setOnThreadSwitched: (callback: (fromId: string, toId: string) => void) => void;
  setOnThreadNameChanged: (callback: (threadId: string, newName: string) => void) => void;
  setOnThreadDeleted: (callback: (threadId: string) => void) => void;
}

const ThreadIntegrationContext = createContext<ThreadIntegrationContextType | undefined>(undefined);

interface ThreadIntegrationProviderProps {
  children: ReactNode;
}

export const ThreadIntegrationProvider: React.FC<ThreadIntegrationProviderProps> = ({ children }) => {
  const [state, setState] = useState<ThreadIntegrationState>({
    currentThreadId: null,
    currentThreadName: null,
    isThreadSwitching: false
  });

  // Event callbacks
  const [onThreadCreated, setOnThreadCreated] = useState<((threadId: string, threadName: string) => void) | undefined>();
  const [onThreadSwitched, setOnThreadSwitched] = useState<((fromId: string, toId: string) => void) | undefined>();
  const [onThreadNameChanged, setOnThreadNameChanged] = useState<((threadId: string, newName: string) => void) | undefined>();
  const [onThreadDeleted, setOnThreadDeleted] = useState<((threadId: string) => void) | undefined>();

  // Actions
  const setCurrentThread = useCallback((threadId: string | null, threadName?: string | null) => {
    console.log('[ThreadIntegrationContext] Setting current thread:', { threadId, threadName });
    setState(prev => ({
      ...prev,
      currentThreadId: threadId,
      currentThreadName: threadName || null,
      isThreadSwitching: false
    }));
  }, []);

  const updateThreadName = useCallback((threadId: string, newName: string) => {
    console.log('[ThreadIntegrationContext] Updating thread name:', { threadId, newName });
    setState(prev => {
      if (prev.currentThreadId === threadId) {
        return {
          ...prev,
          currentThreadName: newName
        };
      }
      return prev;
    });

    // Trigger callback
    onThreadNameChanged?.(threadId, newName);
  }, [onThreadNameChanged]);

  const setThreadSwitching = useCallback((switching: boolean) => {
    console.log('[ThreadIntegrationContext] Setting thread switching:', switching);
    setState(prev => ({
      ...prev,
      isThreadSwitching: switching
    }));
  }, []);

  const clearCurrentThread = useCallback(() => {
    console.log('[ThreadIntegrationContext] Clearing current thread');
    setState(prev => ({
      ...prev,
      currentThreadId: null,
      currentThreadName: null,
      isThreadSwitching: false
    }));
  }, []);

  // Event setter wrappers
  const setOnThreadCreatedWrapper = useCallback((callback: (threadId: string, threadName: string) => void) => {
    setOnThreadCreated(() => callback);
  }, []);

  const setOnThreadSwitchedWrapper = useCallback((callback: (fromId: string, toId: string) => void) => {
    setOnThreadSwitched(() => callback);
  }, []);

  const setOnThreadNameChangedWrapper = useCallback((callback: (threadId: string, newName: string) => void) => {
    setOnThreadNameChanged(() => callback);
  }, []);

  const setOnThreadDeletedWrapper = useCallback((callback: (threadId: string) => void) => {
    setOnThreadDeleted(() => callback);
  }, []);

  const value: ThreadIntegrationContextType = {
    // State
    currentThreadId: state.currentThreadId,
    currentThreadName: state.currentThreadName,
    isThreadSwitching: state.isThreadSwitching,

    // Actions
    setCurrentThread,
    updateThreadName,
    setThreadSwitching,
    clearCurrentThread,

    // Events
    onThreadCreated,
    onThreadSwitched,
    onThreadNameChanged,
    onThreadDeleted,

    // Event setters
    setOnThreadCreated: setOnThreadCreatedWrapper,
    setOnThreadSwitched: setOnThreadSwitchedWrapper,
    setOnThreadNameChanged: setOnThreadNameChangedWrapper,
    setOnThreadDeleted: setOnThreadDeletedWrapper
  };

  return (
    <ThreadIntegrationContext.Provider value={value}>
      {children}
    </ThreadIntegrationContext.Provider>
  );
};

export const useThreadIntegration = (): ThreadIntegrationContextType => {
  const context = useContext(ThreadIntegrationContext);
  if (context === undefined) {
    throw new Error('useThreadIntegration must be used within a ThreadIntegrationProvider');
  }
  return context;
};
