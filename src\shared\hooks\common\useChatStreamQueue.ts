/**
 * useChatStreamQueue Hook
 * Enhanced chat streaming hook với Queue-Based Architecture
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { flushSync } from 'react-dom';
import { ChatApiService } from '@/shared/services/chat-api.service';
import { ChatSSEService } from '@/shared/services/chat-sse.service';
import { StreamingController } from '@/shared/services/streaming-controller.service';
import { CharacterStreamItem } from '@/shared/services/character-stream-processor.service';
import { TokenQueueItem } from '@/shared/services/token-queue.service';
import { chatConfigService } from '@/shared/services/chat-config.service';
import {
  ChatMessage,
  MessageStatus,
  SendMessageResponse,
  mapRoleToMessageSender,
} from '@/shared/types/chat-streaming.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Configuration cho useChatStreamQueue hook
 */
export interface UseChatStreamQueueConfig {
  agentId?: string;
  apiBaseUrl?: string;
  sseBaseUrl?: string;
  alwaysApproveToolCall?: boolean;
  getAuthToken: () => string;
  debug?: boolean;

  /**
   * Queue-based streaming configuration
   */
  queueConfig?: {
    enabled?: boolean;
    maxQueueSize?: number;
    streamingSpeed?: number;
    enableAdaptiveSpeed?: boolean;
    enableMetrics?: boolean;
  };

  /**
   * Configuration cho message history
   */
  messageHistory?: {
    pageSize?: number;
    autoLoad?: boolean;
    timeout?: number;
  };
}

/**
 * Streaming metrics
 */
export interface StreamingMetrics {
  tokensQueued: number;
  charactersStreamed: number;
  currentSpeed: number;
  queueSize: number;
  averageLatency: number;
  throughput: number;
}

/**
 * Return type cho useChatStreamQueue hook
 */
export interface UseChatStreamQueueReturn {
  // State
  messages: ChatMessage[];
  isStreaming: boolean;
  isLoading: boolean;
  currentStreamingText: string;
  currentRunId: string | null;
  threadId: string | null;
  threadName: string | null;
  isCreatingThread: boolean;
  isLoadingThreads: boolean;

  // Queue-specific state
  queueMode: boolean;
  streamingMetrics: StreamingMetrics;

  // Message History
  historyMessages: ChatMessage[];
  isLoadingHistory: boolean;
  isLoadingMoreHistory: boolean;
  hasMoreHistory: boolean;
  historyError: string | null;
  totalHistoryItems: number;

  // Actions
  sendMessage: (content: string) => Promise<void>;
  stopStreaming: () => Promise<void>;
  clearMessages: () => void;
  createNewThread: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  loadLatestThread: () => Promise<void>;
  retryLastMessage: () => Promise<void>;

  // Queue-specific actions
  setQueueMode: (enabled: boolean) => void;
  setStreamingSpeed: (speed: number) => void;
  getStreamingController: () => StreamingController | null;

  // Message History Actions
  loadMoreHistory: () => Promise<void>;
  refreshHistory: () => Promise<void>;

  // Status
  isConnected: boolean;
  error: string | null;
  streamError: {
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null;
}

/**
 * useChatStreamQueue Hook
 */
export function useChatStreamQueue(config: UseChatStreamQueueConfig): UseChatStreamQueueReturn {
  // State
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStreamingText, setCurrentStreamingText] = useState('');
  const [currentRunId, setCurrentRunId] = useState<string | null>(null);
  const [threadId] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Queue-specific state
  const [queueMode, setQueueModeState] = useState(config.queueConfig?.enabled ?? true);
  const [streamingMetrics, setStreamingMetrics] = useState<StreamingMetrics>({
    tokensQueued: 0,
    charactersStreamed: 0,
    currentSpeed: config.queueConfig?.streamingSpeed ?? 50,
    queueSize: 0,
    averageLatency: 0,
    throughput: 0
  });

  // Stream error state
  const [streamError, setStreamError] = useState<{
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null>(null);

  // Message History State
  const [historyMessages] = useState<ChatMessage[]>([]);
  const [isLoadingHistory] = useState(false);
  const [isLoadingMoreHistory] = useState(false);
  const [hasMoreHistory] = useState(true);
  const [historyError] = useState<string | null>(null);
  const [totalHistoryItems] = useState(0);

  // Services refs
  const apiServiceRef = useRef<ChatApiService | null>(null);
  const sseServiceRef = useRef<ChatSSEService | null>(null);
  const streamingControllerRef = useRef<StreamingController | null>(null);
  const currentStreamingMessageRef = useRef<ChatMessage | null>(null);
  const lastMessageContentRef = useRef<string>(''); // Lưu content để retry

  /**
   * Initialize services với queue support
   */
  useEffect(() => {
    const chatConfig = chatConfigService.getConfig();
    const finalConfig = {
      agentId: config.agentId || chatConfig.agentId,
      apiBaseUrl: config.apiBaseUrl || chatConfig.apiBaseUrl,
      sseBaseUrl: config.sseBaseUrl || chatConfig.sseBaseUrl,
      alwaysApproveToolCall: config.alwaysApproveToolCall ?? chatConfig.alwaysApproveToolCall,
      debug: config.debug ?? chatConfig.debug
    };

    // Initialize API service
    apiServiceRef.current = new ChatApiService(
      finalConfig.apiBaseUrl,
      30000, // timeout
      finalConfig.debug
    );

    // Initialize SSE service với queue support
    sseServiceRef.current = new ChatSSEService(
      finalConfig.sseBaseUrl,
      finalConfig.debug
    );

    // Set queue mode
    sseServiceRef.current.setQueueMode(queueMode);

    // Get streaming controller
    streamingControllerRef.current = sseServiceRef.current.getStreamingController();

    // Setup streaming controller callbacks
    if (streamingControllerRef.current) {
      streamingControllerRef.current.setCallbacks({
        onCharacterStreamed: handleCharacterStreamed,
        onTokenProcessed: handleTokenProcessed,
        onQueueOverflow: handleQueueOverflow,
        onStateChanged: handleStreamingStateChanged,
        onError: (error, context) => {
          console.error('[useChatStreamQueue] StreamingController error:', error, context);
          setError(error.message);
        }
      });

      // Subscribe to character stream
      const characterStreamSubscription = streamingControllerRef.current
        .getCharacterStream()
        .subscribe({
          next: handleCharacterStreamed,
          error: (error) => {
            console.error('[useChatStreamQueue] Character stream error:', error);
            setError(error.message);
          }
        });

      // Cleanup subscription
      return () => {
        characterStreamSubscription.unsubscribe();
      };
    }

    // Setup SSE callbacks
    sseServiceRef.current.setCallbacks({
      onConnected: (data) => {
        console.log('[useChatStreamQueue] SSE Connected:', data);
        setIsConnected(true);
        setError(null);
      },

      onTokenQueued: (token: TokenQueueItem) => {
        console.log('[useChatStreamQueue] Token queued:', token);
        setStreamingMetrics(prev => ({
          ...prev,
          tokensQueued: prev.tokensQueued + 1
        }));
      },

      onTextToken: (text: string, role: string) => {
        // Fallback cho direct streaming mode
        if (!queueMode) {
          handleDirectTextToken(text, role);
        }
      },

      onStreamEnd: (runId: string) => {
        console.log('[useChatStreamQueue] Stream ended:', runId);
        handleStreamEnd(runId);
      },

      onStreamError: (errorMessage: string, errorDetails?: unknown) => {
        console.error('[useChatStreamQueue] Stream error:', errorMessage, errorDetails);
        handleStreamError(errorMessage, errorDetails);
      },

      onError: (error: Error) => {
        console.error('[useChatStreamQueue] SSE error:', error);
        setError(error.message);
        setIsStreaming(false);
        setIsLoading(false);
        setIsConnected(false);
      },

      onClose: () => {
        console.log('[useChatStreamQueue] SSE closed');
        setIsConnected(false);
      },

      onQueueOverflow: handleQueueOverflow,

      onStreamingModeChanged: (useQueue: boolean) => {
        console.log('[useChatStreamQueue] Streaming mode changed:', useQueue);
        setQueueModeState(useQueue);
      }
    });

    return () => {
      // Cleanup
      if (streamingControllerRef.current) {
        streamingControllerRef.current.destroy();
      }
      sseServiceRef.current?.destroy();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config, queueMode]);

  /**
   * Handle character streamed từ queue
   */
  const handleCharacterStreamed = useCallback((item: CharacterStreamItem) => {
    console.log('[useChatStreamQueue] Character streamed:', item);

    // Update metrics
    setStreamingMetrics(prev => ({
      ...prev,
      charactersStreamed: prev.charactersStreamed + 1
    }));

    // Update current streaming message
    if (currentStreamingMessageRef.current && 
        currentStreamingMessageRef.current.id === item.messageId) {
      
      // Update message content
      const updatedContent = typeof currentStreamingMessageRef.current.content === 'string'
        ? currentStreamingMessageRef.current.content + item.character
        : item.character;

      currentStreamingMessageRef.current.content = updatedContent;

      // Update UI
      setMessages(prev => prev.map(msg =>
        msg.id === item.messageId
          ? { ...msg, content: updatedContent }
          : msg
      ));

      setCurrentStreamingText(updatedContent);
    }
  }, []);

  /**
   * Handle token processed
   */
  const handleTokenProcessed = useCallback((token: TokenQueueItem) => {
    console.log('[useChatStreamQueue] Token processed:', token);
  }, []);

  /**
   * Handle queue overflow
   */
  const handleQueueOverflow = useCallback((droppedTokens: TokenQueueItem[]) => {
    console.warn('[useChatStreamQueue] Queue overflow, dropped tokens:', droppedTokens.length);
    setError(`Queue overflow: ${droppedTokens.length} tokens dropped`);
  }, []);

  /**
   * Handle streaming state changed
   */
  const handleStreamingStateChanged = useCallback((state: {
    queueSize: number;
    currentSpeed: number;
    metrics: {
      averageLatency: number;
      throughput: number;
    };
  }) => {
    setStreamingMetrics(prev => ({
      ...prev,
      queueSize: state.queueSize,
      currentSpeed: state.currentSpeed,
      averageLatency: state.metrics.averageLatency,
      throughput: state.metrics.throughput
    }));
  }, []);

  /**
   * Handle direct text token (fallback mode)
   */
  const handleDirectTextToken = useCallback((text: string, role: string) => {
    console.log('[useChatStreamQueue] Direct text token:', { text, role });

    const mappedSender = mapRoleToMessageSender(role);

    if (!isStreaming) {
      setIsStreaming(true);
    }

    // Create or update streaming message
    if (!currentStreamingMessageRef.current) {
      const newMessage: ChatMessage = {
        id: uuidv4(),
        content: text,
        sender: mappedSender,
        timestamp: new Date(),
        status: MessageStatus.STREAMING,
        threadId: threadId!
      };

      flushSync(() => {
        setMessages(prev => [...prev, newMessage]);
        setCurrentStreamingText(text);
      });

      currentStreamingMessageRef.current = newMessage;
    } else if (currentStreamingMessageRef.current.sender !== mappedSender) {
      // Role changed - finalize current and create new
      const finalizedMessage = {
        ...currentStreamingMessageRef.current,
        status: MessageStatus.COMPLETED
      };

      const newMessage: ChatMessage = {
        id: uuidv4(),
        content: text,
        sender: mappedSender,
        timestamp: new Date(),
        status: MessageStatus.STREAMING,
        threadId: threadId!
      };

      flushSync(() => {
        setMessages(prev => [
          ...prev.map(msg => msg.id === currentStreamingMessageRef.current?.id ? finalizedMessage : msg),
          newMessage
        ]);
        setCurrentStreamingText(text);
      });

      currentStreamingMessageRef.current = newMessage;
    } else {
      // Append to current message
      currentStreamingMessageRef.current.content += text;

      setMessages(prev => prev.map(msg =>
        msg.id === currentStreamingMessageRef.current?.id
          ? { ...msg, content: currentStreamingMessageRef.current!.content }
          : msg
      ));

      setCurrentStreamingText(
        typeof currentStreamingMessageRef.current.content === 'string'
          ? currentStreamingMessageRef.current.content
          : ''
      );
    }
  }, [isStreaming, threadId]);

  /**
   * Handle stream end
   */
  const handleStreamEnd = useCallback((runId: string) => {
    console.log('[useChatStreamQueue] Stream ended:', runId);

    setStreamError(null);

    // Force complete any remaining streaming
    if (streamingControllerRef.current) {
      streamingControllerRef.current.forceCompleteStreaming();
    }

    if (currentStreamingMessageRef.current) {
      const finalMessage: ChatMessage = {
        ...currentStreamingMessageRef.current,
        status: MessageStatus.COMPLETED,
        timestamp: new Date(),
        runId: runId,
        metadata: {
          ...currentStreamingMessageRef.current.metadata,
          processingTime: Date.now() - currentStreamingMessageRef.current.timestamp.getTime()
        }
      };

      setMessages(prev => prev.map(msg =>
        msg.id === currentStreamingMessageRef.current?.id
          ? finalMessage
          : msg
      ));
    }

    // Reset streaming state với delay để cho phép streaming hoàn thành
    setTimeout(() => {
      setIsStreaming(false);
      setIsLoading(false);
      setCurrentStreamingText('');
      setCurrentRunId(null);
      setIsConnected(false);
      currentStreamingMessageRef.current = null;

      // Stop streaming controller sau khi hoàn thành
      if (streamingControllerRef.current) {
        streamingControllerRef.current.stopAllStreaming();
      }
    }, 1000); // Chờ 1 giây để streaming hoàn thành
  }, []);

  /**
   * Handle stream error
   */
  const handleStreamError = useCallback((errorMessage: string, errorDetails?: unknown) => {
    console.error('[useChatStreamQueue] Stream error:', errorMessage, errorDetails);

    const errorState = {
      message: errorMessage,
      details: errorDetails,
      retryContent: lastMessageContentRef.current
    };

    setStreamError(errorState);
    setIsStreaming(false);
    setIsLoading(false);
    setIsConnected(false);
    currentStreamingMessageRef.current = null;

    // Stop streaming controller
    if (streamingControllerRef.current) {
      streamingControllerRef.current.stopAllStreaming();
    }
  }, []);

  /**
   * Send message với queue support
   */
  const sendMessage = useCallback(async (content: string): Promise<void> => {
    if (!apiServiceRef.current || !sseServiceRef.current || !threadId) {
      throw new Error('Services not initialized or no thread ID');
    }

    try {
      setIsLoading(true);
      setError(null);
      setStreamError(null);
      lastMessageContentRef.current = content;

      // Create user message
      const userMessage: ChatMessage = {
        id: uuidv4(),
        content,
        sender: 'user',
        timestamp: new Date(),
        status: MessageStatus.COMPLETED,
        threadId
      };

      setMessages(prev => [...prev, userMessage]);

      // Prepare thread
      try {
        await apiServiceRef.current.createThread(threadId);
      } catch (error) {
        console.warn('[useChatStreamQueue] Thread preparation failed, continuing...', error);
      }

      // Send message
      const response: SendMessageResponse = await apiServiceRef.current.sendMessage(
        content,
        threadId,
        config.alwaysApproveToolCall ?? false
      );

      setCurrentRunId(response.runId);

      // Set current message ID for streaming
      const messageId = uuidv4();
      sseServiceRef.current.setCurrentMessageId(messageId);

      // Start streaming
      if (queueMode && streamingControllerRef.current) {
        streamingControllerRef.current.startStreaming(messageId);
      }

      // Connect SSE
      await sseServiceRef.current.connect(threadId, response.runId);

      console.log('[useChatStreamQueue] Message sent and streaming started');
    } catch (error) {
      console.error('[useChatStreamQueue] Send message failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to send message');
      setIsLoading(false);
      setIsStreaming(false);
    }
  }, [threadId, config.alwaysApproveToolCall, queueMode]);

  /**
   * Stop streaming
   */
  const stopStreaming = useCallback(async (): Promise<void> => {
    console.log('[useChatStreamQueue] Stopping streaming...');

    if (streamingControllerRef.current) {
      streamingControllerRef.current.stopAllStreaming();
    }

    sseServiceRef.current?.disconnect();

    setIsStreaming(false);
    setIsLoading(false);
    setIsConnected(false);
    currentStreamingMessageRef.current = null;
  }, []);

  /**
   * Clear messages
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentStreamingText('');
    setStreamError(null);
    setError(null);
    currentStreamingMessageRef.current = null;

    if (streamingControllerRef.current) {
      streamingControllerRef.current.stopAllStreaming();
    }
  }, []);

  return {
    // State
    messages,
    isStreaming,
    isLoading,
    currentStreamingText,
    currentRunId,
    threadId,
    threadName: null,
    isCreatingThread: false,
    isLoadingThreads: false,

    // Queue-specific state
    queueMode,
    streamingMetrics,

    // Message History
    historyMessages,
    isLoadingHistory,
    isLoadingMoreHistory,
    hasMoreHistory,
    historyError,
    totalHistoryItems,

    // Actions
    sendMessage,
    stopStreaming,
    clearMessages,
    createNewThread: async (name?: string) => {
      // Placeholder - implement thread creation
      console.log('[useChatStreamQueue] Create new thread:', name);
      // Return mock data for now
      return {
        threadId: 'mock-thread-id',
        threadName: name || 'New Thread'
      };
    },
    loadLatestThread: async () => {
      // Placeholder - implement thread loading
      console.log('[useChatStreamQueue] Load latest thread');
    },
    retryLastMessage: async () => {
      if (lastMessageContentRef.current) {
        await sendMessage(lastMessageContentRef.current);
      }
    },

    // Queue-specific actions
    setQueueMode: (enabled: boolean) => {
      setQueueModeState(enabled);
      sseServiceRef.current?.setQueueMode(enabled);
    },
    setStreamingSpeed: (speed: number) => {
      streamingControllerRef.current?.setStreamingSpeed(speed);
      setStreamingMetrics(prev => ({ ...prev, currentSpeed: speed }));
    },
    getStreamingController: () => streamingControllerRef.current,

    // Message History Actions
    loadMoreHistory: async () => {
      if (hasMoreHistory && !isLoadingMoreHistory) {
        // Placeholder - implement history loading
        console.log('[useChatStreamQueue] Load more history');
      }
    },
    refreshHistory: async () => {
      // Placeholder - implement history refresh
      console.log('[useChatStreamQueue] Refresh history');
    },

    // Status
    isConnected,
    error,
    streamError
  };
}

export default useChatStreamQueue;
