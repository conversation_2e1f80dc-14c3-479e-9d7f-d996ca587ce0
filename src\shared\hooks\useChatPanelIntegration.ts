/**
 * Hook để tích hợp ChatPanel với external modules
 * Cung cấp access đến chatStream và các functions của ChatPanel
 */

import { useRef, useCallback } from 'react';
import { UseChatStreamReturn } from './common/useChatStream';

interface ChatPanelIntegration {
  chatStream: UseChatStreamReturn | null;
  focusChatInput: (() => void) | null;
}

interface UseChatPanelIntegrationReturn {
  /**
   * Register ChatPanel's chatStream và focus function
   */
  registerChatPanel: (integration: ChatPanelIntegration) => void;
  
  /**
   * Get current chatStream instance
   */
  getChatStream: () => UseChatStreamReturn | null;
  
  /**
   * Get focus chat input function
   */
  getFocusChatInput: () => (() => void) | null;
  
  /**
   * Check if ChatPanel is registered
   */
  isRegistered: () => boolean;
}

/**
 * Global singleton để quản lý ChatPanel integration
 */
class ChatPanelIntegrationManager {
  private static instance: ChatPanelIntegrationManager;
  private chatPanelIntegration: ChatPanelIntegration = {
    chatStream: null,
    focusChatInput: null
  };

  static getInstance(): ChatPanelIntegrationManager {
    if (!ChatPanelIntegrationManager.instance) {
      ChatPanelIntegrationManager.instance = new ChatPanelIntegrationManager();
    }
    return ChatPanelIntegrationManager.instance;
  }

  register(integration: ChatPanelIntegration): void {
    console.log('[ChatPanelIntegrationManager] Registering ChatPanel integration:', integration);
    this.chatPanelIntegration = integration;
  }

  getChatStream(): UseChatStreamReturn | null {
    return this.chatPanelIntegration.chatStream;
  }

  getFocusChatInput(): (() => void) | null {
    return this.chatPanelIntegration.focusChatInput;
  }

  isRegistered(): boolean {
    return !!(this.chatPanelIntegration.chatStream && this.chatPanelIntegration.focusChatInput);
  }

  clear(): void {
    console.log('[ChatPanelIntegrationManager] Clearing ChatPanel integration');
    this.chatPanelIntegration = {
      chatStream: null,
      focusChatInput: null
    };
  }
}

/**
 * Hook để tích hợp với ChatPanel
 */
export const useChatPanelIntegration = (): UseChatPanelIntegrationReturn => {
  const managerRef = useRef(ChatPanelIntegrationManager.getInstance());

  const registerChatPanel = useCallback((integration: ChatPanelIntegration) => {
    managerRef.current.register(integration);
  }, []);

  const getChatStream = useCallback(() => {
    return managerRef.current.getChatStream();
  }, []);

  const getFocusChatInput = useCallback(() => {
    return managerRef.current.getFocusChatInput();
  }, []);

  const isRegistered = useCallback(() => {
    return managerRef.current.isRegistered();
  }, []);

  return {
    registerChatPanel,
    getChatStream,
    getFocusChatInput,
    isRegistered
  };
};

export default useChatPanelIntegration;
