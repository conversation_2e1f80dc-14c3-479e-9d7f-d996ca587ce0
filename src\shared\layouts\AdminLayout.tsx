import { useState, ReactNode, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';

import { Icon } from '@/shared/components/common';
import { useIsMobile } from '@/shared/hooks/common';
import { useChatPanel } from '@/shared/contexts';
import { clearChatNotifications } from '@/shared/store/slices/chatSlice';
import SidebarMenu from '@/shared/components/layout/sidebar-menu/SidebarMenu';
import ChatButton from '@/shared/components/layout/chat-button/ChatButton';
import ChatPanel from '@/shared/components/layout/chat-panel/ChatPanel';
import { adminMenuItems } from '@/shared/components/layout/chat-panel/menu-items';
import StableViewPanelAdmin from '@/shared/components/layout/StableViewPanelAdmin';
import ResizableChatLayout from '@/shared/components/layout/ResizableChatLayout';

interface AdminLayoutProps {
  children: ReactNode;
  title: string;
  actions?: ReactNode;
}

/**
 * Layout chính cho giao diện Admin
 */
const AdminLayout = ({ children, title, actions }: AdminLayoutProps) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isMobile = useIsMobile();
  const { isChatPanelOpen, setIsChatPanelOpen } = useChatPanel();
  const [isMobileFullscreen, setIsMobileFullscreen] = useState(false);

  // Effect để xử lý hành vi responsive
  useEffect(() => {
    // Đóng chat panel trên mobile theo mặc định
    if (isMobile) {
      setIsChatPanelOpen(false);
    } else {
      // Mở chat panel trên desktop theo mặc định
      setIsChatPanelOpen(true);
    }
  }, [isMobile, setIsChatPanelOpen]);

  // Xử lý đóng chat panel - memoized để tránh re-render
  const handleChatPanelClose = useCallback(() => {
    console.log('[AdminLayout] Closing chat panel and clearing notifications');
    // Xóa tất cả thông báo khi đóng chat panel
    dispatch(clearChatNotifications());
    setIsChatPanelOpen(false);
    setIsMobileFullscreen(false);
  }, [dispatch, setIsChatPanelOpen]);

  // Xử lý mở chat panel - memoized để tránh re-render
  const handleChatPanelOpen = useCallback(() => {
    setIsChatPanelOpen(true);
    if (isMobile) {
      setIsMobileFullscreen(true);
    }
  }, [isMobile, setIsChatPanelOpen]);

  // Xử lý phát hiện từ khóa hoặc điều hướng đường dẫn - memoized để tránh re-render
  const handleKeywordDetected = useCallback((keywordOrPath: string) => {
    console.log('Keyword or path detected:', keywordOrPath);

    // Nếu là đường dẫn (bắt đầu bằng '/')
    if (keywordOrPath.startsWith('/')) {
      navigate(keywordOrPath);
      return;
    }

    // Mapping từ khóa đến đường dẫn
    const keywordMappings: Record<string, string> = {
      admin: '/admin',
      dashboard: '/admin',
      users: '/admin/users',
      employees: '/admin/employees',
      affiliate: '/admin/affiliate',
      rpoint: '/admin/rpoint',
      subscription: '/admin/subscription',
      settings: '/admin/settings',
      media: '/admin/media',
    };

    // Chuyển từ khóa thành chữ thường để so sánh
    const keyword = keywordOrPath.toLowerCase().trim();

    // Kiểm tra từ khóa chính xác
    if (keywordMappings[keyword]) {
      navigate(keywordMappings[keyword]);
      return;
    }

    // Kiểm tra từ khóa một phần
    for (const [key, path] of Object.entries(keywordMappings)) {
      if (keyword.includes(key) || key.includes(keyword)) {
        navigate(path);
        console.log(
          `Partial keyword match: "${keyword}" contains or is contained in "${key}" -> navigating to ${path}`
        );
        return;
      }
    }

    console.log(`No matching keyword found for: "${keyword}"`);
  }, [navigate]);

  // Chuyển đổi adminMenuItems để sử dụng trong SidebarMenu
  const menuItems = adminMenuItems;

  // Sử dụng StableViewPanelAdmin để tối ưu hóa
  const viewPanelElement = (
    <StableViewPanelAdmin title={title} actions={actions}>
      {children}
    </StableViewPanelAdmin>
  );

  // Memoize ChatPanel để tránh re-render không cần thiết
  const memoizedChatPanel = useMemo(
    () => (
      <ChatPanel
        onClose={handleChatPanelClose}
        onKeywordDetected={handleKeywordDetected}
        enableThreadIntegration={true}
        onThreadCreated={(threadId, threadName) => {
          console.log('[AdminLayout] Thread created:', { threadId, threadName });
        }}
        onThreadSwitched={(fromId, toId) => {
          console.log('[AdminLayout] Thread switched:', { fromId, toId });
        }}
        onThreadNameChanged={(threadId, newName) => {
          console.log('[AdminLayout] Thread name changed:', { threadId, newName });
        }}
        onThreadDeleted={(threadId) => {
          console.log('[AdminLayout] Thread deleted:', threadId);
        }}
      />
    ),
    [handleChatPanelClose, handleKeywordDetected]
  );

  // Memoize SidebarMenu items để tránh re-render không cần thiết
  const memoizedSidebarItems = useMemo(
    () =>
      menuItems.map(item => ({
        icon: <Icon name={item.icon} size="lg" />, // Dynamically set icon
        tooltip: item.label, // Tooltip from label
        path: item.path, // Path from menuItems
      })),
    [menuItems]
  );

  return (
    <div className="h-screen w-full max-w-full overflow-hidden">
      {/* Mobile fullscreen chat panel - overlay */}
      {isMobile && isMobileFullscreen && (
        <div className="fixed inset-0 z-50 bg-white dark:bg-dark w-full">
          {memoizedChatPanel}
        </div>
      )}

      {/* Main layout container */}
      <div className="flex h-full w-full max-w-full overflow-hidden">
        {/* Sidebar menu - when chat panel is closed */}
        {!isChatPanelOpen && !isMobile && (
          <div className="h-full">
            <SidebarMenu onOpenChat={handleChatPanelOpen} items={memoizedSidebarItems} />
          </div>
        )}

        {/* Resizable Chat Layout - handles both chat panel and view panel */}
        <div className="flex-1 w-full max-w-full overflow-hidden">
          <ResizableChatLayout
            chatPanel={memoizedChatPanel}
            viewPanel={viewPanelElement}
            isChatPanelOpen={isChatPanelOpen}
            isMobile={isMobile}
          />
        </div>

        {/* Chat button - only visible on mobile when chat panel is closed */}
        {isMobile && !isChatPanelOpen && (
          <ChatButton
            onClick={handleChatPanelOpen}
            position="bottom-right"
            offset={20}
            size={36}
            hoverEffect="scale"
            className="animate-fade-in"
          />
        )}
      </div>
    </div>
  );
};

export default AdminLayout;
