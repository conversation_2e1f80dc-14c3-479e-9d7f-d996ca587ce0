/**
 * Chat Configuration Service
 * Quản lý cấu hình cho chat streaming system
 */

export interface ChatConfig {
  /**
   * Agent ID để sử dụng cho chat
   */
  agentId: string;
  
  /**
   * Base URL cho REST API
   */
  apiBaseUrl: string;
  
  /**
   * Base URL cho SSE
   */
  sseBaseUrl: string;
  
  /**
   * Tự động approve tool calls
   */
  alwaysApproveToolCall: boolean;
  
  /**
   * Bật debug mode
   */
  debug: boolean;
  
  /**
   * Timeout cho API calls (ms)
   */
  apiTimeout: number;
  
  /**
   * Timeout cho SSE connection (ms)
   */
  sseTimeout: number;
  
  /**
   * Function để lấy auth token
   */
  getAuthToken?: () => string | Promise<string>;
}

/**
 * Default configuration
 */
const DEFAULT_CONFIG: ChatConfig = {
  agentId: '',
  apiBaseUrl: import.meta.env['VITE_API_URL'] || 'https://v2.redai.vn/api',
  sseBaseUrl: import.meta.env['VITE_API_URL'] || 'https://v2.redai.vn/api',
  alwaysApproveToolCall: false,
  debug: false,
  apiTimeout: 30000, // 30 seconds
  sseTimeout: 60000, // 60 seconds
};

/**
 * Chat Configuration Service
 * Singleton pattern với localStorage persistence
 */
class ChatConfigService {
  private static instance: ChatConfigService;
  private config: ChatConfig;
  private readonly STORAGE_KEY = 'chat_config';

  private constructor() {
    this.config = this.loadConfig();
  }

  /**
   * Lấy instance singleton
   */
  static getInstance(): ChatConfigService {
    if (!ChatConfigService.instance) {
      ChatConfigService.instance = new ChatConfigService();
    }
    return ChatConfigService.instance;
  }

  /**
   * Load config từ localStorage hoặc sử dụng default
   */
  private loadConfig(): ChatConfig {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        return { ...DEFAULT_CONFIG, ...parsedConfig };
      }
    } catch (error) {
      console.warn('[ChatConfigService] Failed to load config from localStorage:', error);
    }
    return { ...DEFAULT_CONFIG };
  }

  /**
   * Save config vào localStorage
   */
  private saveConfig(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.warn('[ChatConfigService] Failed to save config to localStorage:', error);
    }
  }

  /**
   * Lấy toàn bộ config hiện tại
   */
  getConfig(): ChatConfig {
    return { ...this.config };
  }

  /**
   * Cập nhật toàn bộ config
   */
  updateConfig(newConfig: Partial<ChatConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
  }

  /**
   * Set agent ID
   */
  setAgentId(agentId: string): void {
    this.config.agentId = agentId;
    this.saveConfig();
  }

  /**
   * Set API base URL
   */
  setApiBaseUrl(url: string): void {
    this.config.apiBaseUrl = url;
    this.saveConfig();
  }

  /**
   * Set SSE base URL
   */
  setSseBaseUrl(url: string): void {
    this.config.sseBaseUrl = url;
    this.saveConfig();
  }

  /**
   * Toggle debug mode
   */
  setDebug(debug: boolean): void {
    this.config.debug = debug;
    this.saveConfig();
  }

  /**
   * Reset về default config
   */
  resetConfig(): void {
    this.config = { ...DEFAULT_CONFIG };
    this.saveConfig();
  }

  /**
   * Validate config
   */
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.config.agentId) {
      errors.push('Agent ID is required');
    }

    if (!this.config.apiBaseUrl) {
      errors.push('API Base URL is required');
    }

    if (!this.config.sseBaseUrl) {
      errors.push('SSE Base URL is required');
    }

    if (this.config.apiTimeout < 1000) {
      errors.push('API timeout must be at least 1000ms');
    }

    if (this.config.sseTimeout < 1000) {
      errors.push('SSE timeout must be at least 1000ms');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const chatConfigService = ChatConfigService.getInstance();

