import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
  Transform,
} from 'redux-persist';
import storage from 'redux-persist/lib/storage';

// Import reducers
import authCommonReducer, { AuthCommonState } from './slices/authCommonSlice';
import chatReducer from './slices/chatSlice';
import cartReducer from './slices/cartSlice';
import settingsReducer from '@/modules/settings/store/settingsSlice';
import threadIntegrationReducer from './slices/threadIntegrationSlice';

// Các cấu hình Redux Persist cũ đã được xóa vì không còn sử dụng authSlice và adminAuthSlice

// Cấu hình Redux Persist cho chat
const chatPersistConfig = {
  key: 'chat',
  storage,
  whitelist: [], // Không lưu thông báo giữa các phiên làm việc
};

// Cấu hình Redux Persist cho cart
const cartPersistConfig = {
  key: 'cart',
  storage,
  whitelist: ['items', 'selectedItems', 'soldCounts', 'discount'], // Lưu thông tin giỏ hàng giữa các phiên làm việc
};

// Cấu hình Redux Persist cho settings
const settingsPersistConfig = {
  key: 'settings',
  storage,
  whitelist: ['timezone', 'chatKeywords', 'customKeywords'], // Lưu cài đặt giữa các phiên làm việc
};

// Cấu hình Redux Persist cho authCommon
const authCommonPersistConfig = {
  key: 'authCommon',
  storage,
  whitelist: [
    'authType',
    'accessToken',
    'refreshToken',
    'expiresIn',
    'expiresAt',
    'isAuthenticated',
    'verifyToken',
    'verifyExpiresIn',
    'verifyExpiresAt',
    'verifyInfo',
    'twoFactorVerifyToken',
    'twoFactorExpiresIn',
    'twoFactorExpiresAt',
    'enabledMethods',
  ],
  transforms: [
    {
      in: (inState: AuthCommonState) => {
        return inState;
      },
      out: (outState: AuthCommonState) => {
        return outState;
      },
    } as Transform<AuthCommonState, AuthCommonState>,
  ],
};



// Cấu hình Redux Persist cho root reducer
const rootReducer = combineReducers({
  authCommon: persistReducer(authCommonPersistConfig, authCommonReducer),
  chat: persistReducer(chatPersistConfig, chatReducer),
  cart: persistReducer(cartPersistConfig, cartReducer),
  settings: persistReducer(settingsPersistConfig, settingsReducer),
  threadIntegration: threadIntegrationReducer, // Không persist thread integration state
  // Thêm các reducer khác ở đây
});

// Cấu hình store
export const store = configureStore({
  reducer: rootReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        // Bỏ qua các action của redux-persist
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        // Bỏ qua các field path trong action
        ignoredActionPaths: ['payload.info', 'payload.enabledMethods'],
        // Bỏ qua các path trong state
        ignoredPaths: ['auth.user', 'adminAuth.employee', 'authCommon.user', 'authCommon.employee'],
      },
    }),
});

// Tạo persistor
export const persistor = persistStore(store);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = (): AppDispatch => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
